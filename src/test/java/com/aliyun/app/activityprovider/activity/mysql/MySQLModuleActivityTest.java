package com.aliyun.app.activityprovider.activity.mysql;

import com.aliyun.app.activityprovider.base.backup.BifrostBaseService;
import com.aliyun.app.activityprovider.base.backup.DbsGateWayService;
import com.aliyun.app.activityprovider.base.backup.param.DescribeRestoreBackupSetParam;
import com.aliyun.app.activityprovider.base.backup.response.BinlogBackupPolicyResponse;
import com.aliyun.app.activityprovider.base.backup.response.DescribeRestoreBackupSetResponse;
import com.aliyun.app.activityprovider.base.meta.DbaasMetaClient;
import com.aliyun.app.activityprovider.bizservice.modules.ModulePlan;
import com.aliyun.app.activityprovider.bizservice.mysql.MysqlBizService;
import com.aliyun.app.activityprovider.common.consts.BackupConsts;
import com.aliyun.app.activityprovider.web.RequestSession;
import com.aliyun.app.activityprovider.web.Result;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaListResult;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.TransferTask;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MySQLModuleActivityTest {
    @InjectMocks
    private MySQLModuleActivity mySQLModuleActivity;

    @Mock
    private BifrostBaseService bifrostBaseService;

    @Mock
    private DbsGateWayService dbsGateWayService;

    @Mock
    private MysqlBizService mysqlBizService;

    @Mock
    private DbaasMetaClient dBaasMetaClient;

    @Mock
    private DefaultApi dbaasDefaultApi;

    @Before
    public void setUp() throws Exception {
        RequestSession.init("requestId", "", "", "", "", "");
        Mockito.when(dBaasMetaClient.getDefaultmetaApi()).thenReturn(dbaasDefaultApi);

        TransferTask transferTask = new TransferTask();
        transferTask.setId(1);
        transferTask.setSrcReplicaSetName("srcRepSet");
        transferTask.setDestReplicaSetName("destRepSet");
        transferTask.setSrcClassCode("srcClass");
        transferTask.setDestClassCode("destClass");
        transferTask.setSrcDiskSizeMB(1024);
        transferTask.setDestDiskSizeMB(2048);
        transferTask.setParameter("{\"srcPerformanceLevel\":\"srcPerf\",\"targetPerformanceLevel\":\"targetPerf\",\"srcDiskType\":\"srcDisk\",\"targetDiskType\":\"targetDisk\"," +
                "\"resourceGuaranteeLevel\":\"test\",\"resourceGuaranteeLevelType\":\"force\",\"resourceGuaranteeBackUpLevels\":\"regular,low-cost\"}");


        Mockito.when(dbaasDefaultApi.getTransferTask(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(transferTask);

        Replica masterReplica = new Replica();
        Replica slaveReplica = new Replica();
        masterReplica.setId(1L);
        slaveReplica.setId(2L);

        Mockito.when(mysqlBizService.findMasterReplica(Mockito.any())).thenReturn(masterReplica);
        Mockito.when(mysqlBizService.findSlaveReplica(Mockito.any())).thenReturn(slaveReplica);
    }

    @Test
    public void testBuildHALocalModifyModulesPlan_Success() throws Exception {
        String replicaSetName = "testReplicaSet";
        Long transTaskId = 1L;

        Result<Map<String, ModulePlan>> result = mySQLModuleActivity.buildHALocalModifyModulesPlan(replicaSetName, transTaskId);
        Result<Map<String, ModulePlan>> resultForCluster = mySQLModuleActivity.buildClusterLocalModifyModulesPlan(replicaSetName, transTaskId);

        assertEquals("Success", result.getCode());
        assertEquals("Success", resultForCluster.getCode());

    }
    @Test
    public void testBuildReadInsAppendBinLogModulesPlanFromDBS() throws Exception {
        String srcReplicaSetName = "srcRepSet";
        String destReplicaSetName = "destRepSet";
        String backupSetId = "123";

        BinlogBackupPolicyResponse backupPolicyResponse = new BinlogBackupPolicyResponse();
        Mockito.doReturn(backupPolicyResponse).when(bifrostBaseService).getBinlogBackupPolicy(anyString());

        DescribeRestoreBackupSetParam paramObj = DescribeRestoreBackupSetParam.builder()
                .instanceName(srcReplicaSetName)
                .backupId(backupSetId)
                .build();
        Mockito.doReturn(paramObj).when(dbsGateWayService).describeRestoreBackupSetBuilder(anyString(), anyString());

        DescribeRestoreBackupSetResponse describeRestoreBackupSetResponse = new DescribeRestoreBackupSetResponse();
        DescribeRestoreBackupSetResponse.BackupSetInfo backupSetInfo = new DescribeRestoreBackupSetResponse.BackupSetInfo();
        backupSetInfo.setBackupStatus(BackupConsts.BACKUP_SET_STATUS_OK);
        DescribeRestoreBackupSetResponse.ExtraInfo extraInfo = new DescribeRestoreBackupSetResponse.ExtraInfo();
        extraInfo.setBINLOG_HINSID(123456L);
        extraInfo.setBINLOG_FILE("mysql-bin.000001");
        backupSetInfo.setExtraInfoObj(extraInfo);
        describeRestoreBackupSetResponse.setBackupSetInfoObj(backupSetInfo);

        Mockito.doReturn(describeRestoreBackupSetResponse).when(dbsGateWayService).describeRestoreBackupSet(any());
        Mockito.doReturn(true).when(bifrostBaseService).binlogFromBackupSetHasPurged(anyString(), anyString(), anyLong());

        Replica masterReplica = new Replica();
        masterReplica.setId(123456L);
        Mockito.doReturn(masterReplica).when(mysqlBizService).findMasterReplica(anyString());

        Result<Map<String, ModulePlan>> result = mySQLModuleActivity.buildReadInsAppendBinLogModulesPlanFromDBS(srcReplicaSetName, destReplicaSetName, backupSetId);

        assertNotNull(result);
    }

    @Test
    public void buildMigrateInsNodePlan_EmptySourceReplicas_ThrowsException() throws Exception {
        ReplicaListResult replicaListResult = new ReplicaListResult();
        when(dbaasDefaultApi.listReplicasInReplicaSet(any(), any(), any(), any(), any(), any()))
            .thenReturn(replicaListResult);
        try {
            mySQLModuleActivity.buildMigrateInsNodePlan("srcRepSet", "destRepSet", 1, "{1=2}");
            Assert.fail();
        } catch (Exception e) {
            assertEquals("Not retry RESPONSE ERROR [405] - get srcReplicas failed", e.getMessage());
        }
    }

    @Test
    public void buildMigrateInsNodePlan_EmptyDestReplicas_ThrowsException() throws Exception {
        List<Replica> replicaList = new ArrayList<>();
        Replica replica = new Replica();
        replica.setId(123456L);
        replicaList.add(replica);
        ReplicaListResult sourceReplicaListResult = new ReplicaListResult();
        sourceReplicaListResult.setItems(replicaList);
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setCategory("cluster");
        when(dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet("requestId", "srcRepSet", null, null, null, null))
            .thenReturn(sourceReplicaListResult);
        when(dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet("requestId", "destRepSet", null, null, null, null))
            .thenReturn(new ReplicaListResult());
        try {
            mySQLModuleActivity.buildMigrateInsNodePlan("srcRepSet", "destRepSet", 1, "{1=2}");
            Assert.fail();
        } catch (Exception e) {
            assertEquals("Not retry RESPONSE ERROR [405] - get srcReplicas failed", e.getMessage());
        }
    }

    @Test
    public void buildMigrateInsNodePlan_SourceReplicaIsMaster_ThrowsException() throws Exception {
        List<Replica> replicaList = new ArrayList<>();
        Replica replica = new Replica();
        replica.setId(123456L);
        replica.setRole(Replica.RoleEnum.MASTER);
        replicaList.add(replica);
        ReplicaListResult sourceReplicaListResult = new ReplicaListResult();
        sourceReplicaListResult.setItems(replicaList);
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setCategory("cluster");
        ReplicaListResult destReplicaListResult = new ReplicaListResult();
        destReplicaListResult = sourceReplicaListResult;
        when(dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet("requestId", "srcRepSet", null, null, null, null))
            .thenReturn(sourceReplicaListResult);
        when(dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet("requestId", "destRepSet", null, null, null, null))
            .thenReturn(destReplicaListResult);
        try {
            mySQLModuleActivity.buildMigrateInsNodePlan("srcRepSet", "destRepSet", 1, "{123456=rn-xxxx}");
            Assert.fail();
        } catch (Exception e) {
            assertEquals("Not retry RESPONSE ERROR [405] - srcReplica is master, can not migrate", e.getMessage());
        }
    }


    @Test
    public void buildMigrateInsNodePlan_Success() throws Exception {
        List<Replica> replicaList = new ArrayList<>();
        Replica replica = new Replica();
        replica.setId(123456L);
        replica.setRole(Replica.RoleEnum.MASTER);
        replicaList.add(replica);
        ReplicaListResult sourceReplicaListResult = new ReplicaListResult();
        sourceReplicaListResult.setItems(replicaList);
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setCategory("cluster");
        ReplicaListResult destReplicaListResult = new ReplicaListResult();
        destReplicaListResult = sourceReplicaListResult;
        when(dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet("requestId", "srcRepSet", null, null, null, null))
            .thenReturn(sourceReplicaListResult);
        when(dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet("requestId", "destRepSet", null, null, null, null))
            .thenReturn(destReplicaListResult);

        mySQLModuleActivity.buildMigrateInsNodePlan("srcRepSet", "destRepSet", 1, "{1=2}");
        replica.setRole(Replica.RoleEnum.SLAVE);
        replicaList.add(replica);
        sourceReplicaListResult.setItems(replicaList);
        Result<Map<String, ModulePlan>> result = mySQLModuleActivity.buildMigrateInsNodePlan("srcRepSet", "destRepSet", 1, "{123456=rn-xxxx}");
        assertEquals("Success", result.getCode());
    }

    @Test
    public void buildClusterMigrateDestReplica_EmptyDestReplicaId_ThrowsException() throws Exception {
        try {
            mySQLModuleActivity.buildClusterMigrateDestReplica(123456L, "{1=2}", "{\"key\":\"value\"}");
            Assert.fail();
        } catch (Exception e) {
            assertEquals("this srcReplica not found dest upgrade/replace replica node", e.getMessage());
        }
    }

    @Test
    public void buildClusterMigrateDestReplica_Success() throws Exception {
        String extraParams = "{\"key\":\"value\"}";
        Result<Map<String, Object>> result = mySQLModuleActivity.buildClusterMigrateDestReplica(123456L, "{123456=2}", extraParams);
        assertEquals("Success", result.getCode());
    }

    @Test
    public void test_buildCloneModulesPlan() throws Exception {
        String replicaSetName = "testReplicaSet";
        String sourceReplicaSetName = "sourceReplicaSetName";
        ReplicaListResult replicaListResult = new ReplicaListResult();
        List<Replica> items = new ArrayList<>();
        items.add(new Replica());
        items.get(0).setStorageType(Replica.StorageTypeEnum.LOCAL_SSD);
        replicaListResult.setItems(items);
        when(dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(any(), any(), any(), any(), any(), any())).thenReturn(replicaListResult);
        ReplicaSet replicaSet = new ReplicaSet();
        when(dBaasMetaClient.getDefaultmetaApi().getReplicaSet(any(), any(), any())).thenReturn(replicaSet);
        try {
            Result<Map<String, ModulePlan>> result = mySQLModuleActivity.buildCloneModulesPlan(replicaSetName, sourceReplicaSetName, "1", "123", "123", "2024-01-01", "mysql0001.binlog", "true", "3");
            Assert.fail();
        } catch (Exception e) {
            Assert.assertNotNull(e);
        }
    }

    @Test
    public void testBuildModifyInsNodePlan_NoSlaveNodeReplacementNeeded() throws Exception {
        // Arrange
        String replicaSetName = "testReplicaSet";
        Integer transTaskId = 1;
        String srcPerformanceLevel = "srcPerf";
        String targetPerformanceLevel = "srcPerf"; // 相同性能等级

        // 创建TransferTask
        TransferTask transferTask = new TransferTask();
        transferTask.setId(1);
        transferTask.setSrcReplicaSetName("srcRepSet");
        transferTask.setDestReplicaSetName("destRepSet");
        transferTask.setSrcDiskSizeMB(1024);
        transferTask.setDestDiskSizeMB(2048);
        transferTask.setParameter("{\"transParamList\":[]}"); // 空的transParamList

        when(dBaasMetaClient.getDefaultmetaApi().getTransferTask(anyString(), anyString(), anyInt())).thenReturn(transferTask);

        // 创建空的Replica列表
        ReplicaListResult replicaListResult = new ReplicaListResult();
        replicaListResult.setItems(new ArrayList<>());
        when(dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet("requestId", "srcRepSet", null, null, null, null))
                .thenReturn(replicaListResult);

        // Act
        Result<Map<String, ModulePlan>> result = mySQLModuleActivity.buildModifyInsNodePlan(replicaSetName, transTaskId, srcPerformanceLevel, targetPerformanceLevel);

        // Assert
        assertNotNull(result);
        assertEquals("Success", result.getCode());
        assertTrue(result.getData().containsKey("modify_ins_slave_node"));
        ModulePlan slaveModule = result.getData().get("modify_ins_slave_node");
        assertNotNull(slaveModule);
        assertNull(slaveModule.getParams());
        assertEquals("No need to replace slave node!", slaveModule.getSkipReason());
    }

    @Test
    public void testBuildModifyInsNodePlan_NoMasterNodeReplacementNeeded() throws Exception {
        // Arrange
        String replicaSetName = "testReplicaSet";
        Integer transTaskId = 1;
        String srcPerformanceLevel = "srcPerf";
        String targetPerformanceLevel = "srcPerf"; // 相同性能等级

        // 创建TransferTask
        TransferTask transferTask = new TransferTask();
        transferTask.setId(1);
        transferTask.setSrcReplicaSetName("srcRepSet");
        transferTask.setDestReplicaSetName("destRepSet");
        transferTask.setSrcDiskSizeMB(1024);
        transferTask.setDestDiskSizeMB(2048);
        transferTask.setParameter("{\"transParamList\":[]}"); // 空的transParamList

        when(dBaasMetaClient.getDefaultmetaApi().getTransferTask(anyString(), anyString(), anyInt())).thenReturn(transferTask);

        // 创建空的Replica列表
        ReplicaListResult replicaListResult = new ReplicaListResult();
        replicaListResult.setItems(new ArrayList<>());
        when(dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet("requestId", "srcRepSet", null, null, null, null))
                .thenReturn(replicaListResult);

        // Act
        Result<Map<String, ModulePlan>> result = mySQLModuleActivity.buildModifyInsNodePlan(replicaSetName, transTaskId, srcPerformanceLevel, targetPerformanceLevel);

        // Assert
        assertNotNull(result);
        assertEquals("Success", result.getCode());
        assertTrue(result.getData().containsKey("modify_ins_master_node"));
        ModulePlan masterModule = result.getData().get("modify_ins_master_node");
        assertNotNull(masterModule);
        assertNull(masterModule.getParams());
        assertEquals("No need to replace master node!", masterModule.getSkipReason());
    }

    @Test
    public void testBuildModifyInsNodePlan_OnlyPerformanceLevelDifferent() throws Exception {
        // Arrange
        String replicaSetName = "testReplicaSet";
        Integer transTaskId = 1;
        String srcPerformanceLevel = "srcPerf";
        String targetPerformanceLevel = "targetPerf";

        // 修改磁盘大小为相同值
        TransferTask transferTask = new TransferTask();
        transferTask.setId(1);
        transferTask.setSrcReplicaSetName("srcRepSet");
        transferTask.setDestReplicaSetName("destRepSet");
        transferTask.setSrcDiskSizeMB(1024);
        transferTask.setDestDiskSizeMB(1024); // 相同磁盘大小
        transferTask.setParameter("{\"resourceParam\":[{\"resourceGuaranteeLevel\":\"test\",\"resourceGuaranteeLevelType\":\"force\",\"resourceGuaranteeBackUpLevels\":\"regular,low-cost\"}]}");
        when(dBaasMetaClient.getDefaultmetaApi().getTransferTask(anyString(), anyString(), anyInt())).thenReturn(transferTask);
        ReplicaListResult replicaListResult = new ReplicaListResult();
        replicaListResult.setItems(new ArrayList<>());
        when(dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet("requestId", "srcRepSet", null, null, null, null))
                .thenReturn(replicaListResult);
        // Act
        Result<Map<String, ModulePlan>> result = mySQLModuleActivity.buildModifyInsNodePlan(replicaSetName, transTaskId, srcPerformanceLevel, targetPerformanceLevel);

        // Assert
        assertNotNull(result);
        assertEquals("Success", result.getCode());
        assertTrue(result.getData().containsKey("modify_ins_master_node"));
        ModulePlan resizeModule = result.getData().get("modify_ins_master_node");
        assertNotNull(resizeModule);
        assertFalse(resizeModule.getParams().isEmpty());
        assertFalse(resizeModule.getParams().contains("\"srcDiskSizeMB\""));
        assertFalse(resizeModule.getParams().contains("\"destDiskSizeMB\""));
    }
}
