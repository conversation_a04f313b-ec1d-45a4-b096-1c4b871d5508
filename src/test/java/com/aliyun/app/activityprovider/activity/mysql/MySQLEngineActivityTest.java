package com.aliyun.app.activityprovider.activity.mysql;

import com.aliyun.app.activityprovider.base.actionmanager.ActionCmdManager;
import com.aliyun.app.activityprovider.base.common.CommonProviderClient;
import com.aliyun.app.activityprovider.base.common.CommonProviderService;
import com.aliyun.app.activityprovider.base.maxscale.MaxscaleProviderService;
import com.aliyun.app.activityprovider.base.meta.DbaasMetaClient;
import com.aliyun.app.activityprovider.base.sqlexec.MySQLCommand;
import com.aliyun.app.activityprovider.base.sqlexec.SQLBaseExecutor;
import com.aliyun.app.activityprovider.base.sqlexec.SQLResult;
import com.aliyun.app.activityprovider.base.sqlexec.SQLTemplateSet;
import com.aliyun.app.activityprovider.base.workflow.WorkFlowBaseService;
import com.aliyun.app.activityprovider.bizservice.mysql.*;
import com.aliyun.app.activityprovider.bizservice.rdsreplication.ReplicationBizService;
import com.aliyun.app.activityprovider.common.consts.CommonConsts;
import com.aliyun.app.activityprovider.common.exception.ActivityException;
import com.aliyun.app.activityprovider.meta.ReplicaMetaHelper;
import com.aliyun.app.activityprovider.meta.ReplicaSetMetaHelper;
import com.aliyun.app.activityprovider.web.RequestSession;
import com.aliyun.apsaradb.activityprovider.model.CmdStatusOutput;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.app.activityprovider.web.Result;
import com.aliyun.app.activityprovider.common.exception.NotRetryException;
import lombok.val;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RequestSession.class, DbaasMetaClient.class, MysqlServiceFactory.class})
public class MySQLEngineActivityTest {

    @InjectMocks
    @Spy
    private MySQLEngineActivity mySQLEngineActivity = new MySQLEngineActivity();

    @Mock
    private DbaasMetaClient dBaasMetaClient;

    @Mock
    private DbaasMetaClient dbaasMetaClient;

    @Mock
    private CommonProviderClient commonProviderClient;

    @Mock
    private MysqlServiceFactory mysqlServiceFactory;

    @Mock
    private ReplicaSetMetaHelper replicaSetMetaHelper;

    @Mock
    private SQLBaseExecutor sqlBaseExecutor;

    @Mock
    private ActionCmdManager actionCmdManager;
    @Mock
    private MysqlMigrateBizService mysqlMigrateBizService;
    @Mock
    private MysqlBizService mysqlBizService;
    @Mock
    private MaxscaleProviderService maxscaleProviderService;
    @Mock
    private WorkFlowBaseService workFlowBaseService;

    private DefaultApi dbaasDefaultApi;

    private com.aliyun.apsaradb.activityprovider.api.DefaultApi commonProviderDefaultApi;

    @Mock
    private ReplicationBizService replicationBizService;
    @Mock
    private ReplicaMetaHelper replicaMetaHelper;

    @Before
    public void setUp() throws Exception {
        RequestSession.init(UUID.randomUUID().toString(), "", "", "", "", "");
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(RequestSession.class);
        this.dbaasDefaultApi = Mockito.mock(DefaultApi.class);
        this.commonProviderDefaultApi = Mockito.mock(com.aliyun.apsaradb.activityprovider.api.DefaultApi.class);
        Mockito.when(dBaasMetaClient.getDefaultmetaApi())
                .thenReturn(dbaasDefaultApi);
        Mockito.when(dbaasMetaClient.getDefaultmetaApi())
                .thenReturn(dbaasDefaultApi);
        Mockito.when(commonProviderClient.getDefaultApi()).thenReturn(commonProviderDefaultApi);
        when(RequestSession.getRequestId()).thenReturn("requestId");
        when(workFlowBaseService.getValueFromContext(anyString(), anyInt())).thenReturn(42);
    }

    @Test
    public void checkNodesHealthAndDelay_Success_ReturnsTrue() throws Exception {
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setName("replicaSetName");
        replicaSet.setCategory("basic");
        when(dBaasMetaClient.getDefaultmetaApi().getReplicaSet(any(), any(), anyBoolean())).thenReturn(replicaSet);

        Replica replica = new Replica();
        replica.setId(1L);
        replica.setRole(Replica.RoleEnum.MASTER);
        ReplicaListResult replicaListResult = new ReplicaListResult();
        replicaListResult.setItems(Collections.singletonList(replica));
        when(dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(any(), any(), any(), any(), any(), any())).thenReturn(replicaListResult);

        MysqlServiceDecorator mysqlService = mock(MysqlServiceDecorator.class);
        when(mysqlServiceFactory.get(any(), any())).thenReturn(mysqlService);
        when(mysqlServiceFactory.get((ReplicaSet) any())).thenReturn(mysqlService);
        doNothing().when(mysqlService).checkSlaveHealthAndDelay(any(), anyLong(), any(), anyInt(), anyLong());
        Result<Boolean> result=new Result<>();
        result.setData(true);
        Result<Boolean> res = mySQLEngineActivity.checkNodesHealthAndDelay("replicaSetName", 5, 1L, 0L, null, null);
        assertEquals(true, res.getData());
    }

    @Test
    public void dropTmpInsForBatchIns_Success_Test() throws Exception{
        ReplicaSetListResult result=new ReplicaSetListResult();
        result.setItems(new ArrayList<>());
        ReplicaSet replicaSet=new ReplicaSet();
        replicaSet.setName("test");
        result.getItems().add(replicaSet);
        when(dbaasMetaClient.getDefaultmetaApi().listReplicaSetSubIns(any(),any(),any())).thenReturn(result);
        when(mySQLEngineActivity.startDropTmpIns(Collections.singletonList("test"),null,null)).thenReturn(null);
        Result<String> res = mySQLEngineActivity.dropTmpInsForBatchIns("testReplicaSetName");
        assertEquals(null, res.getData());
    }


    @Test
    public void resetSlave_ExecCmdTrue_Failed() throws Exception {
        Long replicaId = 1L;
        ReplicaResource replicaResource = new ReplicaResource();
        replicaResource.setReplicaSetName("replicaSetName");
        when(dbaasDefaultApi.getReplica(anyString(), anyLong(), anyBoolean())).thenReturn(replicaResource);
        ReplicaSetResource replicaSetResource = new ReplicaSetResource();
        replicaSetResource.setReplicaSet(new ReplicaSet());
        when(dbaasDefaultApi.getReplicaSetBundleResource(anyString(), anyString())).thenReturn(replicaSetResource);
        CmdStatusOutput cmdStatusOutput = new CmdStatusOutput();
        cmdStatusOutput.setExitedCode(1);
        cmdStatusOutput.setDetailLog("reset slave failed");
        when(actionCmdManager.runCmdInReplicaUsingSQL(anyString(), any(), anyString())).thenReturn(cmdStatusOutput);
        try {
            mySQLEngineActivity.resetSlave(replicaId, true, true);
            fail("Expected ActivityException to be thrown");
        } catch (ActivityException e) {
            assertEquals("reset slave failed", e.getMessage());
        }
    }
    @Test
    public void checkSlaveDiskUsageHealth_tmp_Test() throws Exception {
        String replicaSetName = "replicaSetName";
        Long replicaId = 123L;
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setName("replicaSetName");
        replicaSet.setCategory("basic");
        MysqlServiceDecorator mysqlService = mock(MysqlServiceDecorator.class);
        when(dBaasMetaClient.getDefaultmetaApi().getReplicaSet(any(), any(), anyBoolean())).thenReturn(replicaSet);
        when(mysqlServiceFactory.get(replicaSet)).thenReturn(mysqlService);
        doNothing().when(mysqlService).checkSlaveDiskUsageHealth(replicaSet,replicaId,null);
        Result<Boolean> result = mySQLEngineActivity.checkSlaveDiskHealth(replicaSetName, replicaId, null, null);
        assertEquals(true,result.getData());
    }

    @Test
    public void checkSlaveDiskUsageHealth_success_Test() throws Exception {
        String replicaSetName = "replicaSetName";
        Long replicaId = 123L;
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setName("replicaSetName");
        replicaSet.setCategory("basic");
        replicaSet.setInsType(ReplicaSet.InsTypeEnum.TMP);
        replicaSet.setPrimaryInsName("pri");
        MysqlServiceDecorator mysqlService = mock(MysqlServiceDecorator.class);
        when(replicaSetMetaHelper.isTmpIns(replicaSet)).thenReturn(true);
        when(dBaasMetaClient.getDefaultmetaApi().getReplicaSet(any(), any(), anyBoolean())).thenReturn(replicaSet);
        when(mysqlServiceFactory.get(replicaSet)).thenReturn(mysqlService);
        doNothing().when(mysqlService).checkSlaveDiskUsageHealth(replicaSet,replicaId,null);
        Result<Boolean> result = mySQLEngineActivity.checkSlaveDiskHealth(replicaSetName, replicaId, null, null);
        assertEquals(true,result.getData());
    }
    @Test
    public void replaceEndPointRealServerForEndpoints_success_Test() throws Exception {
        String replicaMapping = "{\"123\"=\"123\"}";
        String replicaSetName = "replicaSetName";
        ReplicaSet replicaSet=new ReplicaSet();
        replicaSet.setName("replicaSet");
        List<Replica> replicas=new ArrayList<>();
        Replica replica=new Replica();
        replica.setId(123L);
        replica.setName("123");
        replica.setZoneId("cn-beijing");
        replica.setClassCode("mysql-x5");
        replica.setRole(Replica.RoleEnum.MASTER);
        replicas.add(replica);
        ReplicaListResult replicaListResult=new ReplicaListResult();
        replicaListResult.setItems(replicas);
        MysqlServiceDecorator mysqlService = mock(MysqlServiceDecorator.class);
        when(dbaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(any(), any(), any(), any(), any(), any())).thenReturn(replicaListResult);
        List<Endpoint> endpointList=new ArrayList<>();
        Endpoint endpoint=new Endpoint();
        endpoint.setId(1L);
        endpointList.add(endpoint);
        EndpointListResult endpointResult=new EndpointListResult();
        endpointResult.setItems(endpointList);
        when(dBaasMetaClient.getDefaultmetaApi().listReplicaSetEndpoints(any(), any(), any(), any(), any(), any())).thenReturn(endpointResult);
        EndpointGroupListResult endpointGroupListResult=new EndpointGroupListResult();
        List<EndpointGroup> endpointGroupList=new ArrayList<>();
        EndpointGroup endpointGroupNode=new EndpointGroup();
        endpointGroupNode.setType("node");
        endpointGroupNode.setStatus(1);
        EndpointGroup endpointGroupRO=new EndpointGroup();
        endpointGroupRO.setType("readonly");
        endpointGroupRO.setStatus(1);
        endpointGroupList.add(endpointGroupNode);
        endpointGroupList.add(endpointGroupRO);
        endpointGroupListResult.setItems(endpointGroupList);
        when(dbaasMetaClient.getDefaultmetaApi().listEndpointGroups(any(),any())).thenReturn(endpointGroupListResult);
        EndpointGroup groupResult=new EndpointGroup();
        Map<String,String> labels=new HashMap<>();
        labels.put("Node","123");
        labels.put("NodeWeights","{\"nodeWeights\":[{\"dBInstanceId\":\"rm-2ze2hfi87s9tjbh4h\",\"maxReplicationLag\":0,\"nodeId\":\"123\",\"status\":\"ONLINE\",\"weight\":10}]}");
        groupResult.setLabels(labels);
        when(dbaasMetaClient.getDefaultmetaApi().getEndpointGroup(any(),any(),any(),any())).thenReturn(groupResult);
        when(commonProviderClient.getDefaultApi().replaceEndPointRealServer(any(),any(),any(),any())).thenReturn(null);
        when(replicaSetMetaHelper.isTmpIns(replicaSet)).thenReturn(true);
        when(dBaasMetaClient.getDefaultmetaApi().getReplicaSet(any(), any(), anyBoolean())).thenReturn(replicaSet);
        when(mysqlServiceFactory.get(replicaSet)).thenReturn(mysqlService);
        Result<String> stringResult = mySQLEngineActivity.replaceEndPointRealServerForEndpoints(replicaSetName, replicaMapping);
        assertEquals(RequestSession.getRequestId(),stringResult.getData());
    }


    @Test
    public void startSlave_ExecCmdTrue_Success() throws Exception {
        Long replicaId = 1L;
        ReplicaResource replicaResource = new ReplicaResource();
        replicaResource.setReplicaSetName("replicaSetName");
        when(dbaasDefaultApi.getReplica(anyString(), anyLong(), anyBoolean())).thenReturn(replicaResource);

        CmdStatusOutput cmdStatusOutput = new CmdStatusOutput();
        cmdStatusOutput.setExitedCode(0);
        when(actionCmdManager.runCmdInReplicaUsingSQL(anyString(), any(), anyString())).thenReturn(cmdStatusOutput);

        Result<Boolean> result = mySQLEngineActivity.startSlave(replicaId, true);
        assertEquals(true, result.getData());
    }


    @Test
    public void startSlave_ExecCmdTrue_Failed() throws Exception {
        Long replicaId = 1L;
        ReplicaResource replicaResource = new ReplicaResource();
        replicaResource.setReplicaSetName("replicaSetName");
        when(dbaasDefaultApi.getReplica(anyString(), anyLong(), anyBoolean())).thenReturn(replicaResource);

        CmdStatusOutput cmdStatusOutput = new CmdStatusOutput();
        cmdStatusOutput.setExitedCode(1);
        cmdStatusOutput.setDetailLog("start slave failed");
        when(actionCmdManager.runCmdInReplicaUsingSQL(anyString(), any(), anyString())).thenReturn(cmdStatusOutput);

        try {
            mySQLEngineActivity.startSlave(replicaId, true);
            fail("Expected ActivityException to be thrown");
        } catch (ActivityException e) {
            assertEquals("start slave failed", e.getMessage());
        }
    }


    @Test
    public void startSlave_ExecCmdFalse_Success() throws Exception {
        Long replicaId = 1L;
        ReplicaResource replicaResource = new ReplicaResource();
        replicaResource.setReplicaSetName("replicaSetName");
        when(dbaasDefaultApi.getReplica(anyString(), anyLong(), anyBoolean())).thenReturn(replicaResource);

        SQLResult sqlResult = new SQLResult();
        sqlResult.setSuccess(true);
        when(sqlBaseExecutor.execSQLInReplica(anyLong(), any(MySQLCommand.class))).thenReturn(sqlResult);

        Result<Boolean> result = mySQLEngineActivity.startSlave(replicaId, false);
        assertEquals(true, result.getData());
    }

    @Test
    public void deleteInstanceServiceForReplicaset_ValidRequest_PodStopped() throws Exception {
        try {
            String replicaSetName = "testReplicaSet";
            ReplicaSet replicaSet = new ReplicaSet();
            replicaSet.setInsType(ReplicaSet.InsTypeEnum.TMP);
            Mockito.doReturn(replicaSet).when(dbaasDefaultApi).getReplicaSet(anyString(), anyString(), any());

            Replica replica = new Replica();
            replica.setId(1L);
            ReplicaListResult replicaListResult = new ReplicaListResult();
            replicaListResult.setItems(Collections.singletonList(replica));
            when(dbaasDefaultApi.listReplicasInReplicaSet(anyString(), anyString(), any(), any(), any(), any())).thenReturn(replicaListResult);

            ReplicaResource replicaResource = new ReplicaResource();
            when(dbaasDefaultApi.getReplica(anyString(), anyLong(), any())).thenReturn(replicaResource);
            PowerMockito.when(RequestSession.getRequestId()).thenReturn("requestId");
            Mockito.doReturn(CommonConsts.POD_STOPPED).when(commonProviderDefaultApi).getReplicaStatus(anyString(), anyString(), anyLong());

            Result<Boolean> res = mySQLEngineActivity.deleteInstanceServiceForReplicaset(replicaSetName, null, null);
            System.out.println(res);
            assertNotNull(res);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testRestartMysqldByKill_AliYun_Success() throws Exception {
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
        ReplicaResource replicaResource = new ReplicaResource();
        Replica replica = new Replica();
        replica.setRole(Replica.RoleEnum.SLAVE);
        replicaResource.setReplica(replica);
        when(dbaasMetaClient.getDefaultmetaApi().getReplicaSet(anyString(), anyString(), anyBoolean())).thenReturn(replicaSet);
        when(dbaasMetaClient.getDefaultmetaApi().getReplica(anyString(), anyLong(), anyBoolean())).thenReturn(replicaResource);
        mySQLEngineActivity.restartMysqldByKill("replicaSetName", 1L, "15", true, true);

        replica.setRole(Replica.RoleEnum.MASTER);
        replicaResource.setReplica(replica);
        mySQLEngineActivity.restartMysqldByKill("replicaSetName", 1L, "15", true, true);

        Result result = mySQLEngineActivity.restartMysqldByKill("replicaSetName", 1L, "15", true, false);
        assertNotNull(result);
    }

    @Test
    public void testResetSlaveForExternalReplication() throws Exception {
        when(replicaSetMetaHelper.isReplicaSetExternalReplication(anyString())).thenReturn(true);
        DefaultApi defaultApi = Mockito.mock(DefaultApi.class);
        when(dBaasMetaClient.getDefaultmetaApi()).thenReturn(defaultApi);
        when(defaultApi.getReplica(anyString(), anyLong(), anyBoolean())).thenReturn(new ReplicaResource(){{
            setReplicaSetName("rm-xxx");
        }});
        when(sqlBaseExecutor.execSQLInReplica(anyLong(), any(MySQLCommand.class))).thenReturn(new SQLResult(){{
            setSuccess(true);
        }});
        when(sqlBaseExecutor.execQueryInReplica(anyLong(), any(MySQLCommand.class))).thenReturn(new SQLResult(){{
            setSuccess(true);
            setResult("[{\"key\":\"result\"}]");
        }});
        Result<Boolean> res = mySQLEngineActivity.resetSlave(1L, true, false);
        assertNotNull(res);
    }

    @Test
    public void test_migrateK8SFormatDir() throws Exception {
        Result<String> stringResult = mySQLEngineActivity.migrateK8SFormatDir(1L, "rm-xxx", 1L, 1);
        Assert.assertNotNull(stringResult);
    }

    @Test
    public void test_buildSyncUpgradeReadInsPlan() throws Exception {
        ReplicaSet replicaSet = new ReplicaSet();
        when(dBaasMetaClient.getDefaultmetaApi().getReplicaSet(any(), any(), any())).thenReturn(replicaSet);
        ReplicaSetListResult replicaSetListResult = new ReplicaSetListResult();
        replicaSetListResult.setItems(new ArrayList<>());
        ReplicaSet read1 = new ReplicaSet();
        replicaSetListResult.getItems().add(read1);
        Map<String, String> labels = new HashMap<>();
        labels.put("minor_version", "mysql57_20241231");
        when(dbaasMetaClient.getDefaultmetaApi()
                .listReplicaSetLabels(any(), any())).thenReturn(labels);
        when(dBaasMetaClient.getDefaultmetaApi().listReplicaSetSubIns(any(), any(), any())).thenReturn(replicaSetListResult);
        try {
            Result<Object> objectResult = mySQLEngineActivity.buildSyncUpgradeReadInsPlan("rm-xxx", "20241231", "mysql57_xc_20241231", "1", null, null);
            Assert.fail();
        } catch (Exception e) {
            Assert.assertNotNull(e);
        }
    }

    @Test
    public void switch_rs() throws Exception {
        when(mysqlBizService.findMasterReplica(anyString())).thenReturn(new Replica(){{setId(1L);}});
        doReturn(null).when(mySQLEngineActivity).replaceEndpointRsForBasic(anyLong(), anyLong(), anyBoolean());
        doReturn(null).when(mySQLEngineActivity).switchProxyRsToTmpIns(anyString(), anyString(), anyString());
        doReturn(null).when(mySQLEngineActivity).switchProxyRsToTmpIns(anyString(), anyString(), anyString());
        when(maxscaleProviderService.getMaxScaleName(anyString())).thenReturn("proxyReplicaSetName");
        when(dbaasDefaultApi.getReplicaSet(anyString(), anyString(), anyBoolean())).thenReturn(new ReplicaSet(){{setPrimaryInsName("replicaSetName");}});
        mySQLEngineActivity.switch_rs("replicaSetName", "", "destReplicaSetName", "toProxy");
        mySQLEngineActivity.switch_rs("replicaSetName", "proxyReplicaSetName", "destReplicaSetName", "toProxy");
        mySQLEngineActivity.switch_rs("replicaSetName", "proxyReplicaSetName", "destReplicaSetName", "toTmpIns");
        try {
            mySQLEngineActivity.switch_rs("replicaSetName", "proxyReplicaSetName", "destReplicaSetName", "");
            fail();
        } catch (Exception e) {
            assertNotNull(e);
        }
    }
}