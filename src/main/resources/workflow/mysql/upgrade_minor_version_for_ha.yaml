---
domain: mysql
name: upgrade_minor_version_for_ha
version: 28
timeout: 600
priority: 10

outer_info:
  cn_name: 升级实例内核小版本
  group: KernelVersionUpgrade

activities:
  outer_stage_name_prepare_params:
    name: prepare_params
  init_extra_param:
    invoke:
      url: http://%(ACTIVITY_PROVIDER_URL)/api/v1/common/init_extra_param
      arg-template: >-
        {
          "workflowId": "{{workflowId}}",
          "requestId": "{{requestId}}",
          "targetId": "{{targetId}}",
          "targetType": "{{targetType}}",
          "replicaSetName": "{{targetId}}",
          "extraParams": "{{extraParams}}",
        }

  role_replica_mapping:
    invoke:
      url: http://%(ACTIVITY_PROVIDER_URL)/api/v1/meta/role_replica_mapping
      arg-template: >-
        {
          "workflowId": "{{workflowId}}",
          "requestId": "{{requestId}}",
          "targetId": "{{targetId}}",
          "targetType": "{{targetType}}",
          "replicaSetName": "{{targetId}}",
        }

  outer_stage_name_do_upgrade:
    name: do_upgrade
  async_upgrade_readins_workflows:
    invoke:
      timeout: 3600
      url: http://%(ACTIVITY_PROVIDER_URL)/api/v1/mysql/upgrade_readins
      arg-template: >-
        {
          "workflowId": "{{workflowId}}",
          "requestId": "{{requestId}}",
          "replicaSetName": "{{targetId}}",
          "switchTimeMode": "{{init_extra_param.data.switch_time_mode}}",
          "switchTime": "{{init_extra_param.data.switch_time}}",
          "targetMinorVersion": "{{init_extra_param.data.minor_version}}",
          "releaseDate": "{{init_extra_param.data.releaseDate}}",
          "readInsTargetMinorVersion": "{{init_extra_param.data.readInsTargetMinorVersion}}"
        }

  bind_replica_set_together_with_service_spec:
    invoke:
      url: http://%(ACTIVITY_PROVIDER_URL)/api/v1/minor_version/bind_replica_set_together_with_service_spec
      arg-template: >-
        {
          "workflowId": "{{workflowId}}",
          "requestId": "{{requestId}}",
          "targetId": "{{targetId}}",
          "targetType": "{{targetType}}",
          "replicaSetName": "{{targetId}}",
          "serviceSpecId": "{{init_extra_param.data.serviceSpecId}}",
        }

  rebuild_node_for_slave:
    invoke:
      url: http://%(ACTIVITY_PROVIDER_URL)/api/v1/sub_tasks/create_subtask_for_module
      arg-template: >-
        {
          "workflowId": "{{workflowId}}",
          "requestId": "{{requestId}}",
          "targetType": "replica",
          "replicaSetName": "{{targetId}}",
          "replicaIds": "{{role_replica_mapping.data.SLAVE.id}}",
          "activityName": "{{activityName}}",
          "parentTaskId": "{{taskId}}",
          "domain": "mysql",
          "name": "rebuild_mysql_node"
        }


  wait_subtask_rebuild_node_for_slave:
    invoke:
      url: http://%(ACTIVITY_PROVIDER_URL)/api/v1/sub_tasks/wait_all_sub_tasks_finish
      timeout: 7200
      arg-template: >-
        {
          "workflowId": "{{workflowId}}",
          "requestId": "{{requestId}}",
          "targetId": "{{targetId}}",
          "targetType": "{{targetType}}",
          "waitWorkflowId": "{{workflowId}}"
        }

  check_slave_no_delay:
    invoke:
      url: http://%(ACTIVITY_PROVIDER_URL)/api/v1/mysql/check_slave_health_and_delay
      timeout: 1800
      arg-template: >-
        {
          "workflowId": "{{workflowId}}",
          "requestId": "{{requestId}}",
          "targetId": "{{targetId}}",
          "targetType": "{{targetType}}",
          "replicaSetName": "{{targetId}}",
        }

  outer_stage_name_pause_for_switch_time:
    name: pause_for_switch_time
    action_info:
      Waiting:
        - modifySwitchTime

  do_pause_activity:
    invoke:
      url: http://%(ACTIVITY_PROVIDER_URL)/api/v1/workflow/pause
      arg-template: >-
        {
          "requestId": "{{requestId}}",
          "workflowId": "{{workflowId}}",
          "taskId": "{{taskId}}",
          "targetId": "{{targetId}}",
          "targetType": "{{targetType}}",
          "extraParams": "{{extraParams}}",
        }

  outer_stage_name_do_ha:
    name: do_ha
  # 切换时间后，再进行等待，避免在步骤内等待只读实例到切换时间
  # 副作用：如果只读实例升级失败，可能会错过当次切换窗口
  wait_upgrade_readonly_workflows:
    invoke:
      url: http://%(ACTIVITY_PROVIDER_URL)/api/v1/workflow/wait_tasks_finish
      timeout: 3600
      retry_interval: 30
      arg-template: >-
        {
          "workflowId": "{{workflowId}}",
          "requestId": "{{requestId}}",
          "targetId": "{{targetId}}",
          "targetType": "{{targetType}}",
          "taskIds": "{{async_upgrade_readins_workflows.data}}",
          "SKIP_THIS_STEP": "{{async_upgrade_readins_workflows.data == null}}"
        }

  get_primary_replica_set_lock:
    invoke:
      url: http://%(ACTIVITY_PROVIDER_URL)/api/v1/meta/get_replica_set_lock
      timeout: 3600
      arg-template: >-
        {
          "workflowId": "{{workflowId}}",
          "requestId": "{{requestId}}",
          "replicaSetName": "{{init_extra_param.data.primaryReplicaSetName}}",
          "SKIP_THIS_STEP": "{{!init_extra_param.data.primaryReplicaSetName || init_extra_param.data.primaryReplicaSetName == targetId}}",
          "key": "{{targetId}}"
        }

  check_slave_health_and_delay:
    invoke:
      url: http://%(ACTIVITY_PROVIDER_URL)/api/v1/mysql/check_slave_health_and_delay
      timeout: 1800
      arg-template: >-
        {
          "workflowId": "{{workflowId}}",
          "requestId": "{{requestId}}",
          "targetId": "{{targetId}}",
          "targetType": "{{targetType}}",
          "replicaSetName": "{{targetId}}",
        }

  do_aurora_switch:
    invoke:
      url: http://%(ACTIVITY_PROVIDER_URL)/api/v1/services/do_aurora_switch
      arg-template: >-
        {
          "workflowId": "{{workflowId}}",
          "requestId": "{{requestId}}",
          "targetId": "{{targetId}}",
          "targetType": "{{targetType}}",
          "replicaSetName": "{{targetId}}",
          "srcReplicaId": "{{srcReplicaId}}",
          "expectedReplicaId": "{{role_replica_mapping.data.MASTER.id}}"
        }

  get_aurora_switch_status:
    invoke:
      url: http://%(ACTIVITY_PROVIDER_URL)/api/v1/services/get_aurora_switch_status
      timeout: 300
      arg-template: >-
        {
          "workflowId": "{{workflowId}}",
          "requestId": "{{requestId}}",
          "targetId": "{{targetId}}",
          "targetType": "{{targetType}}",
          "replicaSetName": "{{targetId}}",
          "requestUuid": "{{do_aurora_switch.data}}",
        }


  rebuild_node_for_master:
    invoke:
      url: http://%(ACTIVITY_PROVIDER_URL)/api/v1/sub_tasks/create_subtask_for_module
      arg-template: >-
        {
          "workflowId": "{{workflowId}}",
          "requestId": "{{requestId}}",
          "targetType": "replica",
          "replicaSetName": "{{targetId}}",
          "replicaIds": "{{role_replica_mapping.data.MASTER.id}}",
          "activityName": "{{activityName}}",
          "parentTaskId": "{{taskId}}",
          "domain": "mysql",
          "name": "rebuild_mysql_node"
        }

  wait_subtask_rebuild_node_for_master:
    invoke:
      url: http://%(ACTIVITY_PROVIDER_URL)/api/v1/sub_tasks/wait_all_sub_tasks_finish
      timeout: 7200
      arg-template: >-
        {
          "workflowId": "{{workflowId}}",
          "requestId": "{{requestId}}",
          "targetId": "{{targetId}}",
          "targetType": "{{targetType}}",
          "waitWorkflowId": "{{workflowId}}"
        }

  outer_stage_name_check_service_config:
    name: check_service_config
  check_minor_version_release_date:
    invoke:
      url: http://%(ACTIVITY_PROVIDER_URL)/api/v1/minor_version/check_minor_version_release_date
      arg-template: >-
        {
          "workflowId": "{{workflowId}}",
          "requestId": "{{requestId}}",
          "targetId": "{{targetId}}",
          "targetType": "{{targetType}}",
          "replicaSetName": "{{targetId}}",
          "expectedReleaseDate": "{{init_extra_param.data.releaseDate}}"
        }

  update_minor_version_param:
    invoke:
      url: http://%(ACTIVITY_PROVIDER_URL)/api/v1/minor_version/update_minor_version_param
      arg-template: >-
        {
          "workflowId": "{{workflowId}}",
          "requestId": "{{requestId}}",
          "targetId": "{{targetId}}",
          "targetType": "{{targetType}}",
          "replicaSetName": "{{targetId}}",
          "releaseDate":"{{init_extra_param.data.releaseDate}}",
          "serviceSpecTag":"{{init_extra_param.data.serviceSpecTag}}",
        }

  restore_upload_binlog:
    invoke:
      timeout: 600
      retry_interval: 10
      retries: 10
      url: http://%(ACTIVITY_PROVIDER_URL)/api/v1/services/restore_upload_binlog
      arg-template: >-
        {
          "workflowId": "{{workflowId}}",
          "requestId": "{{requestId}}",
          "targetId": "{{targetId}}",
          "targetType": "{{targetType}}",
          "replicaSetName": "{{targetId}}"
        }

  check_link_health:
    invoke:
      url: http://%(ACTIVITY_PROVIDER_URL)/api/v1/network/check_link_health
      arg-template: >-
        {
          "workflowId": "{{workflowId}}",
          "requestId": "{{requestId}}",
          "targetId": "{{targetId}}",
          "targetType": "{{targetType}}",
          "replicaSetName": "{{targetId}}"
        }

  outer_stage_name_active_ins:
    name: active_ins
  active_ins:
    invoke:
      url: http://%(ACTIVITY_PROVIDER_URL)/api/v1/meta/active_ins
      arg-template: >-
        {
          "workflowId": "{{workflowId}}",
          "requestId": "{{requestId}}",
          "targetId": "{{targetId}}",
          "targetType": "{{targetType}}",
          "replicaSetName": "{{targetId}}",
        }

  release_primary_replica_set_lock:
    invoke:
      url: http://%(ACTIVITY_PROVIDER_URL)/api/v1/meta/release_replica_set_lock
      arg-template: >-
        {
          "workflowId": "{{workflowId}}",
          "requestId": "{{requestId}}",
          "replicaSetName": "{{init_extra_param.data.primaryReplicaSetName}}",
          "SKIP_THIS_STEP": "{{!init_extra_param.data.primaryReplicaSetName || init_extra_param.data.primaryReplicaSetName == targetId}}",
          "key": "{{targetId}}"
        }


  do_nothing:
    invoke:
      url: http://%(ACTIVITY_PROVIDER_URL)/api/v1/common/do_nothing
      arg-template: >-
        {
          "workflowId": "{{workflowId}}",
          "requestId": "{{requestId}}",
        }