package com.aliyun.app.activityprovider.activity.mysql;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.app.activityprovider.base.backup.BackupBaseService;
import com.aliyun.app.activityprovider.base.backup.BifrostBaseService;
import com.aliyun.app.activityprovider.base.backup.DbsGateWayService;
import com.aliyun.app.activityprovider.base.backup.param.DescribeRestoreBackupSetParam;
import com.aliyun.app.activityprovider.base.backup.response.*;
import com.aliyun.app.activityprovider.base.meta.DbaasMetaClient;
import com.aliyun.app.activityprovider.base.minorversion.MinorVersionService;
import com.aliyun.app.activityprovider.base.param.builder.ReplicaParamBuilder;
import com.aliyun.app.activityprovider.base.param.component.ParamDependency;
import com.aliyun.app.activityprovider.bizservice.modules.*;
import com.aliyun.app.activityprovider.bizservice.mysql.MysqlBizService;
import com.aliyun.app.activityprovider.bizservice.mysql.MysqlMigrateBizService;
import com.aliyun.app.activityprovider.common.annotation.RetryAnnotation;
import com.aliyun.app.activityprovider.common.consts.BackupConsts;
import com.aliyun.app.activityprovider.common.consts.ResourceKeyConsts;
import com.aliyun.app.activityprovider.common.exception.ActivityException;
import com.aliyun.app.activityprovider.common.exception.CheckException;
import com.aliyun.app.activityprovider.common.exception.NotRetryException;
import com.aliyun.app.activityprovider.common.utils.DateTimeUtils;
import com.aliyun.app.activityprovider.common.utils.LangUtil;
import com.aliyun.app.activityprovider.common.utils.StringUtil;
import com.aliyun.app.activityprovider.meta.ReplicaMetaHelper;
import com.aliyun.app.activityprovider.meta.ReplicaSetMetaHelper;
import com.aliyun.app.activityprovider.meta.TransInfo;
import com.aliyun.app.activityprovider.web.RequestSession;
import com.aliyun.app.activityprovider.web.Result;
import com.aliyun.apsaradb.activityprovider.model.TransParam;
import com.aliyun.apsaradb.activityprovider.model.TransParams;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.aliyun.app.activityprovider.base.param.utils.ParamConst.DYNAMIC_PARAM;
import static com.aliyun.app.activityprovider.base.workflow.WorkFlowConsts.*;
import static com.aliyun.app.activityprovider.common.consts.CommonConsts.*;
import static com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet.InsTypeEnum.READONLY;

/**
 * <AUTHOR>
 */
@Api(tags = "MySQL构建模块计划相关Activity")
@RestController
@RequestMapping("/api/v1/mysql/module")
@Slf4j
public class MySQLModuleActivity {

    @Resource
    private DbaasMetaClient dBaasMetaClient;
    @Resource
    private ReplicaSetMetaHelper replicaSetMetaHelper;
    @Resource
    private MysqlBizService mysqlBizService;
    @Resource
    private BackupBaseService backupBaseService;
    @Resource
    private MysqlMigrateBizService mysqlMigrateBizService;
    @Resource
    private ReplicaMetaHelper replicaMetaHelper;
    @Resource
    private BifrostBaseService bifrostBaseService;
    @Resource
    private ParamDependency paramDependency;
    @Resource
    private TaskParamHelper taskParamHelper;
    @Resource
    private MinorVersionService minorVersionService;
    @Resource
    private DbsGateWayService dbsGateWayService;


    /**
     * 针对需要使用模块的步骤，来构建克隆执行计划模块
     *
     * @return
     */
    @ApiOperation(value = "构建克隆执行计划模块")
    @PostMapping("/build_clone_modules_plan")
    @RetryAnnotation(timeout = 30, retry = 6, interval = 5)
    public Result<Map<String, ModulePlan>> buildCloneModulesPlan(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                                 @RequestParam(name = "sourceReplicaSetName") String sourceReplicaSetName,
                                                                 @RequestParam(name = "restoreType") String restoreType,
                                                                 @RequestParam(name = "bakHisID") String bakHisID,
                                                                 @RequestParam(name = "backupSetHostId") String backupSetHostId,
                                                                 @RequestParam(name = "restoreTime") String restoreTime,
                                                                 @RequestParam(name = "startBinlogFile") String startBinlogFile,
                                                                 @RequestParam(name = "isPengineBackupSet", required = false) String isPengineBackupSet,
                                                                 @RequestParam(name = "backupKindCode", required = false) String backupKindCode) throws Exception {
        ReplicaSet replicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), replicaSetName, null);
        ReplicaSet sourceReplicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), sourceReplicaSetName, true);

        Map<String, MysqlCloneModuleBuilder> cloneModuleBuilderMap = new HashMap<>();

        //扩盘和检查节点的磁盘
        cloneModuleBuilderMap.put("resize_and_check_disk", new MysqlCloneModuleBuilder() {
            @Override
            public ModulePlan build(ReplicaSet replicaSet, ReplicaSet sourceReplicaSet, String restoreType,
                                    String bakHisID, String backupSetHostId, String restoreTime, String startBinlogFile) throws Exception {
                ModulePlan cloneModule = new ModulePlan();
                ReplicaListResult replicaListResult = dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(RequestSession.getRequestId(), replicaSetName, null, null, null, null);
                if (ReplicaSetMetaHelper.isStorageTypeCloudDisk(replicaListResult.getItems().get(0).getStorageType())) {
                    Replica masterReplica = replicaListResult.getItems().stream().filter(v -> v.getRole() == Replica.RoleEnum.MASTER).findFirst().get();
                    Map<String, Object> params = new HashMap<>(1);
                    params.put("destDiskSizeMB", masterReplica.getDiskSizeMB());
                    cloneModule.setParams((JSON.toJSONString(params)));
                    return cloneModule;
                }
                return null;
            }
        });

        cloneModuleBuilderMap.put("append_binlog", new MysqlCloneModuleBuilder() {
            @Override
            public ModulePlan build(ReplicaSet replicaSet, ReplicaSet sourceReplicaSet, String restoreType,
                                    String bakHisID, String backupSetHostId, String restoreTime, String startBinlogFile) throws Exception {
                ModulePlan appendLogModule = new ModulePlan();
                String name = sourceReplicaSet != null ? sourceReplicaSet.getName() : null;

                ReplicaListResult destReplicaListResult = dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(RequestSession.getRequestId(), replicaSet.getName(), null, null, null, null);
                Replica destReplica = Objects.requireNonNull(destReplicaListResult.getItems()).stream().filter(v -> v.getRole() == Replica.RoleEnum.MASTER).findFirst().orElse(null);
                if (restoreType.equals(BackupConsts.RESTORE_TYPE_TIME)) {
                    Map<String, Object> params = new HashMap<>();
                    params.put("srcReplicaSetName", name);
                    params.put("destReplicaSetName", replicaSet.getName());
                    params.put("srcReplicaId", backupSetHostId);
                    params.put("destReplicaId", destReplica == null ? null : destReplica.getId());
                    params.put("restoreTime", restoreTime);
                    params.put("startBinlogFile", startBinlogFile);
                    appendLogModule.setParams((JSON.toJSONString(params)));
                } else {
                    appendLogModule.setSkipReason("it is not restore by time");
                }
                return appendLogModule;
            }
        });

        cloneModuleBuilderMap.put("init_db_node_with_data", new MysqlCloneModuleBuilder() {
            @Override
            public ModulePlan build(ReplicaSet replicaSet, ReplicaSet sourceReplicaSet, String restoreType, String bakHisID, String backupSetHostId, String restoreTime, String startBinlogFile) throws Exception {
                ModulePlan initDBModule = new ModulePlan();
                Map<String, Object> params = new HashMap<>();
                params.put("isPengineBackupSet", isPengineBackupSet);
                params.put("backupKindCode", backupKindCode);
                if (sourceReplicaSet != null) {
                    GetBackupSetResponse backupSetResponse = backupBaseService.getBackupSet(sourceReplicaSet.getName(), LangUtil.getLong(bakHisID));
                    params.put("skipLock", true);
                    params.put("isPengineBackupSet", !Objects.equals(backupSetResponse.getKindCode(), KIND_CODE_K8S));
                    params.put("bakHisID", backupSetResponse.getBackupSetId());
                    params.put("sourceReplicaSetName", sourceReplicaSet.getName());
                    if (BackupConsts.BACKUP_SET_STATUS_OK.equalsIgnoreCase(backupSetResponse.getStatus())) {
                        GetBackupSetResponse.SlaveStatus slaveStatus = backupSetResponse.getSlaveStatusObj();
                        if (slaveStatus != null && StringUtils.isNotEmpty(slaveStatus.getMINOR_VERSION())) {
                            String minorVersionFromBackup = slaveStatus.getMINOR_VERSION();
                            String currentMinorVersion = minorVersionService.getMinorVersionFromMetaDB(replicaSet.getName());
                            params.put("skipUpgradeVersion", StringUtils.equalsIgnoreCase(currentMinorVersion, minorVersionFromBackup));
                        }
                    }
                }
                initDBModule.setParams(JSON.toJSONString(params));
                return initDBModule;
            }
        });

        Map<String, ModulePlan> resultMap = new HashMap<>();
        for (String stepName : cloneModuleBuilderMap.keySet()) {
            resultMap.put(stepName, cloneModuleBuilderMap.get(stepName).build(replicaSet, sourceReplicaSet, restoreType,
                    bakHisID, backupSetHostId, restoreTime, startBinlogFile));
        }
        return Result.returnSuccessResult(resultMap);
    }

    @ApiOperation(value = "构建跨地域恢复执行计划模块")
    @PostMapping("/build_disaster_restore_modules_plan")
    public Result<Map<String, ModulePlan>> buildDisasterRestoreModulesPlan(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                                 @RequestParam(name = "sourceReplicaSetName") String sourceReplicaSetName,
                                                                 @RequestParam(name = "restoreType") String restoreType,
                                                                 @RequestParam(name = "bakHisId") String bakHisId,
                                                                 @RequestParam(name = "consistentTime") String consistentTime,
                                                                 @RequestParam(name = "restoreTime") String restoreTime) throws Exception {
        ReplicaSet replicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), replicaSetName, null);

        Map<String, MysqlDisasterRestoreModuleBuilder> disasterRestoreModuleBuilderMap = new HashMap<>();

        //扩盘和检查节点的磁盘
        disasterRestoreModuleBuilderMap.put("resize_and_check_disk", (replicaSet1, restoreType1, bakHisId1, consistentTime1, restoreTime1, sourceReplicaSetName1) -> {
            ModulePlan resizeModule = new ModulePlan();
            ReplicaListResult replicaListResult = dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(
                    RequestSession.getRequestId(), replicaSetName, null, null, null, null);

            if (ReplicaSetMetaHelper.isStorageTypeCloudDisk(replicaListResult.getItems().get(0).getStorageType())) {
                Replica masterReplica = replicaListResult.getItems().stream().filter(v -> v.getRole() == Replica.RoleEnum.MASTER).findFirst().get();
                Map<String, Object> params = new HashMap<>(1);
                params.put("destDiskSizeMB", masterReplica.getDiskSizeMB());
                resizeModule.setParams((JSON.toJSONString(params)));
            } else {
                resizeModule.setSkipReason("not cloud disk");
            }
            return resizeModule;
        });

        disasterRestoreModuleBuilderMap.put("apply_binlog_by_eir", (replicaSet2, restoreType2, bakHisId2, consistentTime2, restoreTime2, sourceReplicaSetName2) -> {
            ModulePlan recoverModule = new ModulePlan();

            if (restoreType2.equals(BackupConsts.RESTORE_TYPE_TIME)) {
                Map<String, Object> params = new HashMap<>();
                params.put("srcReplicaSetName", sourceReplicaSetName2);
                params.put("restoreType", restoreType2);
                params.put("bakHisId", bakHisId2);
                params.put("restoreTime", restoreTime2);
                params.put("consistentTime", consistentTime2);
                recoverModule.setParams((JSON.toJSONString(params)));
            } else {
                recoverModule.setSkipReason("it is not restore by time");
            }
            return recoverModule;
        });

        Map<String, ModulePlan> resultMap = new HashMap<>(1);
        for (String stepName : disasterRestoreModuleBuilderMap.keySet()) {
            resultMap.put(stepName, disasterRestoreModuleBuilderMap.get(stepName).build(replicaSet, restoreType, bakHisId, consistentTime, restoreTime, sourceReplicaSetName));
        }
        return Result.returnSuccessResult(resultMap);
    }

    /**
     * 针对需要使用模块的步骤，来构建变配执行计划模块
     *
     * @return
     */
    @ApiOperation(value = "构建变配执行计划模块")
    @PostMapping("/build_modify_modules_plan")
    public Result<Map<String, ModulePlan>> buildModifyModulesPlan(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                                  @RequestParam(name = "transTaskId") Long transTaskId) throws Exception {

        TransferTask transferTask = dBaasMetaClient.getDefaultmetaApi().getTransferTask(RequestSession.getRequestId(),
                replicaSetName, LangUtil.getInteger(transTaskId));

        Map<String, TransModuleBuilder> transModuleBuilderMap = new HashMap<>();

        transModuleBuilderMap.put("replace_node", new TransModuleBuilder() {
            @Override
            public ModulePlan build(TransferTask transferTask) throws ApiException {
                ModulePlan transModule = new ModulePlan();
                List<Replica> srcReplicas = dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(RequestSession.getRequestId(), transferTask.getSrcReplicaSetName(), null, null, null, null).getItems();
                List<Replica> destReplicas = dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(RequestSession.getRequestId(), transferTask.getDestReplicaSetName(), null, null, null, null).getItems();
                Map<String, String> labels = dBaasMetaClient.getDefaultmetaApi().listReplicaSetLabels(RequestSession.getRequestId(), transferTask.getSrcReplicaSetName());
                Long srcReplicaId = srcReplicas.get(0).getId();
                Long destReplicaId = destReplicas.get(0).getId();
                ReplicaResource srcReplicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), srcReplicaId, null);
                ReplicaResource destReplicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), destReplicaId, null);

                Map<String, Object> params = new HashMap<>();
                params.put("srcReplicaSetName", transferTask.getSrcReplicaSetName());
                params.put("destReplicaSetName", transferTask.getDestReplicaSetName());
                params.put("srcReplicaId", srcReplicaId);
                params.put("destReplicaId", destReplicaId);
                params.put("srcDiskSizeMB", transferTask.getSrcDiskSizeMB());
                params.put("destDiskSizeMB", transferTask.getDestDiskSizeMB());
                params.put("srcPerformanceLevel", LangUtil.getString(srcReplicaResource.getVolumes().get(0).getPerformanceLevel(), Volume.PerformanceLevelEnum.PL1.toString()));
                params.put("destStorageType", destReplicaResource.getVolumes().get(0).getStorageType().toString());
                params.put("targetPerformanceLevel", LangUtil.getString(destReplicaResource.getVolumes().get(0).getPerformanceLevel(), Volume.PerformanceLevelEnum.PL1.toString()));
                params.put("dbEngine", LangUtil.getString(labels.get("dbEngine")));
                transModule.setParams((JSON.toJSONString(params)));
                return transModule;
            }
        });

        Map<String, ModulePlan> resultMap = new HashMap<>(1);
        for (String stepName : transModuleBuilderMap.keySet()) {
            resultMap.put(stepName, transModuleBuilderMap.get(stepName).build(transferTask));
        }

        return Result.returnSuccessResult(resultMap);
    }

    /**
     * 构建恢复数据模块
     */
    @ApiOperation(value = "构建备库重搭数据恢复执行计划模块")
    @PostMapping("/build_restore_data_modules_plan")
    public Result<Map<String, ModulePlan>> buildRestoreDataModulesPlan(@RequestParam(name = "srcReplicaSetName") String srcReplicaSetName,
                                                                       @RequestParam(name = "destReplicaSetName") String destReplicaSetName,
                                                                       @RequestParam(name = "srcReplicaId") Long srcReplicaId,
                                                                       @RequestParam(name = "destReplicaId") Long destReplicaId,
                                                                       @RequestParam(name = "backupOnMaster", defaultValue = "false") boolean backupOnMaster) throws Exception {
        ReplicaSet srcReplicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), srcReplicaSetName, null);
        ReplicaSet destReplicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), destReplicaSetName, null);

        ReplicaResource srcReplicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), srcReplicaId, null);
        ReplicaResource destReplicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), destReplicaId, null);
        ConfigListResult configListResult = dBaasMetaClient.getDefaultmetaApi().listConfigs(RequestSession.getRequestId(), ResourceKeyConsts.RESTORE_DATA_BY_SNAPSHOT);
        Map<String, String> labels = dBaasMetaClient.getDefaultmetaApi().listReplicaSetLabels(RequestSession.getRequestId(), srcReplicaSetName);

        Map<String, RebuildFollowerModuleBuilder> moduleBuilderMap = new HashMap<>(1);
        moduleBuilderMap.put("restore_replica_data", (srcReplicaSet1, destReplicaSet1, srcReplica, destReplica) -> {
            boolean restoreType = false;
            if (replicaSetMetaHelper.isCloudPfsDisk(srcReplicaSetName) && configListResult != null
                    && CollectionUtils.isNotEmpty(configListResult.getItems())) {
                List<Config> configs = configListResult.getItems();
                Config config = configs.get(0);
                String value = config.getValue();
                if ("TRUE".equals(value) || (labels.containsKey("RESTORE_DATA_BY_SNAPSHOT") && "TRUE".equals(labels.get("RESTORE_DATA_BY_SNAPSHOT")))) {
                    restoreType = true;
                }
            }
            ModulePlan modulePlan = new ModulePlan();
            String moduleName = restoreType ? "restore_replica_data_by_snapshot" : "restore_replica_data_by_nc";
            modulePlan.setModuleName(moduleName);
            Map<String, Object> params = new HashMap<>(8);
            params.put("srcReplicaSetName", srcReplicaSet1.getName());
            params.put("destReplicaSetName", destReplicaSet1.getName());
            params.put("srcReplicaId", srcReplicaResource.getReplica().getId());
            params.put("destReplicaId", destReplicaResource.getReplica().getId());
            params.put("backupOnMaster", backupOnMaster);
            modulePlan.setParams((JSON.toJSONString(params)));
            return modulePlan;
        });

        Map<String, ModulePlan> resultMap = new HashMap<>(1);
        for (String stepName : moduleBuilderMap.keySet()) {
            resultMap.put(stepName, moduleBuilderMap.get(stepName).build(srcReplicaSet, destReplicaSet, srcReplicaResource, destReplicaResource));
        }
        return Result.returnSuccessResult(resultMap);
    }

    /**
     * 构建备库重搭时，数据恢复执行计划模块
     */
    @ApiOperation(value = "构建备库重搭数据恢复执行计划模块")
    @PostMapping("/build_rebuild_follower_modules_plan")
    public Result<Map<String, ModulePlan>> buildRebuildFollowerModulesPlan(@RequestParam(name = "srcReplicaSetName") String srcReplicaSetName,
                                                                           @RequestParam(name = "destReplicaSetName") String destReplicaSetName,
                                                                           @RequestParam(name = "srcReplicaId") Long srcReplicaId,
                                                                           @RequestParam(name = "destReplicaId") Long destReplicaId,
                                                                           @RequestParam(name = "backupReplicaId") Long backupReplicaId) throws Exception {

        ReplicaSet srcReplicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), srcReplicaSetName, null);
        ReplicaSet destReplicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), destReplicaSetName, null);

        ReplicaResource srcReplicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), srcReplicaId, null);
        ReplicaResource destReplicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), destReplicaId, null);

        Map<String, RebuildFollowerModuleBuilder> moduleBuilderMap = new HashMap<>();

        moduleBuilderMap.put("restore_dest_data", new RebuildFollowerModuleBuilder() {
            @Override
            public ModulePlan build(ReplicaSet srcReplicaSet, ReplicaSet destReplicaSet, ReplicaResource srcReplica, ReplicaResource destReplica) throws Exception {
                ModulePlan modulePlan = new ModulePlan();
                boolean isPfs = replicaSetMetaHelper.isCloudPfsDisk(srcReplicaSet.getName());
                Map<String, Object> params = new HashMap<>(1);
                if (ReplicaSetMetaHelper.isStorageTypeCloudDisk(srcReplicaResource.getReplica().getStorageType()) && !isPfs) {
                    modulePlan.setModuleName("init_dest_replicaset_data_from_backup_set_for_cloud_disk");

                    params.put("srcReplicaSetName", srcReplicaSet.getName());
                    params.put("destReplicaSetName", destReplicaSet.getName());
                    params.put("srcReplicaId", srcReplicaResource.getReplica().getId());
                    params.put("srcReplicaIdList", Collections.singletonList(srcReplicaResource.getReplica().getId()));
                    params.put("destReplicaId", destReplicaResource.getReplica().getId());
                    params.put("destReplicaIdList", Collections.singletonList(destReplicaResource.getReplica().getId()));
                    params.put("destDiskSizeMB", destReplicaResource.getReplica().getDiskSizeMB());
                    params.put("backupPriority", backupReplicaId.equals(srcReplicaResource.getReplica().getId()) ? BackupConsts.BACKUP_PRIORITY_FORCE_SLAVE : BackupConsts.BACKUP_PRIORITY_FORCE_MASTER);

                } else {
                    modulePlan.setModuleName("init_dest_replicaset_data_from_nc");
                    params.put("srcReplicaSetName", srcReplicaSet.getName());
                    params.put("destReplicaSetName", destReplicaSet.getName());
                    params.put("srcReplicaId", srcReplicaResource.getReplica().getId());
                    params.put("srcReplicaIdList", Collections.singletonList(srcReplicaResource.getReplica().getId()));
                    params.put("destReplicaId", destReplicaResource.getReplica().getId());
                    params.put("destReplicaIdList", Collections.singletonList(destReplicaResource.getReplica().getId()));
                    params.put("srcBackupReplicaId", backupReplicaId);
                    params.put("replicaIdList", destReplicaResource.getReplica().getId());
                    params.put("skip_init_db", true);
                }
                modulePlan.setParams((JSON.toJSONString(params)));
                return modulePlan;
            }
        });
        Map<String, ModulePlan> resultMap = new HashMap<>(1);
        for (String stepName : moduleBuilderMap.keySet()) {
            resultMap.put(stepName, moduleBuilderMap.get(stepName).build(srcReplicaSet, destReplicaSet, srcReplicaResource, destReplicaResource));
        }
        return Result.returnSuccessResult(resultMap);
    }

    @ApiOperation(value = "构建基础版小版本升级增量恢复任务")
    @PostMapping("/build_upgrade_version_for_basic_modules_plan")
    public Result<Map<String, ModulePlan>> buildUpgradeVersionForBasic(@RequestParam(name = "srcReplicaSetName") String srcReplicaSetName,
                                                                       @RequestParam(name = "destReplicaSetName") String destReplicaSetName,
                                                                       @RequestParam(name = "backupSetId") Long backupSetId) throws Exception {
        Map<String, UpgradeVersionForBasicBuilder> modulesPlan = new HashMap<>();

        modulesPlan.put("append_binlog", new UpgradeVersionForBasicBuilder() {
            @Override
            public ModulePlan build(String srcReplicaSetName, String destReplicaSetName, Long backupSetId) throws Exception {
                ModulePlan appendLogModule = new ModulePlan();
                Map<String, Object> params = new HashMap<>();
                BinlogBackupPolicyResponse policyResponse = bifrostBaseService.getBinlogBackupPolicy(srcReplicaSetName);
                if (Objects.equals(policyResponse.getUploadWay(), BackupConsts.BINLOG_NO_UPLOAD)) {
                    appendLogModule.setSkipReason("binlog policy is not upload.");
                } else {
                    GetBackupSetResponse getBackupSetResponse = backupBaseService.getBackupSet(srcReplicaSetName, backupSetId);
                    if (!BackupConsts.BACKUP_SET_STATUS_OK.equalsIgnoreCase(getBackupSetResponse.getStatus())) {
                        throw new NotRetryException(String.format("The backup set state is unavailable %s", getBackupSetResponse));
                    }
                    GetBackupSetResponse.SlaveStatus slaveStatus = getBackupSetResponse.getSlaveStatusObj();
                    boolean hasPurged = bifrostBaseService.binlogFromBackupSetHasPurged(srcReplicaSetName, slaveStatus.getBINLOGFILE(), slaveStatus.getBINLOGHINSID());
                    if (hasPurged) {
                        params.put("srcReplicaSetName", srcReplicaSetName);
                        params.put("destReplicaSetName", destReplicaSetName);
                        params.put("srcReplicaId", slaveStatus.getBINLOGHINSID());
                        params.put("destReplicaId", mysqlBizService.findMasterReplica(destReplicaSetName).getId());

                        // 指定恢复到最新的binlog
                        params.put("restoreToNewest", true);
                        params.put("restoreTime", DateTimeUtils.MAX_TIME);
                        if(replicaSetMetaHelper.isReplicaSetExternalReplication(destReplicaSetName)){
                            params.put("channel", MAINTAIN_THREAD_CHANNEL);
                        }

                        params.put("startBinlogFile", slaveStatus.getBINLOGFILE());
                        appendLogModule.setModuleName("append_binlog");
                        appendLogModule.setParams((JSON.toJSONString(params)));
                    } else {
                        appendLogModule.setSkipReason("binlog is not purged.");
                    }
                }
                return appendLogModule;
            }
        });
        Map<String, ModulePlan> resultMap = new HashMap<>(1);
        for (String stepName : modulesPlan.keySet()) {
            resultMap.put(stepName, modulesPlan.get(stepName).build(srcReplicaSetName, destReplicaSetName, backupSetId));
        }
        return Result.returnSuccessResult(resultMap);
    }

    @ApiOperation(value = "构建基础版数据恢复计划")
    @PostMapping("/build_recover_basic_ins_modules_plan")
    public Result<Map<String, ModulePlan>> buildRecoverBasicIns(@RequestParam(name = "srcReplicaSetName") String srcReplicaSetName,
                                                                @RequestParam(name = "destReplicaSetName") String destReplicaSetName,
                                                                @RequestParam(name = "backupSetId") Long backupSetId,
                                                                @RequestParam(name = "restoreType") String restoreType,
                                                                @RequestParam(name = "restoreTime", defaultValue = "") String restoreTime
    ) throws Exception {
        Map<String, RecoverBasicInsBuilder> modulesPlan = new HashMap<>();

        modulesPlan.put("append_binlog", new RecoverBasicInsBuilder() {
            @Override
            public ModulePlan build(String srcReplicaSetName, String destReplicaSetName, Long backupSetId, String restoreTime) throws Exception {
                ModulePlan appendLogModule = new ModulePlan();
                if (restoreType.equals(BackupConsts.RESTORE_TYPE_TIME)) {
                    GetBackupSetResponse getBackupSetResponse = backupBaseService.getBackupSet(srcReplicaSetName, backupSetId);
                    if (!BackupConsts.BACKUP_SET_STATUS_OK.equalsIgnoreCase(getBackupSetResponse.getStatus())) {
                        throw new NotRetryException(String.format("The backup set state is unavailable %s", getBackupSetResponse));
                    }
                    GetBackupSetResponse.SlaveStatus slaveStatus = getBackupSetResponse.getSlaveStatusObj();

                    Map<String, Object> params = new HashMap<>();
                    params.put("srcReplicaSetName", srcReplicaSetName);
                    params.put("destReplicaSetName", destReplicaSetName);
                    params.put("srcReplicaId", slaveStatus.getBINLOGHINSID());
                    params.put("destReplicaId", mysqlBizService.findMasterReplica(destReplicaSetName).getId());

                    if (StringUtils.isEmpty(restoreTime)) {
                        // 指定恢复到最新的binlog
                        params.put("restoreToNewest", true);
                        params.put("restoreTime", DateTimeUtils.MAX_TIME);
                    } else {
                        params.put("restoreTime", restoreTime);
                    }

                    params.put("startBinlogFile", slaveStatus.getBINLOGFILE());
                    appendLogModule.setModuleName("append_binlog");
                    appendLogModule.setParams((JSON.toJSONString(params)));
                } else {
                    appendLogModule.setSkipReason("not restore by time.");
                }

                return appendLogModule;
            }
        });
        Map<String, ModulePlan> resultMap = new HashMap<>(1);
        for (String stepName : modulesPlan.keySet()) {
            resultMap.put(stepName, modulesPlan.get(stepName).build(srcReplicaSetName, destReplicaSetName, backupSetId, restoreTime));
        }
        return Result.returnSuccessResult(resultMap);
    }

    /**
     * 针对Pengine迁移新架构，来构建备份恢复执行计划模块
     *
     * @return
     */
    @ApiOperation(value = "构建备份恢复执行计划模块")
    @PostMapping("/build_migrate_k8s_restore_modules_plan")
    public Result<Map<String, ModulePlan>> buildMigrateK8SRestoreModulesPlan(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                                             @RequestParam(name = "sourceReplicaSetName") String sourceReplicaSetName,
                                                                             @RequestParam(name = "startBinlogFile") String startBinlogFile) throws Exception {
        // 当前目标实例
        ReplicaSet replicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), replicaSetName, null);
        ReplicaListResult destReplicaListResult = dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(RequestSession.getRequestId(), replicaSetName, null, null, null, null);
        Replica destReplica = destReplicaListResult.getItems().stream().filter(v -> v.getRole() == Replica.RoleEnum.MASTER).findFirst().get();
        // Pengine实例
        Replica srcReplica = mysqlMigrateBizService.findPengineMasterReplica(sourceReplicaSetName,null);
        ReplicaSet srcPhysicalReplicaSet = mysqlMigrateBizService.findPenginePhysicalReplicaSet(sourceReplicaSetName);
        String srcPhysicalCustInsName = srcPhysicalReplicaSet!=null?srcPhysicalReplicaSet.getName(): null ;
        log.info("srcPhysicalCustInsName -> {}", srcPhysicalCustInsName);
        Map<String, MigrateK8SRestoreModuleBuilder> migrateK8SRestoreModuleBuilderMap = new HashMap<>();
        migrateK8SRestoreModuleBuilderMap.put("recovery_data", new MigrateK8SRestoreModuleBuilder() {
            @Override
            public ModulePlan build(ReplicaSet replicaSet, ReplicaSet sourceReplicaSet, String restoreType,
                                    String restoreTime, String startBinlogFile) throws Exception {

                Map<String, Object> params = new HashMap<>();
                BinlogBackupPolicyResponse policyResponse = bifrostBaseService.getBinlogBackupPolicy(sourceReplicaSetName);
                ModulePlan appendLogModule = new ModulePlan();
                if (Objects.equals(policyResponse.getUploadWay(), BackupConsts.BINLOG_NO_UPLOAD)) {
                    appendLogModule.setSkipReason("binlog policy is not upload.");
                } else {
                    boolean hasPurged = bifrostBaseService.binlogFromBackupSetHasPurged(sourceReplicaSetName, startBinlogFile, srcReplica.getId());
                    if (hasPurged) {
                        params.put("srcReplicaSetName", sourceReplicaSetName);
                        params.put("srcPhysicalCustInsName", srcPhysicalCustInsName);
                        params.put("destReplicaSetName", replicaSetName);
                        params.put("srcReplicaId", srcReplica.getId());
                        params.put("destReplicaId", destReplica.getId());
                        params.put("startBinlogFile", startBinlogFile);
                        // 指定恢复到最新的binlog
                        params.put("restoreToNewest", true);
                        params.put("migrateToK8s", true);
                        params.put("restoreTime", DateTimeUtils.MAX_TIME);
                    }
                    if (params.isEmpty()) {
                        appendLogModule.setSkipReason("binlog is not purged.");
                    } else {
                        appendLogModule.setParams((JSON.toJSONString(params)));
                    }
                }
                return appendLogModule;
            }
        });

        migrateK8SRestoreModuleBuilderMap.put("online_resize", new MigrateK8SRestoreModuleBuilder() {
            @Override
            public ModulePlan build(ReplicaSet replicaSet, ReplicaSet sourceReplicaSet, String restoreType, String restoreTime, String startBinlogFile) throws Exception {
                ModulePlan onlineResizeModule = new ModulePlan();
                Map<String, Object> params = new HashMap<>(1);
                params.put("destDiskSizeMB", replicaSet.getDiskSizeMB());
                onlineResizeModule.setParams((JSON.toJSONString(params)));
                return onlineResizeModule;
            }
        });

        Map < String, ModulePlan > resultMap = new HashMap<>(1);
        for (String stepName : migrateK8SRestoreModuleBuilderMap.keySet()) {
            resultMap.put(stepName, migrateK8SRestoreModuleBuilderMap.get(stepName).build(replicaSet, srcPhysicalReplicaSet, "1",
                    DateTimeUtils.MAX_TIME, startBinlogFile));
        }
        return Result.returnSuccessResult(resultMap);
    }


    /**
     * 构建基础版单租户实例本地变配执行计划
     * @return
     */
    @ApiOperation(value = "构建基础版单租户实例本地变配执行计划")
    @PostMapping("/build_local_modify_modules_plan")
    public Result<Map<String, ModulePlan>> buildLocalModifyModulesPlan(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                                       @RequestParam(name = "transTaskId") Long transTaskId) throws Exception {
        TransferTask transferTask = dBaasMetaClient.getDefaultmetaApi().getTransferTask(RequestSession.getRequestId(),
                replicaSetName, LangUtil.getInteger(transTaskId));

        Map<String, TransModuleBuilder> transModuleBuilderMap = new HashMap<>();

        Map<String, Object> transParameter = JSON.parseObject(transferTask.getParameter(), Map.class);
        String srcPerformanceLevel = LangUtil.getString(transParameter.get("srcPerformanceLevel"));
        String targetPerformanceLevel = LangUtil.getString(transParameter.get("targetPerformanceLevel"));
        String srcDiskType = LangUtil.getString(transParameter.get("srcDiskType"));
        String targetDiskType = LangUtil.getString(transParameter.get("targetDiskType"));

        transModuleBuilderMap.put("local_upgrade_node", transTask -> {
            ModulePlan transModule = new ModulePlan();
            List<Replica> srcReplicas = dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(RequestSession.getRequestId(), transTask.getSrcReplicaSetName(), null, null, null, null).getItems();
            List<Replica> destReplicas = dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(RequestSession.getRequestId(), transTask.getDestReplicaSetName(), null, null, null, null).getItems();
            Long srcReplicaId = srcReplicas.get(0).getId();
            Long destReplicaId = destReplicas.get(0).getId();
            Map<String, Object> params = new HashMap<>(1);
            params.put("srcReplicaSetName", transTask.getSrcReplicaSetName());
            params.put("srcReplicaId", srcReplicaId);
            params.put("destReplicaId", destReplicaId);
            params.put("srcClassCode", transTask.getSrcClassCode());
            params.put("destClassCode", transTask.getDestClassCode());
            params.put("srcDiskSizeMB", transTask.getSrcDiskSizeMB());
            params.put("destDiskSizeMB", transTask.getDestDiskSizeMB());
            params.put("srcDiskType", srcDiskType);
            params.put("targetDiskType", targetDiskType);
            params.put("srcPerformanceLevel", srcPerformanceLevel);
            params.put("targetPerformanceLevel", targetPerformanceLevel);
            transModule.setParams((JSON.toJSONString(params)));
            return transModule;
        });

        Map<String, ModulePlan> resultMap = new HashMap<>();
        for (String stepName : transModuleBuilderMap.keySet()) {
            resultMap.put(stepName, transModuleBuilderMap.get(stepName).build(transferTask));
        }
        return Result.returnSuccessResult(resultMap);
    }

    /**
     * 构建高可用单租户实例本地变配执行计划
     * @return
     */
    @ApiOperation(value = "构建高可用单租户实例本地变配执行计划")
    @PostMapping("/build_ha_local_modify_modules_plan")
    public Result<Map<String, ModulePlan>> buildHALocalModifyModulesPlan(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                                         @RequestParam(name = "transTaskId") Long transTaskId) throws Exception {
        TransferTask transferTask = dBaasMetaClient.getDefaultmetaApi().getTransferTask(RequestSession.getRequestId(),
                replicaSetName, LangUtil.getInteger(transTaskId));

        Map<String, TransModuleBuilder> transModuleBuilderMap = new HashMap<>();

        Map<String, Object> transParameter = JSON.parseObject(transferTask.getParameter(), Map.class);
        String srcPerformanceLevel = LangUtil.getString(transParameter.get("srcPerformanceLevel"));
        String targetPerformanceLevel = LangUtil.getString(transParameter.get("targetPerformanceLevel"));
        String srcDiskType = LangUtil.getString(transParameter.get("srcDiskType"));
        String targetDiskType = LangUtil.getString(transParameter.get("targetDiskType"));
        String resourceGuaranteeLevel = LangUtil.getString(transParameter.get("resourceGuaranteeLevel"));
        String resourceGuaranteeLevelType = LangUtil.getString(transParameter.get("resourceGuaranteeLevelType"));
        String resourceGuaranteeBackUpLevels = LangUtil.getString(transParameter.get("resourceGuaranteeBackUpLevels"));

        transModuleBuilderMap.put("online_resize_node", new TransModuleBuilder() {
            @Override
            public ModulePlan build(TransferTask transferTask) throws Exception {
                ModulePlan transModule = new ModulePlan();
                Map<String, Object> params = new HashMap<>();
                if (!transferTask.getSrcDiskSizeMB().equals(transferTask.getDestDiskSizeMB())) {
                    params.put("srcDiskSizeMB", transferTask.getSrcDiskSizeMB());
                    params.put("destDiskSizeMB", transferTask.getDestDiskSizeMB());
                    params.put("replicaSetName", replicaSetName);
                }
                if (params.isEmpty()) {
                    transModule.setSkipReason("no need online resize");
                } else {
                    transModule.setParams(JSON.toJSONString(params));
                }
                return transModule;
            }
        });

        transModuleBuilderMap.put("local_upgrade_master_node", transTask -> {
            ModulePlan transModule = new ModulePlan();
            Replica masterReplica = mysqlBizService.findMasterReplica(replicaSetName);
            Long srcReplicaId = masterReplica.getId();
            Map<String, Object> params = new HashMap<>(1);
            params.put("srcReplicaSetName", transTask.getSrcReplicaSetName());
            params.put("srcReplicaId", srcReplicaId);
            params.put("srcClassCode", transTask.getSrcClassCode());
            params.put("destClassCode", transTask.getDestClassCode());
            params.put("srcDiskSizeMB", transTask.getSrcDiskSizeMB());
            params.put("destDiskSizeMB", transTask.getDestDiskSizeMB());
            params.put("srcDiskType", srcDiskType);
            params.put("targetDiskType", targetDiskType);
            params.put("srcPerformanceLevel", srcPerformanceLevel);
            params.put("targetPerformanceLevel", targetPerformanceLevel);
            params.put("resourceGuaranteeLevel", resourceGuaranteeLevel);
            params.put("resourceGuaranteeLevelType", resourceGuaranteeLevelType);
            params.put("resourceGuaranteeBackUpLevels", resourceGuaranteeBackUpLevels);
            transModule.setParams((JSON.toJSONString(params)));
            return transModule;
        });

        transModuleBuilderMap.put("local_upgrade_slave_node", transTask -> {
            ModulePlan transModule = new ModulePlan();
            Replica slaveReplica = mysqlBizService.findSlaveReplica(replicaSetName);
            Long srcReplicaId = slaveReplica.getId();
            Map<String, Object> params = new HashMap<>(1);
            params.put("srcReplicaSetName", transTask.getSrcReplicaSetName());
            params.put("srcReplicaId", srcReplicaId);
            params.put("srcClassCode", transTask.getSrcClassCode());
            params.put("destClassCode", transTask.getDestClassCode());
            params.put("srcDiskSizeMB", transTask.getSrcDiskSizeMB());
            params.put("destDiskSizeMB", transTask.getDestDiskSizeMB());
            params.put("srcDiskType", srcDiskType);
            params.put("targetDiskType", targetDiskType);
            params.put("srcPerformanceLevel", srcPerformanceLevel);
            params.put("targetPerformanceLevel", targetPerformanceLevel);
            params.put("resourceGuaranteeLevel", resourceGuaranteeLevel);
            params.put("resourceGuaranteeLevelType", resourceGuaranteeLevelType);
            params.put("resourceGuaranteeBackUpLevels", resourceGuaranteeBackUpLevels);
            transModule.setParams((JSON.toJSONString(params)));
            return transModule;
        });
        Map<String, ModulePlan> resultMap = new HashMap<>();
        for (String stepName : transModuleBuilderMap.keySet()) {
            resultMap.put(stepName, transModuleBuilderMap.get(stepName).build(transferTask));
        }
        return Result.returnSuccessResult(resultMap);
    }



    /**
     * 构建cluster单租户实例本地变配执行计划
     * @return
     */
    @RetryAnnotation(timeout = 300, retry = 3, interval = 10)
    @ApiOperation(value = "构建cluster单租户实例本地变配执行计划")
    @PostMapping("/build_cluster_local_modify_modules_plan")
    public Result<Map<String, ModulePlan>> buildClusterLocalModifyModulesPlan(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                                             @RequestParam(name = "transTaskId") Long transTaskId) throws Exception {
        TransferTask transferTask = dBaasMetaClient.getDefaultmetaApi().getTransferTask(RequestSession.getRequestId(),
                replicaSetName, LangUtil.getInteger(transTaskId));

        Map<String, TransModuleBuilder> transModuleBuilderMap = new HashMap<>();

        Map<String, Object> transParameter = JSON.parseObject(transferTask.getParameter(), Map.class);
        String srcPerformanceLevel = LangUtil.getString(transParameter.get("srcPerformanceLevel"));
        String targetPerformanceLevel = LangUtil.getString(transParameter.get("targetPerformanceLevel"));
        String srcDiskType = LangUtil.getString(transParameter.get("srcDiskType"));
        String targetDiskType = LangUtil.getString(transParameter.get("targetDiskType"));
        String resourceGuaranteeLevel = LangUtil.getString(transParameter.get("resourceGuaranteeLevel"));
        String resourceGuaranteeLevelType = LangUtil.getString(transParameter.get("resourceGuaranteeLevelType"));
        String resourceGuaranteeBackUpLevels = LangUtil.getString(transParameter.get("resourceGuaranteeBackUpLevels"));

        transModuleBuilderMap.put("online_resize_node", new TransModuleBuilder() {
            @Override
            public ModulePlan build(TransferTask transferTask) throws Exception {
                ModulePlan transModule = new ModulePlan();
                Map<String, Object> params = new HashMap<>();
                if (!transferTask.getSrcDiskSizeMB().equals(transferTask.getDestDiskSizeMB())) {
                    params.put("srcDiskSizeMB", transferTask.getSrcDiskSizeMB());
                    params.put("destDiskSizeMB", transferTask.getDestDiskSizeMB());
                    params.put("replicaSetName", replicaSetName);
                }
                if (params.isEmpty()) {
                    transModule.setSkipReason("no need online resize");
                } else {
                    transModule.setParams(JSON.toJSONString(params));
                }
                return transModule;
            }
        });

        transModuleBuilderMap.put("local_upgrade_master_node", transTask -> {
            ModulePlan transModule = new ModulePlan();
            Map<String, Object> params = new HashMap<>(1);
            params.put("srcReplicaSetName", transTask.getSrcReplicaSetName());
            params.put("srcClassCode", transTask.getSrcClassCode());
            params.put("destClassCode", transTask.getDestClassCode());
            params.put("srcDiskSizeMB", transTask.getSrcDiskSizeMB());
            params.put("destDiskSizeMB", transTask.getDestDiskSizeMB());
            params.put("srcDiskType", srcDiskType);
            params.put("targetDiskType", targetDiskType);
            params.put("srcPerformanceLevel", srcPerformanceLevel);
            params.put("targetPerformanceLevel", targetPerformanceLevel);
            params.put("transTaskId", transTaskId);
            params.put("nodeRole", "master");
            params.put("resourceGuaranteeLevel", resourceGuaranteeLevel);
            params.put("resourceGuaranteeLevelType", resourceGuaranteeLevelType);
            params.put("resourceGuaranteeBackUpLevels", resourceGuaranteeBackUpLevels);
            transModule.setParams((JSON.toJSONString(params)));
            return transModule;
        });

        transModuleBuilderMap.put("local_upgrade_slave_node", transTask -> {
            ModulePlan transModule = new ModulePlan();
            Map<String, Object> params = new HashMap<>(1);
            params.put("srcReplicaSetName", transTask.getSrcReplicaSetName());
            params.put("srcClassCode", transTask.getSrcClassCode());
            params.put("destClassCode", transTask.getDestClassCode());
            params.put("srcDiskSizeMB", transTask.getSrcDiskSizeMB());
            params.put("destDiskSizeMB", transTask.getDestDiskSizeMB());
            params.put("srcDiskType", srcDiskType);
            params.put("targetDiskType", targetDiskType);
            params.put("srcPerformanceLevel", srcPerformanceLevel);
            params.put("targetPerformanceLevel", targetPerformanceLevel);
            params.put("transTaskId", transTaskId);
            params.put("nodeRole", "slave");
            params.put("resourceGuaranteeLevel", resourceGuaranteeLevel);
            params.put("resourceGuaranteeLevelType", resourceGuaranteeLevelType);
            params.put("resourceGuaranteeBackUpLevels", resourceGuaranteeBackUpLevels);
            transModule.setParams((JSON.toJSONString(params)));
            return transModule;
        });
        Map<String, ModulePlan> resultMap = new HashMap<>();
        for (String stepName : transModuleBuilderMap.keySet()) {
            resultMap.put(stepName, transModuleBuilderMap.get(stepName).build(transferTask));
        }
        return Result.returnSuccessResult(resultMap);
    }


    @Deprecated
    @ApiOperation(value = "构建变配执行计划模块")
    @PostMapping("/build_basic_to_standard_modules_plan")
    public Result<Map<String, ModulePlan>> buildBasicToStandardModulesPlan(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                                           @RequestParam(name = "transTaskId") Long transTaskId) throws Exception {

        return buildBasicUpgradeCategoryModulesPlan(replicaSetName, transTaskId);
    }

    @ApiOperation(value = "构建变配执行计划模块")
    @PostMapping("/build_basic_upgrade_category_modules_plan")
    public Result<Map<String, ModulePlan>> buildBasicUpgradeCategoryModulesPlan(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                                           @RequestParam(name = "transTaskId") Long transTaskId) throws Exception {

        TransferTask transferTask = dBaasMetaClient.getDefaultmetaApi().getTransferTask(RequestSession.getRequestId(),
                replicaSetName, LangUtil.getInteger(transTaskId));

        Map<String, TransModuleBuilder> transModuleBuilderMap = new HashMap<>();
        transModuleBuilderMap.put("online_resize_node", new TransModuleBuilder() {
            @Override
            public ModulePlan build(TransferTask transferTask) throws Exception {
                ModulePlan transModule = new ModulePlan();
                Map<String, Object> params = new HashMap<>();
                if (!transferTask.getSrcDiskSizeMB().equals(transferTask.getDestDiskSizeMB())) {
                    params.put("srcDiskSizeMB", transferTask.getSrcDiskSizeMB());
                    params.put("destDiskSizeMB", transferTask.getDestDiskSizeMB());
                    params.put("replicaSetName", replicaSetName);
                }
                if (params.isEmpty()) {
                    transModule.setSkipReason("no need online resize");
                } else {
                    transModule.setParams(JSON.toJSONString(params));
                }
                return transModule;
            }
        });


        Map<String, ModulePlan> resultMap = new HashMap<>(1);
        for (String stepName : transModuleBuilderMap.keySet()) {
            resultMap.put(stepName, transModuleBuilderMap.get(stepName).build(transferTask));
        }

        return Result.returnSuccessResult(resultMap);
    }

    @ApiOperation(value = "构建变配执行计划模块")
    @PostMapping("build_standard_to_cluster_directly_modules_plan")
    public Result<Map<String, ModulePlan>> buildStandardToClusterDirectlyModulesPlan(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                                                     @RequestParam(name = "extraParams") String extraParams) throws Exception {
        Map<String, OnlineResizeModuleBuilder> onlineResizeModuleBuilderMap = new HashMap<>();
        JSONObject param = JSONObject.parseObject(extraParams);
        Integer srcDiskSizeMB = param.getInteger("srcDiskSizeMB");
        Integer destDiskSizeMB = param.getInteger("destDiskSizeMB");
        String srcPerformanceLevel = param.getString("srcPerformanceLevel");
        String targetPerformanceLevel = param.getString("targetPerformanceLevel");

        onlineResizeModuleBuilderMap.put("online_resize_ins", () -> {
            ModulePlan onlineResizeModule = new ModulePlan();
            boolean isSizeChanged = !Objects.equals(srcDiskSizeMB, destDiskSizeMB);
            boolean isPlChanged = !Objects.equals(srcPerformanceLevel, targetPerformanceLevel);

            if (isSizeChanged || isPlChanged) {
                JSONObject params = JSONObject.parseObject(extraParams);
                params.put("replicaSetName", replicaSetName);
                params.put("skip_active_ins", true);
                onlineResizeModule.setParams(JSON.toJSONString(params));
                onlineResizeModule.setModuleName(ONLINE_RESIZE_INS);
            } else {
                onlineResizeModule.setSkipReason("no need online resize");
            }
            return onlineResizeModule;
        });

        Map<String, ModulePlan> resultMap = new HashMap<>(1);
        for (String stepName : onlineResizeModuleBuilderMap.keySet()) {
            resultMap.put(stepName, onlineResizeModuleBuilderMap.get(stepName).build());
        }

        return Result.returnSuccessResult(resultMap);
    }

    @ApiOperation(value = "构建cluster增加节点执行计划")
    @PostMapping("/build_cluster_add_node_modules_plan")
    public Result<Map<String, ModulePlan>> buildClusterAddNodeModulesPlan(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                                           @RequestParam(name = "transTaskId") Long transTaskId,
                                                                          @RequestParam(name = "extraParams") String extraParams) throws Exception {

        TransferTask transferTask = dBaasMetaClient.getDefaultmetaApi().getTransferTask(RequestSession.getRequestId(),
                replicaSetName, LangUtil.getInteger(transTaskId));

        Map<String, TransModuleBuilder> transModuleBuilderMap = new HashMap<>();
        transModuleBuilderMap.put("online_resize_node", new TransModuleBuilder() {
            @Override
            public ModulePlan build(TransferTask transferTask) throws Exception {
                ModulePlan transModule = new ModulePlan();
                Map<String, Object> params = new HashMap<>();
                if (!transferTask.getSrcDiskSizeMB().equals(transferTask.getDestDiskSizeMB())) {
                    params.put("srcDiskSizeMB", transferTask.getSrcDiskSizeMB());
                    params.put("destDiskSizeMB", transferTask.getDestDiskSizeMB());
                    params.put("replicaSetName", replicaSetName);
                }
                if (params.isEmpty()) {
                    transModule.setSkipReason("no need online resize");
                } else {
                    transModule.setParams(JSON.toJSONString(params));
                }
                return transModule;
            }
        });

        transModuleBuilderMap.put("node_join_cluster", new TransModuleBuilder() {
            @Override
            public ModulePlan build(TransferTask transferTask) throws Exception {
                ModulePlan transModule = new ModulePlan();
                Map<String, Object> params = JSON.parseObject(extraParams, Map.class);
                boolean isMgr = replicaMetaHelper.isMgr(replicaSetName);
                if (isMgr){
                    transModule.setModuleName("join_node_for_mgr");
                } else {
                    transModule.setModuleName("join_node_for_semi_sync");
                }
                params.put("replicaSetName", replicaSetName);
                transModule.setParams(JSON.toJSONString(params));
                return transModule;
            }
        });


        Map<String, ModulePlan> resultMap = new HashMap<>(1);
        for (String stepName : transModuleBuilderMap.keySet()) {
            resultMap.put(stepName, transModuleBuilderMap.get(stepName).build(transferTask));
        }

        return Result.returnSuccessResult(resultMap);
    }

    @ApiOperation(value = "构建clusters删除节点执行计划")
    @PostMapping("/build_cluster_remove_node_modules_plan")
    public Result<Map<String, ModulePlan>> buildClusterRemoveNodeModulesPlan(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                                            @RequestParam(name = "extraParams") String extraParams) throws Exception {

        TransferTask transferTask = null;
        Map<String, TransModuleBuilder> transModuleBuilderMap = new HashMap<>();
        transModuleBuilderMap.put("cluster_remove_node", new TransModuleBuilder() {
            @Override
            public ModulePlan build(TransferTask transferTask) throws Exception {
                ModulePlan transModule = new ModulePlan();
                Map<String, Object> params = JSON.parseObject(extraParams, Map.class);
                Map<String, String> labels = dBaasMetaClient.getDefaultmetaApi().listReplicaSetLabels(RequestSession.getRequestId(), replicaSetName);
                boolean isMgr = ReplicaSetMetaHelper.isMgr(labels);
                if (isMgr){
                    transModule.setModuleName("remove_node_for_mgr");
                } else {
                    transModule.setModuleName("remove_node_for_semi_sync");
                }
                params.put("replicaSetName", replicaSetName);
                params.put("skip_kill_process", true);
                transModule.setParams(JSON.toJSONString(params));
                return transModule;
            }
        });

        Map<String, ModulePlan> resultMap = new HashMap<>(1);
        for (String stepName : transModuleBuilderMap.keySet()) {
            resultMap.put(stepName, transModuleBuilderMap.get(stepName).build(transferTask));
        }

        return Result.returnSuccessResult(resultMap);
    }


    /**
     * 针对需要使用模块的步骤，来构建变配执行计划模块
     *
     * @return
     */
    @ApiOperation(value = "构建变配执行计划模块")
    @PostMapping("/build_ha_modify_modules_plan")
    public Result<Map<String, ModulePlan>> buildHAModifyModulesPlan(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                                    @RequestParam(name = "transTaskId") Long transTaskId) throws Exception {

        TransferTask transferTask = dBaasMetaClient.getDefaultmetaApi().getTransferTask(RequestSession.getRequestId(),
                replicaSetName, LangUtil.getInteger(transTaskId));

        Map<String, TransModuleBuilder> transModuleBuilderMap = new HashMap<>();
        transModuleBuilderMap.put("online_resize_node", new TransModuleBuilder() {
            @Override
            public ModulePlan build(TransferTask transferTask) throws Exception {
                ModulePlan transModule = new ModulePlan();
                Map<String, Object> params = new HashMap<>();
                if (!transferTask.getSrcDiskSizeMB().equals(transferTask.getDestDiskSizeMB())) {
                    params.put("srcDiskSizeMB", transferTask.getSrcDiskSizeMB());
                    params.put("destDiskSizeMB", transferTask.getDestDiskSizeMB());
                    params.put("replicaSetName", replicaSetName);
                }
                if (params.isEmpty()) {
                    transModule.setSkipReason("no need online resize");
                } else {
                    transModule.setParams(JSON.toJSONString(params));
                }
                return transModule;
            }
        });

        transModuleBuilderMap.put("replace_slave_node", new TransModuleBuilder() {
            @Override
            public ModulePlan build(TransferTask transferTask) throws ApiException {
                ModulePlan transModule = new ModulePlan();
                Replica srcReplica = replicaMetaHelper.getRoleReplicaList(null, transferTask.getSrcReplicaSetName(), Replica.RoleEnum.SLAVE).get(0);
                Replica destReplica = replicaMetaHelper.getRoleReplicaList(null, transferTask.getDestReplicaSetName(), Replica.RoleEnum.SLAVE).get(0);
                ReplicaResource srcReplicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), srcReplica.getId(), null);
                ReplicaResource destReplicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), destReplica.getId(), null);
                Map<String, Object> params = new HashMap<>();
                params.put("srcReplicaSetName", transferTask.getSrcReplicaSetName());
                params.put("destReplicaSetName", transferTask.getDestReplicaSetName());
                params.put("srcReplicaId", srcReplica.getId());
                params.put("destReplicaId", destReplica.getId());
                params.put("srcClassCode", transferTask.getSrcClassCode());
                params.put("destClassCode", transferTask.getDestClassCode());
                params.put("srcDiskSizeMB", transferTask.getDestDiskSizeMB());  //已经onlineResize过了，替换节点流程跳过扩容逻辑
                params.put("destDiskSizeMB", transferTask.getDestDiskSizeMB());
                params.put("destStorageType", destReplicaResource.getVolumes().get(0).getStorageType().toString());
                params.put("srcPerformanceLevel", LangUtil.getString(srcReplicaResource.getVolumes().get(0).getPerformanceLevel(), Volume.PerformanceLevelEnum.PL1.toString()));
                params.put("targetPerformanceLevel", LangUtil.getString(destReplicaResource.getVolumes().get(0).getPerformanceLevel(), Volume.PerformanceLevelEnum.PL1.toString()));
                transModule.setParams((JSON.toJSONString(params)));
                return transModule;
            }
        });

        transModuleBuilderMap.put("replace_master_node", new TransModuleBuilder() {
            @Override
            public ModulePlan build(TransferTask transferTask) throws ApiException {
                ModulePlan transModule = new ModulePlan();
                Replica srcReplica = replicaMetaHelper.getRoleReplicaList(null, transferTask.getSrcReplicaSetName(), Replica.RoleEnum.MASTER).get(0);
                Replica destReplica = replicaMetaHelper.getRoleReplicaList(null, transferTask.getDestReplicaSetName(), Replica.RoleEnum.MASTER).get(0);
                ReplicaResource srcReplicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), srcReplica.getId(), null);
                ReplicaResource destReplicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), destReplica.getId(), null);
                Map<String, Object> params = new HashMap<>();
                params.put("srcReplicaSetName", transferTask.getSrcReplicaSetName());
                params.put("destReplicaSetName", transferTask.getDestReplicaSetName());
                params.put("srcReplicaId", srcReplica.getId());
                params.put("destReplicaId", destReplica.getId());
                params.put("srcClassCode", transferTask.getSrcClassCode());
                params.put("destClassCode", transferTask.getDestClassCode());
                params.put("srcDiskSizeMB", transferTask.getDestDiskSizeMB());   //已经onlineResize过了，替换节点流程跳过扩容逻辑
                params.put("destDiskSizeMB", transferTask.getDestDiskSizeMB());
                params.put("destStorageType", destReplicaResource.getVolumes().get(0).getStorageType().toString());
                params.put("srcPerformanceLevel", LangUtil.getString(srcReplicaResource.getVolumes().get(0).getPerformanceLevel(), Volume.PerformanceLevelEnum.PL1.toString()));
                params.put("targetPerformanceLevel", LangUtil.getString(destReplicaResource.getVolumes().get(0).getPerformanceLevel(), Volume.PerformanceLevelEnum.PL1.toString()));
                transModule.setParams((JSON.toJSONString(params)));
                return transModule;
            }
        });


        Map<String, ModulePlan> resultMap = new HashMap<>(1);
        for (String stepName : transModuleBuilderMap.keySet()) {
            resultMap.put(stepName, transModuleBuilderMap.get(stepName).build(transferTask));
        }

        return Result.returnSuccessResult(resultMap);
    }

    @ApiOperation(value = "构建高可用升集群版变配执行计划模块")
    @PostMapping("/build_standard_to_cluster_modules_plan")
    public Result<Map<String, ModulePlan>> buildStandardToClusterModulesPlan(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                                       @RequestParam(name = "transInfo") String transInfoStr,
                                                                       @RequestParam(name = "extraParams") String extraParams) throws Exception {
        transInfoStr = StringEscapeUtils.unescapeJava(transInfoStr);
        TransInfo transInfo = JSONObject.parseObject(transInfoStr, TransInfo.class);
        TransParams transParams = JSONObject.parseObject(StringEscapeUtils.unescapeJava(transInfo.getParameter()), TransParams.class);

        Replica masterReplica = mysqlBizService.findMasterReplica(replicaSetName);
        Replica slaveReplica = mysqlBizService.findSlaveReplica(replicaSetName);
        Long destMasterReplicaId = null;
        Long destSlaveReplicaId = null;
        for (TransParam transParam : transParams.getTransParamList()) {
            if (Objects.equals(transParam.getSrcReplicaId(), masterReplica.getId())) {
                destMasterReplicaId = transParam.getDstReplicaId();
            } else if (slaveReplica != null && Objects.equals(transParam.getSrcReplicaId(), slaveReplica.getId())) {
                destSlaveReplicaId = transParam.getDstReplicaId();
            } else {
                log.error("unknown src replicaId {} from trans_list.", transParam.getSrcReplicaId());
                log.error("TransParam {}", transParam);
                throw new NotRetryException("trans_list error");
            }
        }
        Long destMasterReplicaIdFinal = destMasterReplicaId;
        Long destSlaveReplicaIdFinal = destSlaveReplicaId;


        Map<String, TransInfoModuleBuilder> transModuleBuilderMap = new HashMap<>();

        transModuleBuilderMap.put("online_resize_node", trans -> {
            ModulePlan transModule = new ModulePlan();
            boolean isSizeChanged = !Objects.equals(transInfo.getSrcDiskSizeMB(), transInfo.getDestDiskSizeMB());
            boolean isPlChanged = !Objects.equals(transInfo.getSrcPerformanceLevel(), transInfo.getTargetPerformanceLevel());
            if (isSizeChanged || isPlChanged) {
                JSONObject param = JSONObject.parseObject(extraParams);
                param.put("skip_active_ins", true);
                param.put("replicaSetName", replicaSetName);
                transModule.setParams(param.toJSONString());
                transModule.setModuleName(ONLINE_RESIZE_INS);
            } else {
                transModule.setSkipReason("no need online resize");
            }
            return transModule;
        });

        // 备库变更策略
        transModuleBuilderMap.put("modify_slave_pod", trans -> {
            ModulePlan transModule = new ModulePlan();
            Map<String, Object> params;
            if (!replicaSetMetaHelper.isSingleNode(replicaSetName)) {
                Long srcReplicaId = slaveReplica.getId();
                transModule.setTargetType("replica");
                transModule.setReplicaIds(srcReplicaId.toString());
                transModule.setModuleName(UPGRADE_AND_REPLACE_NODE);
                params = taskParamHelper.getUpgradeAndReplaceNodeParam(transInfo, srcReplicaId, destSlaveReplicaIdFinal);
                transModule.setParams((JSON.toJSONString(params)));
            } else {
                transModule.setSkipReason("is single node.");
            }
            return transModule;
        });

        // 主库变更策略
        transModuleBuilderMap.put("modify_master_pod", trans -> {
            ModulePlan transModule = new ModulePlan();
            Map<String, Object> params;
            Long srcReplicaId = masterReplica.getId();
            transModule.setTargetType("replica");
            transModule.setReplicaIds(srcReplicaId.toString());
            transModule.setModuleName(UPGRADE_AND_REPLACE_NODE);
            params = taskParamHelper.getUpgradeAndReplaceNodeParam(transInfo, srcReplicaId, destMasterReplicaIdFinal);
            transModule.setParams((JSON.toJSONString(params)));
            return transModule;
        });

        // 暂停策略
        transModuleBuilderMap.put("pause", transTask -> {
            ModulePlan transModule = new ModulePlan();
            return transModule;
        });

        // 切换策略
        transModuleBuilderMap.put("ha_switch", transTask -> {
            ModulePlan transModule = new ModulePlan();
            return transModule;
        });

        // 备库半跨机时，需要重建主库上的复制
        transModuleBuilderMap.put("rebuild_master_replication", transTask -> {
            ModulePlan transModule = new ModulePlan();
            return transModule;
        });

        Map<String, ModulePlan> resultMap = new HashMap<>(1);
        for (String stepName : transModuleBuilderMap.keySet()) {
            resultMap.put(stepName, transModuleBuilderMap.get(stepName).build(transInfo));
        }

        return Result.returnSuccessResult(resultMap);
    }

    /**
     * 针对需要使用模块的步骤，来构建变配执行计划模块
     *
     * @return
     */
    @ApiOperation(value = "构建cluster变配执行计划模块")
    @PostMapping("/build_cluster_modify_modules_plan")
    public Result<Map<String, ModulePlan>> buildClusterModifyModulesPlan(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                                         @RequestParam(name = "transTaskId") Long transTaskId) throws Exception {
        ReplicaSet replicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), replicaSetName, false);

        TransferTask transferTask = dBaasMetaClient.getDefaultmetaApi().getTransferTask(RequestSession.getRequestId(),
                replicaSetName, LangUtil.getInteger(transTaskId));

        Map<String, TransModuleBuilder> transModuleBuilderMap = new HashMap<>();
        transModuleBuilderMap.put("online_resize_node", new TransModuleBuilder() {
            @Override
            public ModulePlan build(TransferTask transferTask) throws Exception {
                ModulePlan transModule = new ModulePlan();
                Map<String, Object> params = new HashMap<>();
                if (!transferTask.getSrcDiskSizeMB().equals(transferTask.getDestDiskSizeMB())) {
                    params.put("srcDiskSizeMB", transferTask.getSrcDiskSizeMB());
                    params.put("destDiskSizeMB", transferTask.getDestDiskSizeMB());
                    params.put("replicaSetName", replicaSetName);
                }
                if (params.isEmpty()) {
                    transModule.setSkipReason("no need online resize");
                } else {
                    transModule.setParams(JSON.toJSONString(params));
                }
                return transModule;
            }
        });

        transModuleBuilderMap.put("replace_slave_node", new TransModuleBuilder() {
            @Override
            public ModulePlan build(TransferTask transferTask) throws ApiException {
                ModulePlan transModule = new ModulePlan();
                Replica srcReplica = replicaMetaHelper.getRoleReplicaList(null, transferTask.getSrcReplicaSetName(), Replica.RoleEnum.SLAVE).get(0);
                Replica destReplica = replicaMetaHelper.getRoleReplicaList(null, transferTask.getDestReplicaSetName(), Replica.RoleEnum.SLAVE).get(0);
                ReplicaResource srcReplicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), srcReplica.getId(), null);
                ReplicaResource destReplicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), destReplica.getId(), null);
                Map<String, Object> params = new HashMap<>();
                params.put("srcReplicaSetName", transferTask.getSrcReplicaSetName());
                params.put("destReplicaSetName", transferTask.getDestReplicaSetName());
                params.put("srcClassCode", transferTask.getSrcClassCode());
                params.put("destClassCode", transferTask.getDestClassCode());
                params.put("srcDiskSizeMB", transferTask.getDestDiskSizeMB());  //已经onlineResize过了，替换节点流程跳过扩容逻辑
                params.put("destDiskSizeMB", transferTask.getDestDiskSizeMB());
                params.put("destStorageType", destReplicaResource.getVolumes().get(0).getStorageType().toString());
                params.put("srcPerformanceLevel", LangUtil.getString(srcReplicaResource.getVolumes().get(0).getPerformanceLevel(), Volume.PerformanceLevelEnum.PL1.toString()));
                params.put("targetPerformanceLevel", LangUtil.getString(destReplicaResource.getVolumes().get(0).getPerformanceLevel(), Volume.PerformanceLevelEnum.PL1.toString()));
                transModule.setModuleName(UPGRADE_AND_REPLACE_NODE_FOR_CLUSTER);
                params.put("transTaskId", transTaskId);
                params.put("nodeRole", "slave");

                transModule.setParams((JSON.toJSONString(params)));
                return transModule;
            }
        });

        transModuleBuilderMap.put("replace_master_node", new TransModuleBuilder() {
            @Override
            public ModulePlan build(TransferTask transferTask) throws ApiException {
                ModulePlan transModule = new ModulePlan();
                Replica srcReplica = replicaMetaHelper.getRoleReplicaList(null, transferTask.getSrcReplicaSetName(), Replica.RoleEnum.MASTER).get(0);
                Replica destReplica = replicaMetaHelper.getRoleReplicaList(null, transferTask.getDestReplicaSetName(), Replica.RoleEnum.MASTER).get(0);
                ReplicaResource srcReplicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), srcReplica.getId(), null);
                ReplicaResource destReplicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), destReplica.getId(), null);
                Map<String, Object> params = new HashMap<>();
                params.put("srcReplicaSetName", transferTask.getSrcReplicaSetName());
                params.put("destReplicaSetName", transferTask.getDestReplicaSetName());
                params.put("srcClassCode", transferTask.getSrcClassCode());
                params.put("destClassCode", transferTask.getDestClassCode());
                params.put("srcDiskSizeMB", transferTask.getDestDiskSizeMB());   //已经onlineResize过了，替换节点流程跳过扩容逻辑
                params.put("destDiskSizeMB", transferTask.getDestDiskSizeMB());
                params.put("destStorageType", destReplicaResource.getVolumes().get(0).getStorageType().toString());
                params.put("srcPerformanceLevel", LangUtil.getString(srcReplicaResource.getVolumes().get(0).getPerformanceLevel(), Volume.PerformanceLevelEnum.PL1.toString()));
                params.put("targetPerformanceLevel", LangUtil.getString(destReplicaResource.getVolumes().get(0).getPerformanceLevel(), Volume.PerformanceLevelEnum.PL1.toString()));
                transModule.setModuleName(UPGRADE_AND_REPLACE_NODE_FOR_CLUSTER);
                params.put("transTaskId", transTaskId);
                params.put("nodeRole", "master");
                transModule.setParams((JSON.toJSONString(params)));
                return transModule;
            }
        });


        Map<String, ModulePlan> resultMap = new HashMap<>(1);
        for (String stepName : transModuleBuilderMap.keySet()) {
            resultMap.put(stepName, transModuleBuilderMap.get(stepName).build(transferTask));
        }

        return Result.returnSuccessResult(resultMap);
    }

    @ApiOperation(value = "构建节点变配的任务参数")
    @PostMapping("/build_modify_ins_node_modules_plan")
    public Result<Map<String, ModulePlan>> buildModifyInsNodePlan(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                                  @RequestParam(name = "transTaskId") Integer transTaskId,
                                                                  @RequestParam(name = "srcPerformanceLevel", required = false) String srcPerformanceLevel,
                                                                  @RequestParam(name = "targetPerformanceLevel", required = false) String targetPerformanceLevel) throws Exception {
        TransferTask transferTask = dBaasMetaClient.getDefaultmetaApi().getTransferTask(RequestSession.getRequestId(), replicaSetName, transTaskId);

        Map<String, TransModuleBuilder> transModuleBuilderMap = new HashMap<>();
        Function<TransferTask, Map<String, Object>> extractResourceParams = task -> {
            try {
                Map<String, List<Map<String, Object>>> transParameter = JSON.parseObject(task.getParameter(), Map.class);
                Map<String, Object> params = new HashMap<>();
                if (transParameter.containsKey("resourceParam")) {
                    List<Map<String, Object>> resourceParamList = transParameter.get("resourceParam");
                    if (!resourceParamList.isEmpty()) {
                        Map<String, Object> resourceParam = resourceParamList.get(0);
                        if (resourceParam != null) {
                            params.put("resourceGuaranteeLevel", LangUtil.getString(resourceParam.get("resourceGuaranteeLevel")));
                            params.put("resourceGuaranteeLevelType", LangUtil.getString(resourceParam.get("resourceGuaranteeLevelType")));
                            params.put("resourceGuaranteeBackUpLevels", LangUtil.getString(resourceParam.get("resourceGuaranteeBackUpLevels")));
                        }
                    }
                }
                log.info("extractResourceParams:{}", JSON.toJSONString(params));
                return params;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        };
        transModuleBuilderMap.put("online_resize_node", new TransModuleBuilder() {
            @Override
            public ModulePlan build(TransferTask transferTask) throws Exception {
                // 根据
                ModulePlan transModule = new ModulePlan();
                Map<String, Object> params = new HashMap<>();
                if (!transferTask.getSrcDiskSizeMB().equals(transferTask.getDestDiskSizeMB())) {
                    params.put("srcDiskSizeMB", transferTask.getSrcDiskSizeMB());
                    params.put("destDiskSizeMB", transferTask.getDestDiskSizeMB());
                }
                if (StringUtils.isNotBlank(targetPerformanceLevel) && !srcPerformanceLevel.equals(targetPerformanceLevel)) {
                    params.put("targetPerformanceLevel", targetPerformanceLevel);
                }
                if (params.isEmpty()) {
                    transModule.setSkipReason("no need to do online resize");
                } else {
                    params.put("replicaSetName", replicaSetName);
                    transModule.setParams(JSON.toJSONString(params));
                    transModule.setModuleName(ONLINE_RESIZE_NODE);
                }
                return transModule;
            }
        });
        transModuleBuilderMap.put("modify_ins_slave_node", new TransModuleBuilder() {
            @Override
            public ModulePlan build(TransferTask transferTask) throws Exception {
                // 根据transferTask中parameter参数记录的trans对应关系确定replace的节点
                List<Replica> srcReplicas = dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(RequestSession.getRequestId(), transferTask.getSrcReplicaSetName(), null, null, null, null).getItems();
                Map<Long, Replica> id2ReplicaMap = new HashMap<>();
                srcReplicas.forEach(rp -> id2ReplicaMap.put(rp.getId(), rp));

                ModulePlan transModule = new ModulePlan();
                TransParams transParams = JSONObject.parseObject(StringEscapeUtils.unescapeJava(transferTask.getParameter()), TransParams.class);

                // 从 parameter中获取 srcReplicaId，通过这个部分筛选出当前实例的 slave 节点
                List<String> changedReplicaIds = new ArrayList<>();
                for (TransParam transParam : transParams.getTransParamList()) {
                    Replica srcReplica = id2ReplicaMap.get(transParam.getSrcReplicaId());
                    if (Objects.equals(srcReplica.getRole(), Replica.RoleEnum.SLAVE)) {
                        changedReplicaIds.add(srcReplica.getId().toString());
                    }
                }

                Map<String, Object> params = extractResourceParams.apply(transferTask);
                if (!changedReplicaIds.isEmpty()) {
                    params.put("srcReplicaSetName", transferTask.getSrcReplicaSetName());
                    params.put("destReplicaSetName", transferTask.getDestReplicaSetName());
                    params.put("srcDiskSizeMB", transferTask.getDestDiskSizeMB());   //已经onlineResize过了，替换节点流程跳过扩容逻辑
                    params.put("destDiskSizeMB", transferTask.getDestDiskSizeMB());
                    params.put("transTaskId", transTaskId);
                    params.put("nodeRole", "slave");
                }
                if (params.isEmpty()) {
                    transModule.setSkipReason("No need to replace slave node!");
                }
                else {
                    transModule.setParams((JSON.toJSONString(params)));
                    transModule.setReplicaIds(StringUtils.join(changedReplicaIds, ","));
                    transModule.setModuleName(UPGRADE_AND_REPLACE_NODE_FOR_CLUSTER);
                }
                return transModule;
            }
        });

        transModuleBuilderMap.put("modify_ins_master_node", new TransModuleBuilder() {
            @Override
            public ModulePlan build(TransferTask transferTask) throws Exception {
                List<Replica> srcReplicas = dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(RequestSession.getRequestId(), transferTask.getSrcReplicaSetName(), null, null, null, null).getItems();
                Map<Long, Replica> id2ReplicaMap = new HashMap<>();
                srcReplicas.forEach(rp -> id2ReplicaMap.put(rp.getId(), rp));

                ModulePlan transModule = new ModulePlan();
                TransParams transParams = JSONObject.parseObject(StringEscapeUtils.unescapeJava(transferTask.getParameter()), TransParams.class);

                List<String> changedReplicaIds = new ArrayList<>();
                for (TransParam transParam : transParams.getTransParamList()) {
                    Replica srcReplica = id2ReplicaMap.get(transParam.getSrcReplicaId());
                    if (Objects.equals(srcReplica.getRole(), Replica.RoleEnum.MASTER)) {
                        changedReplicaIds.add(srcReplica.getId().toString());
                        break;  // only one master
                    }
                }

                Map<String, Object> params = extractResourceParams.apply(transferTask);
                if (!changedReplicaIds.isEmpty()) {
                    // 跨机不设置目标规格，本地也不设置，本地升降配会在 build_cluster_local_upgrade_Replica 中使用trans_list的数据
                    params.put("srcReplicaSetName", transferTask.getSrcReplicaSetName());
                    params.put("destReplicaSetName", transferTask.getDestReplicaSetName());
                    params.put("srcDiskSizeMB", transferTask.getDestDiskSizeMB());   //已经onlineResize过了，替换节点流程跳过扩容逻辑
                    params.put("destDiskSizeMB", transferTask.getDestDiskSizeMB());
                    params.put("transTaskId", transTaskId);
                    params.put("nodeRole", "master");
                }
                if (params.isEmpty()) {
                    transModule.setSkipReason("No need to replace master node!");
                }
                else {
                    transModule.setParams((JSON.toJSONString(params)));
                    transModule.setReplicaIds(StringUtils.join(changedReplicaIds, ","));
                    transModule.setModuleName(UPGRADE_AND_REPLACE_NODE_FOR_CLUSTER);
                }
                return transModule;
            }
        });

        Map<String, ModulePlan> resultMap = new HashMap<>();
        for (String stepName : transModuleBuilderMap.keySet()) {
            resultMap.put(stepName, transModuleBuilderMap.get(stepName).build(transferTask));
        }

        return Result.returnSuccessResult(resultMap);
    }


    @ApiOperation(value = "构建节点迁移的任务参数")
    @PostMapping("/build_migrate_ins_node_modules_plan")
    public Result<Map<String, ModulePlan>> buildMigrateInsNodePlan(@RequestParam(name = "srcReplicaSetName") String srcReplicaSetName,
                                                                   @RequestParam(name = "destReplicaSetName") String destReplicaSetName,
                                                                   @RequestParam(name = "transTaskId") Integer transTaskId,
                                                                   @RequestParam(name = "replicaMapping") String replicaMapping) throws Exception {
        Map<String, MigrateCLusterNodesModuleBuilder> modulesPlan = new HashMap<>();
        Map<String, String> replicaMappingMap = StringUtil.mapStringToMap(replicaMapping);
        log.info("replicaMapping:{}", JSON.toJSONString(replicaMappingMap));

        modulesPlan.put("migrate_ins_slave_node", new MigrateCLusterNodesModuleBuilder() {
            @Override
            public ModulePlan build(String srcReplicaSetName, String destReplicaSetName) throws Exception {
                List<Replica> srcReplicas = dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(RequestSession.getRequestId(), srcReplicaSetName, null, null, null, null).getItems();
                List<Replica> destReplicas = dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(RequestSession.getRequestId(), destReplicaSetName, null, null, null, null).getItems();
                if (srcReplicas == null || srcReplicas.isEmpty() || destReplicas == null || destReplicas.isEmpty()) {
                    log.error("get srcReplicas or destReplicas failed. srcReplicas is {}, destReplicas is {} ", srcReplicas, destReplicas);
                    throw new NotRetryException("get srcReplicas failed");
                }

                // 从 parameter中获取 srcReplicaId，通过这个部分筛选出当前实例的 slave 节点
                List<String> changedReplicaIds = new ArrayList<>();
                String srcMasterReplicaId = null;
                for (Replica srcReplica : srcReplicas) {
                    if (replicaMappingMap.containsKey(srcReplica.getId().toString())) {
                        changedReplicaIds.add(srcReplica.getId().toString());
                        if (Objects.equals(srcReplica.getRole(), Replica.RoleEnum.MASTER)) {
                            throw new NotRetryException("srcReplica is master, can not migrate");
                        }
                    }
                    if (Objects.equals(srcReplica.getRole(), Replica.RoleEnum.MASTER)) {
                        srcMasterReplicaId = srcReplica.getId().toString();
                    }
                }
                ModulePlan migrateModule = new ModulePlan();

                Map<String, Object> params = new HashMap<>();
                if (!changedReplicaIds.isEmpty()) {
                    params.put("srcReplicaSetName", srcReplicaSetName);
                    params.put("destReplicaSetName", destReplicaSetName);
                    params.put("replicaMapping", replicaMapping);
                    params.put("transTaskId", transTaskId);
                    params.put("srcMasterReplicaId", srcMasterReplicaId);
                }
                if (params.isEmpty()) {
                    migrateModule.setSkipReason("No need to replace slave node!");
                }
                else {
                    migrateModule.setParams((JSON.toJSONString(params)));
                    migrateModule.setReplicaIds(StringUtils.join(changedReplicaIds, ","));
                    migrateModule.setModuleName(REPLACE_NODE_FOR_CLUSTER_MIGRATION);
                }
                return migrateModule;
            }
        });

        Map<String, ModulePlan> resultMap = new HashMap<>();
        for (String stepName : modulesPlan.keySet()) {
            resultMap.put(stepName, modulesPlan.get(stepName).build(srcReplicaSetName, destReplicaSetName));
        }

        return Result.returnSuccessResult(resultMap);
    }

    @ApiOperation(value = "构建重搭备库的恢复任务")
    @PostMapping("/rebuild_slave_modules_plan")
    public Result<Map<String, ModulePlan>> buildRebuildSlavePlan(@RequestParam(name = "srcReplicaSetName") String srcReplicaSetName,
                                                                 @RequestParam(name = "destReplicaSetName") String destReplicaSetName,
                                                                 @RequestParam(name = "backupSetId") Long backupSetId) throws Exception {
        Map<String, RebuildSlaveBuilder> modulesPlan = new HashMap<>();

        modulesPlan.put("append_binlog", new RebuildSlaveBuilder() {
            @Override
            public ModulePlan build(String srcReplicaSetName, String destReplicaSetName, Long backupSetId) throws Exception {
                ReplicaSet srcReplicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), srcReplicaSetName, false);
                // 只读实例使用主实例的备份集
                if (replicaMetaHelper.isReadIns(srcReplicaSet)) {
                    srcReplicaSetName = srcReplicaSet.getPrimaryInsName();
                }
                ModulePlan appendLogModule = new ModulePlan();
                BinlogBackupPolicyResponse backupPolicyResponse = bifrostBaseService.getBinlogBackupPolicy(srcReplicaSetName);
                if (Objects.equals(backupPolicyResponse.getUploadWay(), BackupConsts.BINLOG_NO_UPLOAD)) {
                    appendLogModule.setSkipReason("binlog policy is not upload.");
                } else {
                    GetBackupSetResponse getBackupSetResponse = backupBaseService.getBackupSet(srcReplicaSetName, backupSetId);
                    if (!BackupConsts.BACKUP_SET_STATUS_OK.equalsIgnoreCase(getBackupSetResponse.getStatus())) {
                        throw new NotRetryException(String.format("The backup set state is unavailable %s", getBackupSetResponse));
                    }
                    GetBackupSetResponse.SlaveStatus slaveStatus = getBackupSetResponse.getSlaveStatusObj();
                    Long backupReplicaId = slaveStatus.getBINLOGHINSID();
                    List<Replica> replicaList  = replicaSetMetaHelper.getReplicas(srcReplicaSetName);
                    long cnt = replicaList.stream().filter(v -> Objects.equals(v.getId(), backupReplicaId)).count();
                    if (cnt == 0) {
                        log.info("backupReplicaId not belong current's replicaSet");
                    }
                    boolean hasPurged = bifrostBaseService.binlogFromBackupSetHasPurged(srcReplicaSetName, slaveStatus.getBINLOGFILE(), slaveStatus.getBINLOGHINSID());
                    Map<String, Object> params = new HashMap<>();
                    if (cnt == 0 || hasPurged) {
                        params.put("srcReplicaSetName", srcReplicaSetName);
                        params.put("destReplicaSetName", destReplicaSetName);
                        params.put("srcReplicaId", slaveStatus.getBINLOGHINSID());
                        params.put("backupReplicaId", slaveStatus.getBINLOGHINSID());
                        params.put("destReplicaId", mysqlBizService.findSlaveReplica(destReplicaSetName).getId());
                        // 指定恢复到最新的binlog
                        params.put("restoreToNewest", true);
                        params.put("restoreTime", DateTimeUtils.MAX_TIME);
                        params.put("startBinlogFile", slaveStatus.getBINLOGFILE());
                        log.info("prepare to append binlog");
                    } else {
                        appendLogModule.setSkipReason("binlog is not purge.");
                        log.info("binlog is not purge, skip to append binlog");
                    }
                    appendLogModule.setModuleName("append_binlog");
                    appendLogModule.setParams((JSON.toJSONString(params)));
                }
                return appendLogModule;
            }
        });
        Map<String, ModulePlan> resultMap = new HashMap<>();
        for (String stepName : modulesPlan.keySet()) {
            resultMap.put(stepName, modulesPlan.get(stepName).build(srcReplicaSetName, destReplicaSetName, backupSetId));
        }
        return Result.returnSuccessResult(resultMap);
    }

    @ApiOperation(value = "只读实例创建/重搭时的Online Resize任务")
    @PostMapping("/build_read_ins_online_resize_plan")
    public Result<Map<String, ModulePlan>> buildReadInsOnlineResizePlan(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                                        @RequestParam(name = "performanceLevel") String performanceLevel,
                                                                        @RequestParam(name = "backupSetId", required = false) String backupSetId) throws Exception{
        ReplicaSet replicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(),
                replicaSetName, false);

        Map<String, MysqlReadInsOnlineResizeModuleBuilder> models = new HashMap<>();
        models.put("online_resize", new MysqlReadInsOnlineResizeModuleBuilder() {
            @Override
            public ModulePlan build(ReplicaSet replicaSet, String performanceLevel) throws Exception {
                ModulePlan modulePlan = new ModulePlan();
                Map<String, Object> params = new HashMap<>();
                if (replicaSet.getInsType() == ReplicaSet.InsTypeEnum.READONLY) {
                    ReplicaSet primaryReplicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(),
                            replicaSet.getPrimaryInsName(), false);

                    // 主实例磁盘小于只读实例时，只读实例需要做在线扩容
                    if (primaryReplicaSet.getDiskSizeMB() < replicaSet.getDiskSizeMB()) {
                        params.put("destDiskSizeMB", replicaSet.getDiskSizeMB());
                        params.put("targetPerformanceLevel", performanceLevel);
                    }
                }
                if (NumberUtils.isDigits(backupSetId)) {
                    GetBackupSetResponse backupSetResponse = backupBaseService.getBackupSet(replicaSetName, LangUtil.getLong(backupSetId));
                    Long backupSetSizeMB = backupSetResponse.getBackupSetSize() / 1024;
                    if (replicaSet.getDiskSizeMB() > backupSetSizeMB) {
                        log.info("backupSet size [{}] less than replicaSet diskSize [{}]", backupSetSizeMB, replicaSet.getDiskSizeMB());
                        params.put("destDiskSizeMB", replicaSet.getDiskSizeMB());
                    }
                }
                if (params.isEmpty()) {
                    modulePlan.setSkipReason("no need online resize");
                } else {
                    modulePlan.setParams(JSON.toJSONString(params));
                }
                return modulePlan;
            }
        });
        Map<String, ModulePlan> resultMap = new HashMap<>(1);
        for (String stepName : models.keySet()) {
            resultMap.put(stepName, models.get(stepName).build(replicaSet, performanceLevel));
        }
        return Result.returnSuccessResult(resultMap);

    }

    @ApiOperation(value = "迁移同时变配时的Online Resize任务")
    @PostMapping("/build_migrate_online_resize_plan")
    public Result<Map<String, ModulePlan>> buildMigrateOnlineResizePlan(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                                        @RequestParam(name = "diskSizeMB", required = false, defaultValue = "0") Integer diskSizeMB,
                                                                        @RequestParam(name = "performanceLevel", required = false) String performanceLevel) throws Exception {
        ReplicaSet replicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(),
                replicaSetName, false);

        Map<String, MysqlMigrateOnlineResizeModuleBuilder> models = new HashMap<>();
        models.put("online_resize", new MysqlMigrateOnlineResizeModuleBuilder() {
            @Override
            public ModulePlan build(ReplicaSet replicaSet, Integer diskSizeMB, String performanceLevel) throws Exception {
                ModulePlan modulePlan = new ModulePlan();
                Map<String, Object> params = new HashMap<>();

                if (replicaSet.getInsType() == ReplicaSet.InsTypeEnum.READONLY) {
                    ReplicaSet primaryReplicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(),
                            replicaSet.getPrimaryInsName(), false);

                    // 主实例磁盘小于只读实例时，只读实例需要做在线扩容
                    diskSizeMB = Math.max(diskSizeMB, replicaSet.getDiskSizeMB());
                    if (primaryReplicaSet.getDiskSizeMB() < diskSizeMB) {
                        params.put("destDiskSizeMB", diskSizeMB);
                    }
                } else if (diskSizeMB > 0) {
                    params.put("destDiskSizeMB", diskSizeMB);
                }

                if (performanceLevel != null && !performanceLevel.isEmpty()) {
                    params.put("targetPerformanceLevel", performanceLevel);
                }

                if (params.isEmpty()) {
                    modulePlan.setSkipReason("no need online resize");
                } else {
                    modulePlan.setParams(JSON.toJSONString(params));
                }
                return modulePlan;
            }
        });
        Map<String, ModulePlan> resultMap = new HashMap<>(1);
        for (String stepName : models.keySet()) {
            resultMap.put(stepName, models.get(stepName).build(replicaSet, diskSizeMB, performanceLevel));
        }
        return Result.returnSuccessResult(resultMap);

    }

    @ApiOperation(value = "Online Resize通用任务")
    @PostMapping("/build_online_resize_plan")
    public Result<Map<String, ModulePlan>> buildOnlineResizePlan(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                                        @RequestParam(name = "diskSizeMB", required = false, defaultValue = "0") Integer diskSizeMB,
                                                                        @RequestParam(name = "performanceLevel", required = false) String performanceLevel) throws Exception {
        ReplicaSet replicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(),
                replicaSetName, false);

        Map<String, MysqlOnlineResizeModuleBuilder> models = new HashMap<>();
        models.put("online_resize", new MysqlOnlineResizeModuleBuilder() {
            @Override
            public ModulePlan build(ReplicaSet replicaSet, Integer diskSizeMB, String performanceLevel) throws Exception {
                ModulePlan modulePlan = new ModulePlan();
                Map<String, Object> params = new HashMap<>();

                if (replicaSet.getInsType() == ReplicaSet.InsTypeEnum.READONLY) {
                    ReplicaSet primaryReplicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), replicaSet.getPrimaryInsName(), false);

                    // 主实例磁盘小于只读实例时，只读实例需要做在线扩容
                    diskSizeMB = Math.max(diskSizeMB, replicaSet.getDiskSizeMB());
                    if (primaryReplicaSet.getDiskSizeMB() < diskSizeMB) {
                        params.put("destDiskSizeMB", diskSizeMB);
                    }
                } else if (diskSizeMB > 0) {
                    params.put("destDiskSizeMB", diskSizeMB);
                }

                if (performanceLevel != null && !performanceLevel.isEmpty()) {
                    params.put("targetPerformanceLevel", performanceLevel);
                }

                if (params.isEmpty()) {
                    modulePlan.setSkipReason("no need online resize");
                } else {
                    modulePlan.setParams(JSON.toJSONString(params));
                }
                return modulePlan;
            }
        });
        Map<String, ModulePlan> resultMap = new HashMap<>(1);
        for (String stepName : models.keySet()) {
            resultMap.put(stepName, models.get(stepName).build(replicaSet, diskSizeMB, performanceLevel));
        }
        return Result.returnSuccessResult(resultMap);

    }

    @ApiOperation(value = "构造备库重搭不同角色需要的参数")
    @PostMapping("/build_args_for_modify_task")
    public Result<Map<String, Object>> buildModifyTaskArgs(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                           @RequestParam(name = "transTaskId") Long transTaskId,
                                                           @RequestParam(name = "extraParams") String extraParams) throws Exception {

        TransferTask transferTask = dBaasMetaClient.getDefaultmetaApi().getTransferTask(RequestSession.getRequestId(),
                replicaSetName, LangUtil.getInteger(transTaskId));

        Map<String, Object> tmpExtraParams = JSON.parseObject(extraParams, Map.class);
        Object srcReplicaSetName = transferTask.getSrcReplicaSetName();
        Object destReplicaSetName = transferTask.getDestReplicaSetName();
        if (srcReplicaSetName == null || destReplicaSetName == null){
            throw new Exception("srcReplicaSetName or destReplicaSetName can not be null");
        }

        //获取实例replica信息
        ReplicaListResult listReplicasInReplicaSet = dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(
                RequestSession.getRequestId(), String.valueOf(srcReplicaSetName), null, null, null, null);
        List<Replica> srcReplicas = listReplicasInReplicaSet.getItems();
        ReplicaListResult tmplistReplicasInReplicaSet = dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(
                RequestSession.getRequestId(), String.valueOf(destReplicaSetName),null, null, null, null);
        List<Replica> tmpReplicas = tmplistReplicasInReplicaSet.getItems();

        Optional<Replica> masterReplica = srcReplicas.stream().filter(r -> Objects.equals(r.getRole(), Replica.RoleEnum.MASTER)).findFirst();
        Optional<Replica> slaveReplica = srcReplicas.stream().filter(r -> Objects.equals(r.getRole(), Replica.RoleEnum.SLAVE)).findFirst();
        Optional<Replica> tmpSlaveReplica = tmpReplicas.stream().filter(r -> Objects.equals(r.getRole(), Replica.RoleEnum.SLAVE)).findFirst();
        Optional<Replica> tmpMasterReplica = tmpReplicas.stream().filter(r -> Objects.equals(r.getRole(), Replica.RoleEnum.MASTER)).findFirst();

        if (!masterReplica.isPresent() || !slaveReplica.isPresent() ||
                !tmpSlaveReplica.isPresent() || !tmpMasterReplica.isPresent()) {
            throw new Exception("ReplicaSet not found master or slave replica info");
        }

        Long slaveDestReplicaId = tmpSlaveReplica.get().getId();
        Long slaveSrcReplicaId = slaveReplica.get().getId();
        Long masterDestReplicaId = tmpMasterReplica.get().getId();
        Long masterSrcReplicaId = masterReplica.get().getId();

        if (StringUtils.isEmpty(masterReplica.get().getHostName()) || StringUtils.isEmpty(tmpSlaveReplica.get().getHostName())){
            throw new Exception("replica not find host name");
        } else {
            if (StringUtils.equalsIgnoreCase(masterReplica.get().getHostName(), tmpSlaveReplica.get().getHostName())){
                // src  master与tmp slave为统一节点，则src备库重搭使用tmp的master
                log.info("src master hostname equal dest slave hostname, use tmp instance master for rebuild, " +
                                "replica meta: tmpReplicaName: {}, srcMaster: {}, tmpSlave id: {}", destReplicaSetName,
                        masterReplica.get().getHostName(), tmpSlaveReplica.get().getHostName());

                slaveDestReplicaId = tmpMasterReplica.get().getId();
                masterDestReplicaId = tmpSlaveReplica.get().getId();
                log.info("use rebuild slave replica id: {}, use master replica id: {} ", slaveDestReplicaId, masterDestReplicaId);
            }
        }

        //这里重新设置master， slave的id信息
        tmpExtraParams.put("slaveSrcReplicaId", slaveSrcReplicaId);
        tmpExtraParams.put("slaveDestReplicaId", slaveDestReplicaId);
        tmpExtraParams.put("masterSrcReplicaId", masterSrcReplicaId);
        tmpExtraParams.put("masterDestReplicaId", masterDestReplicaId);

        Map<String, Object> slaveParams = new HashMap<>(tmpExtraParams);
        slaveParams.put("role", "slave");
        slaveParams.put("destReplicaId", slaveDestReplicaId);
        slaveParams.put("srcReplicaId", slaveSrcReplicaId);
        if (Replica.StorageTypeEnum.CLOUD_ESSD == slaveReplica.get().getStorageType()
            ||Replica.StorageTypeEnum.CLOUD_AUTO == slaveReplica.get().getStorageType()){
            slaveParams.put("skip", "skip");
        }

        Map<String, Object> masterParams = new HashMap<>(tmpExtraParams);
        masterParams.put("role", "master");
        masterParams.put("destReplicaId", masterDestReplicaId);
        masterParams.put("srcReplicaId", masterSrcReplicaId);
        if (Replica.StorageTypeEnum.CLOUD_ESSD == masterReplica.get().getStorageType()
                ||Replica.StorageTypeEnum.CLOUD_AUTO == slaveReplica.get().getStorageType()){
            masterParams.put("skip","skip");
        }

        ModulePlan slaveModulePlan = new ModulePlan();
        slaveModulePlan.setParams(JSON.toJSONString(slaveParams));
        tmpExtraParams.put("slaveParams", slaveModulePlan);
        ModulePlan masterModulePlan = new ModulePlan();
        masterModulePlan.setParams(JSON.toJSONString(masterParams));
        tmpExtraParams.put("masterParams", masterModulePlan);

        return Result.returnSuccessResult(tmpExtraParams);
    }

    @ApiOperation(value = "构建只读实例追加Binlog计划")
    @PostMapping("/build_read_ins_append_binlog_modules_plan")
    public Result<Map<String, ModulePlan>> buildReadInsAppendBinLogModulesPlan
            (@RequestParam(name = "srcReplicaSetName") String srcReplicaSetName,
             @RequestParam(name = "destReplicaSetName") String destReplicaSetName,
             @RequestParam(name = "backupSetId") Long backupSetId) throws Exception {
        Map<String, ReadInsAppendBinlogBuilder> modulesPlan = new HashMap<>();

        modulesPlan.put("append_binlog", new ReadInsAppendBinlogBuilder() {
            @Override
            public ModulePlan build(String srcReplicaSetName, String destReplicaSetName, Long backupSetId) throws Exception {
                ModulePlan appendLogModule = new ModulePlan();
                BinlogBackupPolicyResponse backupPolicyResponse = bifrostBaseService.getBinlogBackupPolicy(srcReplicaSetName);
                if (Objects.equals(backupPolicyResponse.getUploadWay(), BackupConsts.BINLOG_NO_UPLOAD)) {
                    appendLogModule.setSkipReason("binlog policy is not upload.");
                } else {
                    GetBackupSetResponse getBackupSetResponse = backupBaseService.getBackupSet(srcReplicaSetName, backupSetId);
                    if (!BackupConsts.BACKUP_SET_STATUS_OK.equalsIgnoreCase(getBackupSetResponse.getStatus())) {
                        throw new NotRetryException(String.format("The backup set state is unavailable %s", getBackupSetResponse));
                    }
                    GetBackupSetResponse.SlaveStatus slaveStatus = getBackupSetResponse.getSlaveStatusObj();
                    boolean hasPurged = bifrostBaseService.binlogFromBackupSetHasPurged(srcReplicaSetName, slaveStatus.getBINLOGFILE(), slaveStatus.getBINLOGHINSID());
                    if (hasPurged) {
                        Map<String, Object> params = new HashMap<>();
                        params.put("srcReplicaSetName", srcReplicaSetName);
                        params.put("destReplicaSetName", destReplicaSetName);
                        params.put("srcReplicaId", slaveStatus.getBINLOGHINSID());
                        params.put("destReplicaId", mysqlBizService.findMasterReplica(destReplicaSetName).getId());
                        params.put("restoreToNewest", true);
                        params.put("restoreTime", DateTimeUtils.MAX_TIME);
                        params.put("startBinlogFile", slaveStatus.getBINLOGFILE());
                        appendLogModule.setModuleName("append_binlog");
                        appendLogModule.setParams((JSON.toJSONString(params)));
                    } else {
                        appendLogModule.setSkipReason("binlog is not purged.");
                    }
                }
                return appendLogModule;
            }
        });
        Map<String, ModulePlan> resultMap = new HashMap<>(1);
        for (String stepName : modulesPlan.keySet()) {
            resultMap.put(stepName, modulesPlan.get(stepName).build(srcReplicaSetName, destReplicaSetName, backupSetId));
        }
        return Result.returnSuccessResult(resultMap);
    }

    @ApiOperation(value = "构建只读实例追加Binlog计划——适配DBS接口备份的备份集")
    @PostMapping("/build_read_ins_append_binlog_modules_plan_from_dbs")
    public Result<Map<String, ModulePlan>> buildReadInsAppendBinLogModulesPlanFromDBS
            (@RequestParam(name = "srcReplicaSetName") String srcReplicaSetName,
             @RequestParam(name = "destReplicaSetName") String destReplicaSetName,
             @RequestParam(name = "backupSetId") String backupSetId) throws Exception {
        Map<String, ReadInsAppendBinlogBuilder> modulesPlan = new HashMap<>();

        modulesPlan.put("append_binlog", new ReadInsAppendBinlogBuilder() {
            @Override
            public ModulePlan build(String srcReplicaSetName, String destReplicaSetName, Long backupSetId) throws Exception {
                ModulePlan appendLogModule = new ModulePlan();
                BinlogBackupPolicyResponse backupPolicyResponse = bifrostBaseService.getBinlogBackupPolicy(srcReplicaSetName);
                if (Objects.equals(backupPolicyResponse.getUploadWay(), BackupConsts.BINLOG_NO_UPLOAD)) {
                    appendLogModule.setSkipReason("binlog policy is not upload.");
                } else {

                    DescribeRestoreBackupSetParam caller = dbsGateWayService.describeRestoreBackupSetBuilder(srcReplicaSetName, backupSetId.toString());
                    DescribeRestoreBackupSetResponse getBackupSetResponse = dbsGateWayService.describeRestoreBackupSet(caller);

                    if (!BackupConsts.BACKUP_SET_STATUS_OK.equalsIgnoreCase(getBackupSetResponse.getBackupSetInfo().getBackupStatus())) {
                        throw new NotRetryException(String.format("The backup set state is unavailable %s", getBackupSetResponse));
                    }
                    DescribeRestoreBackupSetResponse.ExtraInfo extraInfo = getBackupSetResponse.getBackupSetInfo().getExtraInfo();
                    boolean hasPurged = bifrostBaseService.binlogFromBackupSetHasPurged(srcReplicaSetName, extraInfo.getBINLOG_FILE(), extraInfo.getBINLOG_HINSID());
                    if (hasPurged) {
                        Map<String, Object> params = new HashMap<>();
                        params.put("srcReplicaSetName", srcReplicaSetName);
                        params.put("destReplicaSetName", destReplicaSetName);
                        params.put("srcReplicaId", extraInfo.getBINLOG_HINSID());
                        params.put("destReplicaId", mysqlBizService.findMasterReplica(destReplicaSetName).getId());
                        params.put("restoreToNewest", true);
                        params.put("restoreTime", DateTimeUtils.MAX_TIME);
                        params.put("startBinlogFile", extraInfo.getBINLOG_FILE());
                        appendLogModule.setModuleName("append_binlog");
                        appendLogModule.setParams((JSON.toJSONString(params)));
                    } else {
                        appendLogModule.setSkipReason("binlog is not purged.");
                    }
                }
                return appendLogModule;
            }
        });
        Map<String, ModulePlan> resultMap = new HashMap<>(1);
        for (String stepName : modulesPlan.keySet()) {
            resultMap.put(stepName, modulesPlan.get(stepName).build(srcReplicaSetName, destReplicaSetName, Long.parseLong(backupSetId)));
        }
        return Result.returnSuccessResult(resultMap);
    }


    /**
     * 构建在线扩容先后顺序
     */
    @ApiOperation(value = "构建在线扩容先后顺序")
    @PostMapping("/build_online_resize_modules_plan")
    public Result<Map<String, ModulePlan>> buildReadInsRestoreModulesPlan(
            @RequestParam(name = "replicaSetName") String replicaSetName) throws Exception {

        Long backupId = backupBaseService.getBackupRunningJob(replicaSetName);
        Long hostInBackup = null;
        if (backupId != null) {
            BackupTaskResponse backupTaskResponse = backupBaseService.getBackUpJob(replicaSetName, backupId);
            hostInBackup = backupTaskResponse.getHostinsId();
            log.info("Found Host {} is backup, online resize it later.", hostInBackup);
        }
        ReplicaListResult replicaListResult = dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(
                RequestSession.getRequestId(), replicaSetName, null, null, null, null);
        Long finalHostInBackup = hostInBackup;
        List<Replica> replicas = replicaListResult.getItems()
                .stream()
                .filter(replica -> replica.getRole() != Replica.RoleEnum.LOGGER)
                .sorted(Comparator.comparing(Replica::getRole).reversed())
                .sorted((r1, r2) -> Objects.equals(r1.getId(), finalHostInBackup) ? 1 : Objects.equals(r2.getId(), finalHostInBackup) ? -1 : 0)
                .collect(Collectors.toList());

        log.info("Online Resize Order: {}({}) -> {}({})",
                replicas.get(0).getRole(), replicas.get(0).getId(),
                replicas.get(1).getRole(), replicas.get(1).getId());

        Map<String, ModulePlan> resultMap = new HashMap<String, ModulePlan>() {{
            this.put("first_node", ModulePlan.builder().replicaIds(replicas.get(0).getId().toString()).build());
            this.put("second_node", ModulePlan.builder().replicaIds(replicas.get(1).getId().toString()).build());
        }};

        return Result.returnSuccessResult(resultMap);
    }

    /**
     * 构建本地变配执行计划
     */
    @ApiOperation(value = "构建本地变配执行计划")
    @PostMapping("/build_elastic_modify_modules_plan")
    public Result<Map<String, ModulePlan>> buildLocalUpgradeModulesPlan(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                                        @RequestParam(name = "transInfo") String transInfoStr,
                                                                        @RequestParam(name = "extraParams") String extraParams) throws Exception {

        transInfoStr = StringEscapeUtils.unescapeJava(transInfoStr);
        TransInfo transInfo = JSONObject.parseObject(transInfoStr, TransInfo.class);
        TransParams transParams = JSONObject.parseObject(StringEscapeUtils.unescapeJava(transInfo.getParameter()), TransParams.class);
        ReplicaSet replicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(
                RequestSession.getRequestId(), replicaSetName, false);
        InstanceLevel srcLevel = dBaasMetaClient.getDefaultmetaApi()
                .getInstanceLevel(
                        RequestSession.getRequestId(),
                        replicaSet.getService(),
                        replicaSet.getServiceVersion(),
                        transInfo.getSrcClassCode(), null);
        InstanceLevel destLevel = dBaasMetaClient.getDefaultmetaApi()
                .getInstanceLevel(
                        RequestSession.getRequestId(),
                        replicaSet.getService(),
                        replicaSet.getServiceVersion(),
                        transInfo.getDestClassCode(), null);

        boolean isUpgradeMem;
        if (ReplicaSetMetaHelper.isServerless(srcLevel)) { //Serverless在相等时走降配，避免SCC不停重复下发
            isUpgradeMem = ReplicaSetMetaHelper.getInsLevelMemSizeMB(destLevel, transInfo.getRcu())
                    > ReplicaSetMetaHelper.getInsLevelMemSizeMB(srcLevel, transInfo.getSourceRcu());
        } else {
            isUpgradeMem = ReplicaSetMetaHelper.getInsLevelMemSizeMB(destLevel, transInfo.getRcu())
                    >= ReplicaSetMetaHelper.getInsLevelMemSizeMB(srcLevel, transInfo.getSourceRcu());
        }
        Replica masterReplica = mysqlBizService.findMasterReplica(replicaSetName);
        Replica slaveReplica = replicaSetMetaHelper.isSingleNode(replicaSetName) ? null : mysqlBizService.findSlaveReplica(replicaSetName);
        Long destMasterReplicaId = null;
        Long destSlaveReplicaId = null;
        for (TransParam transParam : transParams.getTransParamList()) {
            if (Objects.equals(transParam.getSrcReplicaId(), masterReplica.getId())) {
                destMasterReplicaId = transParam.getDstReplicaId();
            } else if (slaveReplica != null && Objects.equals(transParam.getSrcReplicaId(), slaveReplica.getId())) {
                destSlaveReplicaId = transParam.getDstReplicaId();
            } else {
                log.error("unknown src replicaId {} from trans_list.", transParam.getSrcReplicaId());
                log.error("TransParam {}", transParam);
                throw new NotRetryException("trans_list error");
            }
        }
        Long destMasterReplicaIdFinal = destMasterReplicaId;
        Long destSlaveReplicaIdFinal = destSlaveReplicaId;
        boolean isReplaceMaster = !Objects.equals(destMasterReplicaId, masterReplica.getId());
        boolean isReplaceSlave = slaveReplica != null && !Objects.equals(destSlaveReplicaIdFinal, slaveReplica.getId());
        boolean isReadIns = replicaMetaHelper.isReadIns(replicaSet);
        boolean isSingeNode = replicaMetaHelper.isSingleNode(replicaSetName);

        Map<String, TransInfoModuleBuilder> transModuleBuilderMap = new HashMap<>();

        transModuleBuilderMap.put("online_resize_node", trans -> {
            ModulePlan transModule = new ModulePlan();
            boolean isSizeChanged = !Objects.equals(transInfo.getSrcDiskSizeMB(), transInfo.getDestDiskSizeMB());
            boolean isPlChanged = !Objects.equals(transInfo.getSrcPerformanceLevel(), transInfo.getTargetPerformanceLevel());
            if (isSizeChanged || isPlChanged) {
                JSONObject param = JSONObject.parseObject(extraParams);
                param.put("skip_active_ins", true);
                param.put("replicaSetName", replicaSetName);
                transModule.setParams(param.toJSONString());
                transModule.setModuleName(
                        replicaMetaHelper.isSingleNode(replicaSetName) ?
                                ONLINE_RESIZE_INS_FOR_BASIC : ONLINE_RESIZE_INS
                );
            } else {
                transModule.setSkipReason("no need online resize");
            }
            return transModule;
        });

        // 备库变更策略
        transModuleBuilderMap.put("modify_slave_pod", trans -> {
            ModulePlan transModule = new ModulePlan();
            Map<String, Object> params;
            if (!replicaSetMetaHelper.isSingleNode(replicaSetName)) {
                Long srcReplicaId = slaveReplica.getId();
                transModule.setTargetType("replica");
                transModule.setReplicaIds(srcReplicaId.toString());
                if (isReplaceSlave) {
                    transModule.setModuleName(UPGRADE_AND_REPLACE_NODE);
                    params = taskParamHelper.getUpgradeAndReplaceNodeParam(transInfo, srcReplicaId, destSlaveReplicaIdFinal);
                } else {
                    params = taskParamHelper.getLocalModifyPodParam(transInfo, srcReplicaId);
                    if (ReplicaSetMetaHelper.isServerless(srcLevel)) {
                        transModule.setModuleName(isUpgradeMem ? LOCAL_UPGRADE_POD_FOR_SERVERLESS : LOCAL_DOWNGRADE_POD_FOR_SERVERLESS);
                    } else {
                        transModule.setModuleName(
                                !isUpgradeMem || isRebuildPod(slaveReplica.getId(), transInfo.getSrcClassCode(), transInfo.getDestClassCode()) ?
                                        LOCAL_REBUILD_POD : LOCAL_UPGRADE_POD
                        );
                    }
                }
                transModule.setParams((JSON.toJSONString(params)));
            } else {
                transModule.setSkipReason("is single node.");
            }
            return transModule;
        });

        // 主库变更策略
        transModuleBuilderMap.put("modify_master_pod", trans -> {
            ModulePlan transModule = new ModulePlan();
            Map<String, Object> params;
            Long srcReplicaId = masterReplica.getId();
            transModule.setTargetType("replica");
            transModule.setReplicaIds(srcReplicaId.toString());
            if (isReplaceMaster) {
                transModule.setModuleName(UPGRADE_AND_REPLACE_NODE);
                params = taskParamHelper.getUpgradeAndReplaceNodeParam(transInfo, srcReplicaId, destMasterReplicaIdFinal);
                transModule.setParams((JSON.toJSONString(params)));
            } else {
                params = taskParamHelper.getLocalModifyPodParam(transInfo, srcReplicaId);
                if (ReplicaSetMetaHelper.isServerless(srcLevel)) {
                    transModule.setModuleName(isUpgradeMem ? LOCAL_UPGRADE_POD_FOR_SERVERLESS : LOCAL_DOWNGRADE_POD_FOR_SERVERLESS);
                } else {
                    transModule.setModuleName(isUpgradeMem ? LOCAL_UPGRADE_POD : LOCAL_REBUILD_POD);
                }
                transModule.setParams((JSON.toJSONString(params)));
            }
            return transModule;
        });

        // 暂停策略
        transModuleBuilderMap.put("pause", transTask -> {
            ModulePlan transModule = new ModulePlan();
            if (isUpgradeMem && !isReplaceMaster) {
                transModule.setSkipReason("local upgrade master ins, skip pause");
            }
            // 跟切换策略不同在于，基础实例降配内存时，不需要切换，但需要暂停等待切换时间
            else if (replicaSetMetaHelper.isSingleNode(replicaSetName) && isUpgradeMem) {
                transModule.setSkipReason("replicaSet is single node, skip pause.");
            }
            return transModule;
        });

        // 切换策略
        transModuleBuilderMap.put("ha_switch", transTask -> {
            ModulePlan transModule = new ModulePlan();
            if (isUpgradeMem && !isReplaceMaster) {
                transModule.setSkipReason("local upgrade master ins, skip ha");
            } else if (replicaSetMetaHelper.isSingleNode(replicaSetName)) {
                transModule.setSkipReason("replicaSet is single node, skip ha.");
            }
            return transModule;
        });

        // 备库半跨机时，需要重建主库上的复制
        transModuleBuilderMap.put("rebuild_master_replication", transTask -> {
            ModulePlan transModule = new ModulePlan();
            if (isReplaceSlave && !isReadIns && !isSingeNode) {
                log.info("is replace slave, need to rebuild master replication");
            } else {
                log.info("slave is not changed, skip it.");
                transModule.setSkipReason("slave is not changed, skip it.");
            }
            return transModule;
        });

        // 主库半跨机时，需要重建备库上的复制
        transModuleBuilderMap.put("rebuild_slave_replication", transTask -> {
            ModulePlan transModule = new ModulePlan();
            if (isReplaceMaster && !isReadIns) {
                log.info("is replace master, need to rebuild slave replication");
            } else {
                log.info("master is not changed, skip it.");
                transModule.setSkipReason("master is not changed, skip it.");
            }
            return transModule;
        });

        // 只读实例变配后需要刷新MaxScale权重
        transModuleBuilderMap.put("refresh_maxscale_link", transTask -> {
            ModulePlan transModule = new ModulePlan();
            if (!isReadIns) {
                log.info("replicaSet is not read ins, skip refresh maxscal link");
                transModule.setSkipReason("replicaSet is not read ins, skip it.");
            }
            return transModule;
        });

        Map<String, ModulePlan> resultMap = new HashMap<>();
        for (String stepName : transModuleBuilderMap.keySet()) {
            resultMap.put(stepName, transModuleBuilderMap.get(stepName).build(transInfo));
        }
        return Result.returnSuccessResult(resultMap);
    }

    @ApiOperation(value = "构造cluster node对应的重建replica信息")
    @PostMapping("/build_cluster_upgrade_destReplica")
    public Result<Map<String, Object>> buildClusterUpgradeDestReplica(@RequestParam(name = "srcReplicaId") Long srcReplicaId,
                                                                      @RequestParam(name = "transInfo") String transInfoStr,
                                                                      @RequestParam(name = "extraParams") String extraParams) throws Exception {


        transInfoStr = StringEscapeUtils.unescapeJava(transInfoStr);
        TransInfo transInfo = JSONObject.parseObject(transInfoStr, TransInfo.class);
        TransParams transParams = JSONObject.parseObject(StringEscapeUtils.unescapeJava(transInfo.getParameter()), TransParams.class);

        Long destReplicaId = null;
        String destClassCode = null;
        for (TransParam transParam : transParams.getTransParamList()) {
            if (Objects.equals(transParam.getSrcReplicaId(), srcReplicaId)) {
                destReplicaId = transParam.getDstReplicaId();
                destClassCode = transParam.getDstClassCode();
            }
        }
        if (destReplicaId == null){
            throw new Exception("this srcReplica not found dest upgrade/replace replica node");
        }

        Map<String, Object> tmpExtraParams = JSON.parseObject(extraParams, Map.class);

        tmpExtraParams.put("srcReplicaId", srcReplicaId);
        tmpExtraParams.put("destReplicaId", destReplicaId);
        tmpExtraParams.put("destClassCode", destClassCode);
        return Result.returnSuccessResult(tmpExtraParams);
    }

    @ApiOperation(value = "构造cluster node对应的迁移replica信息")
    @PostMapping("/build_cluster_migrate_destReplica")
    public Result<Map<String, Object>> buildClusterMigrateDestReplica(@RequestParam(name = "srcReplicaId") Long srcReplicaId,
                                                                      @RequestParam(name = "replicaMapping") String replicaMapping,
                                                                      @RequestParam(name = "extraParams") String extraParams) throws Exception {
        Map<String, String> replicaMappingMap = StringUtil.mapStringToMap(replicaMapping);
        log.info("replicaMapping:{}", JSON.toJSONString(replicaMappingMap));

        String destReplicaId = replicaMappingMap.get(srcReplicaId.toString());
        if (destReplicaId == null){
            throw new Exception("this srcReplica not found dest upgrade/replace replica node");
        }

        Map<String, Object> tmpExtraParams = JSON.parseObject(extraParams, Map.class);

        tmpExtraParams.put("srcReplicaId", srcReplicaId);
        tmpExtraParams.put("destReplicaId", destReplicaId);
        return Result.returnSuccessResult(tmpExtraParams);
    }

    @ApiOperation(value = "构造cluster node本地重建replica信息")
    @PostMapping("/build_cluster_local_upgrade_Replica")
    public Result<Map<String, Object>> buildClusterLocalUpgradeReplica(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                                      @RequestParam(name = "srcReplicaId") Long srcReplicaId,
                                                                      @RequestParam(name = "transTaskId") String transTaskId,
                                                                      @RequestParam(name = "extraParams") String extraParams) throws Exception {
        TransferTask transferTask = dBaasMetaClient.getDefaultmetaApi().getTransferTask(RequestSession.getRequestId(),
                replicaSetName, LangUtil.getInteger(transTaskId));

        Map<String, Object> tmpExtraParams = JSON.parseObject(extraParams, Map.class);
        tmpExtraParams.put("srcReplicaId", srcReplicaId);
        tmpExtraParams.put("srcClassCode", transferTask.getSrcClassCode());
        tmpExtraParams.put("destClassCode", transferTask.getDestClassCode());  // 优先使用传入的ClassCode
//        tmpExtraParams.put("srcDiskSizeMB", transferTask.getSrcDiskSizeMB());
//        tmpExtraParams.put("destDiskSizeMB", transferTask.getDestDiskSizeMB());

        // 如果有 parameter, 并且存在 transParamList
        TransParams transParams = JSONObject.parseObject(StringEscapeUtils.unescapeJava(transferTask.getParameter()), TransParams.class);
        if (Objects.nonNull(transParams) && CollectionUtils.isNotEmpty(transParams.getTransParamList())) {
            for (TransParam transParam : transParams.getTransParamList()) {
                if (srcReplicaId.equals(transParam.getSrcReplicaId())) {
                    tmpExtraParams.put("destClassCode", transParam.getDstClassCode());
                    log.info("reset replica {} dest class code to {} ", srcReplicaId, transParam.getDstClassCode());
                }
            }
        }

        return Result.returnSuccessResult(tmpExtraParams);
    }

    /**
     * 本地升降配时，判断是否需要重建Pod
     */
    private boolean isRebuildPod(Long replicaId, String srcClassCode, String destClassCode) throws Exception {
        boolean isRebuild = false;
        ReplicaResource replicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), replicaId, false);
        ReplicaSet replicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), replicaResource.getReplicaSetName(), false);
        if (replicaResource.getReplica().getRole() == Replica.RoleEnum.SLAVE) {
            ReplicaParamBuilder paramBuilder = new ReplicaParamBuilder(paramDependency, replicaSet, replicaResource.getReplica(), destClassCode, null, null, null);
            Map<String, String> destLevelConfigs = paramBuilder.getParamsAfterExprTranslate();
            Map<String, String> srcLevelConfigs = new ReplicaParamBuilder(paramDependency, replicaSet, replicaResource.getReplica(), srcClassCode, null, null, null).getParamsAfterExprTranslate();
            MapDifference<String, String> difference = Maps.difference(srcLevelConfigs, destLevelConfigs);
            log.info("get diff src, dest {}", difference);
            Set<String> diffParam = new HashSet<String>() {
                {
                    addAll(difference.entriesDiffering().keySet());
                    addAll(difference.entriesOnlyOnRight().keySet());
                    addAll(difference.entriesOnlyOnLeft().keySet());
                }
            };

            for (String param : diffParam) {
                if (!DYNAMIC_PARAM.contains(param) && !param.contains(DB_STORAGE_ENGINE_XENGINE)) {
                    if (paramBuilder.getSysBaseParamTemplate().containsKey(param) && Boolean.FALSE.equals(paramBuilder.getSysBaseParamTemplate().get(param).getIsDynamic())) {
                        log.info("param {} is changed, need to restart.", param);
                        isRebuild = true;
                    } else if (!paramBuilder.getSysBaseParamTemplate().containsKey(param)) {
                        log.info("param {} not found in sys template, need to restart.", param);
                        isRebuild = true;
                    }
                }
            }
        }
        log.info("need to rebuild = {}", isRebuild);
        return isRebuild;
    }

    @RetryAnnotation(timeout = 300, retry = 3, interval = 10)
    @ApiOperation(value = "构建缩容时节点的日志追加任务")
    @PostMapping("/append_binlog_for_shrink_modules_plan")
    public Result<Map<String, ModulePlan>> buildAppendBinlogForShrinkPlan(@RequestParam(name = "srcReplicaSetName") String srcReplicaSetName,
                                                                          @RequestParam(name = "destReplicaSetName") String destReplicaSetName,
                                                                          @RequestParam(name = "destReplicaId") Long destReplicaId,
                                                                          @RequestParam(name = "backupSetId") Long backupSetId) throws Exception {
        Map<String, RebuildSlaveBuilder> modulesPlan = new HashMap<>();

        modulesPlan.put("append_binlog", new RebuildSlaveBuilder() {
            @Override
            public ModulePlan build(String srcReplicaSetName, String destReplicaSetName, Long backupSetId) throws Exception {
                ReplicaSet srcReplicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), srcReplicaSetName, false);
                // 只读实例使用主实例的备份集
                if (replicaMetaHelper.isReadIns(srcReplicaSet)) {
                    srcReplicaSetName = srcReplicaSet.getPrimaryInsName();
                }
                ModulePlan appendLogModule = new ModulePlan();
                BinlogBackupPolicyResponse backupPolicyResponse = bifrostBaseService.getBinlogBackupPolicy(srcReplicaSetName);
                if (Objects.equals(backupPolicyResponse.getUploadWay(), BackupConsts.BINLOG_NO_UPLOAD)) {
                    appendLogModule.setSkipReason("binlog policy is not upload.");
                } else {
                    GetBackupSetResponse getBackupSetResponse = backupBaseService.getBackupSet(srcReplicaSetName, backupSetId);
                    if (!BackupConsts.BACKUP_SET_STATUS_OK.equalsIgnoreCase(getBackupSetResponse.getStatus())) {
                        throw new NotRetryException(String.format("The backup set state is unavailable %s", getBackupSetResponse));
                    }
                    GetBackupSetResponse.SlaveStatus slaveStatus = getBackupSetResponse.getSlaveStatusObj();
                    boolean hasPurged = bifrostBaseService.binlogFromBackupSetHasPurged(srcReplicaSetName, slaveStatus.getBINLOGFILE(), slaveStatus.getBINLOGHINSID());
                    Map<String, Object> params = new HashMap<>();
                    if (hasPurged) {
                        params.put("srcReplicaSetName", srcReplicaSetName);
                        params.put("destReplicaSetName", destReplicaSetName);
                        params.put("srcReplicaId", slaveStatus.getBINLOGHINSID());
                        params.put("destReplicaId", destReplicaId);
                        params.put("restoreToNewest", true);
                        if(replicaSetMetaHelper.isReplicaSetExternalReplication(srcReplicaSetName)) {
                            params.put("channel", MAINTAIN_THREAD_CHANNEL);
                        }
                        params.put("restoreTime", DateTimeUtils.MAX_TIME);
                        params.put("startBinlogFile", slaveStatus.getBINLOGFILE());
                    } else {
                        appendLogModule.setSkipReason("binlog is not purge.");
                    }
                    appendLogModule.setModuleName("append_binlog");
                    appendLogModule.setParams((JSON.toJSONString(params)));
                }
                return appendLogModule;
            }
        });
        Map<String, ModulePlan> resultMap = new HashMap<>();
        for (String stepName : modulesPlan.keySet()) {
            resultMap.put(stepName, modulesPlan.get(stepName).build(srcReplicaSetName, destReplicaSetName, backupSetId));
        }
        return Result.returnSuccessResult(resultMap);
    }

    //shrink-v1-function
    @ApiOperation(value = "构建缩容时节点恢复任务")
    @PostMapping("/replace_node_for_shrink_modules_plan")
    public Result<Map<String, ModulePlan>> buildReplaceNodeForShrinkPlan(@RequestParam(name = "srcReplicaSetName") String srcReplicaSetName,
                                                                         @RequestParam(name = "destReplicaSetName") String destReplicaSetName,
                                                                         @RequestParam(name = "snapshotId", required = false) String snapshotId,
                                                                         @RequestParam(name = "backupSetId") Long backupSetId,
                                                                         @RequestParam(name = "extraParams") String extraParams) throws Exception {
        Map<String, ShrinkModuleBuilder> modulesPlan = new HashMap<>();

        // 获取参数
        Map<String, Object> paramObject = JSON.parseObject(extraParams, Map.class);

        // 获取实例信息
        List<Replica> srcReplicas = dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(RequestSession.getRequestId(), srcReplicaSetName, null, null, null, null).getItems();
        Replica destReplica = dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(RequestSession.getRequestId(), destReplicaSetName, null, null, null, null).getItems().get(0);
        String activityName = "replace_node_for_shrink_basic";
        Long srcReplicaId = srcReplicas.stream().filter(r -> Replica.RoleEnum.MASTER == r.getRole()).findFirst().get().getId();

        // 模块参数设置
        Map<String, Object> params = new HashMap<>();
        params.put("srcReplicaSetName", srcReplicaSetName);
        params.put("srcReplicaId", srcReplicaId);
        params.put("destReplicaSetName", destReplicaSetName);
        params.put("destReplicaId", destReplica.getId());
        params.put("snapshotId", snapshotId);
        params.put("switchInfo", paramObject.get("switchInfo"));
        params.put("performanceLevel", paramObject.get("performanceLevel"));
        params.put("srcParentReplicaSetName", paramObject.get("srcParentReplicaSetName"));
        params.put("destDiskSizeGB", paramObject.get("extendedDestDiskSizeGB"));
        params.put("destDiskSizeMB", paramObject.get("destDiskSizeMB"));
        params.put("backupSetId", backupSetId);


        // 判断源实例节点数
        if (srcReplicas.size() > 2) {
            log.error("no support current replicaSet reduce storage size, replica size is [{}]", srcReplicas.size());
            throw new ActivityException("no support current replicaSet reduce storage size");
        }

        if (2 == srcReplicas.size()){
            activityName = "replace_node_for_shrink";
            srcReplicaId = srcReplicas.stream().filter(r -> Replica.RoleEnum.SLAVE == r.getRole()).findFirst().get().getId();
            params.put("srcReplicaId", srcReplicaId);
        }

        modulesPlan.put("replace_for_shrink", new ShrinkModuleBuilder() {
            @Override
            public ModulePlan build(String srcReplicaSetName, String destReplicaSetName, Long backupSetId, String activityName) throws Exception {
                ModulePlan modulePlan = new ModulePlan();
                modulePlan.setTargetType("replica");
                modulePlan.setModuleName(activityName);
                modulePlan.setParams((JSON.toJSONString(params)));
                return modulePlan;
            }
        });

        Map<String, ModulePlan> resultMap = new HashMap<>();
        for (String stepName : modulesPlan.keySet()) {
            resultMap.put(stepName, modulesPlan.get(stepName).build(srcReplicaSetName, destReplicaSetName, backupSetId, activityName));
        }
        return Result.returnSuccessResult(resultMap);
    }

    @ApiOperation(value = "构建缩容时节点恢复任务")
    @PostMapping("/build_first_node_replace_for_shrink")
    public Result<Map<String, ModulePlan>> buildFirstNodeReplaceForShrink(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                                          @RequestParam(name = "destReplicaSetName") String destReplicaSetName,
                                                                          @RequestParam(name = "backupSetId") Long backupSetId,
                                                                          @RequestParam(name = "extraParams") String extraParams) throws Exception {
        Map<String, ShrinkModuleBuilder> modulesPlan = new HashMap<>();

        // 获取参数
        Map<String, Object> paramObject = JSON.parseObject(extraParams, Map.class);

        // 获取源实例信息
        ReplicaSet srcReplicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), replicaSetName, null);
        List<Replica> srcReplicas = dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(RequestSession.getRequestId(), replicaSetName, null, null, null, null).getItems();
        boolean isSingleNode = 1 == srcReplicas.size() ? true : false;
        Replica.RoleEnum srcReplicaRole = isSingleNode ? Replica.RoleEnum.MASTER : Replica.RoleEnum.SLAVE;
        String targetReplicaName = StringUtils.equals(srcReplicaSet.getCategory(), CLUSTER_LEVEL) ? String.valueOf(paramObject.get("targetReplicaName")) : null;
        Replica srcReplica = srcReplicas.stream().filter(r -> srcReplicaRole == r.getRole() && (null == targetReplicaName || targetReplicaName.equalsIgnoreCase(r.getName()))).findFirst().orElse(null);
        if (null == srcReplica) {
            log.error("specified src replica not found, targetReplicaName is [{}]", targetReplicaName);
            throw new ActivityException("specified src replica not found");
        }

        // 获取目标实例信息
        List<Replica> destReplicas = dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(RequestSession.getRequestId(), destReplicaSetName, null, null, null, null).getItems();
        Replica destReplica = destReplicas.get(0);

        // 模块参数设置
        Map<String, Object> params = new HashMap<>();
        params.put("srcReplicaSetName", replicaSetName);
        params.put("srcReplicaId", srcReplica.getId());
        params.put("destReplicaSetName", destReplicaSetName);
        params.put("destReplicaId", destReplica.getId());
        params.put("switchInfo", paramObject.get("switchInfo"));
        params.put("performanceLevel", paramObject.get("performanceLevel"));
        params.put("srcParentReplicaSetName", paramObject.get("srcParentReplicaSetName"));
        params.put("destDiskSizeGB", paramObject.get("extendedDestDiskSizeGB"));
        params.put("destDiskSizeMB", paramObject.get("destDiskSizeMB"));
        params.put("backupSetId", backupSetId);
        params.put("transId", paramObject.get("trans_id"));
        params.put("targetInitOptimizedWrites", paramObject.get("targetInitOptimizedWrites"));
        // 不用备份恢复数据盘
        params.put("skipRestoreFromBak", "true");
        if(replicaSetMetaHelper.isReplicaSetExternalReplication(replicaSetName)) {
            params.put("channel", MAINTAIN_THREAD_CHANNEL);
        }

        // 根据系列构建不同的节点重搭任务
        String activityName = null;

        // 集群版节点为1的实例也走的是provision基础版重搭，因此先判断serverless基础版，再判断provision基础版
        if (StringUtils.equals(srcReplicaSet.getCategory(), SERVERLESS_BASIC_LEVEL)) {
            activityName = SHRINK_REPLACE_NODE_FOR_SERVERLESS_BASIC;
        } else if (StringUtils.equals(srcReplicaSet.getCategory(), BASIC_LEVEL) || 1 == srcReplicas.size()) {
            activityName = SHRINK_REPLACE_NODE_FOR_BASIC;
        }  else if (StringUtils.equals(srcReplicaSet.getCategory(), SERVERLESS_STANDARD_LEVEL)) {
            activityName = SHRINK_REPLACE_NODE_FOR_SERVERLESS_STANDARD;
        } else if (StringUtils.equals(srcReplicaSet.getCategory(), STANDARD_LEVEL)) {
            activityName = SHRINK_REPLACE_NODE_FOR_STANDARD;
        } else if (StringUtils.equals(srcReplicaSet.getCategory(), CLUSTER_LEVEL)) {
            activityName = SHRINK_REPLACE_NODE_FOR_CLUSTER;
            // 是否是mgr
            Map<String, String> labels = dBaasMetaClient.getDefaultmetaApi().listReplicaSetLabels(RequestSession.getRequestId(), replicaSetName);
            boolean isMgr = ReplicaSetMetaHelper.isMgr(labels);
            if (isMgr) {
                params.put("isMgr", true);
            }
            // 指定第一次ha的srcReplica
            params.put("targetReplicaName", paramObject.get("targetReplicaName"));
        } else {
            throw new ActivityException(String.format("no support current replicaSet category [%s] to Shrink", srcReplicaSet.getCategory()));
        }

        modulesPlan.put("replace_for_shrink", new ShrinkModuleBuilder() {
            @Override
            public ModulePlan build(String srcReplicaSetName, String destReplicaSetName, Long backupSetId, String activityName) throws Exception {
                ModulePlan modulePlan = new ModulePlan();
                modulePlan.setTargetType("replica");
                modulePlan.setModuleName(activityName);
                modulePlan.setParams((JSON.toJSONString(params)));
                return modulePlan;
            }
        });

        Map<String, ModulePlan> resultMap = new HashMap<>();
        for (String stepName : modulesPlan.keySet()) {
            resultMap.put(stepName, modulesPlan.get(stepName).build(replicaSetName, destReplicaSetName, backupSetId, activityName));
        }
        return Result.returnSuccessResult(resultMap);
    }

    @ApiOperation(value = "构建缩容时第二次所有备节点重搭任务")
    @PostMapping("/build_all_slave_for_shrink")
    public Result<Map<String, ModulePlan>> buildAllSlaveForShrink(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                                  @RequestParam(name = "srcReplicaId") Long srcReplicaId,
                                                                  @RequestParam(name = "destReplicaSetName") String destReplicaSetName,
                                                                  @RequestParam(name = "backupSetId") Long backupSetId,
                                                                  @RequestParam(name = "extraParams") String extraParams) throws Exception {

        Map<String, ShrinkModuleBuilder> modulesPlan = new HashMap<>();
        // 获取任务参数
        Map<String, Object> paramObject = JSON.parseObject(extraParams, Map.class);

        // 先确认当前master节点为缩容后的节点
        Replica srcMasterReplica = mysqlBizService.findMasterReplica(replicaSetName);
        if (!Objects.equals(srcMasterReplica.getId(), srcReplicaId)) {
            throw new CheckException(String.format("current master replica [%s] is not shrink replica, please check.", srcReplicaId));
        }
        Replica srcSlaveReplica = mysqlBizService.findSlaveReplica(replicaSetName);

        // 构建重搭模块参数
        Map<String, Object> params = new HashMap<>();
        params.put("srcReplicaSetName", replicaSetName);
        params.put("srcReplicaId", srcSlaveReplica.getId());
        params.put("destReplicaSetName", destReplicaSetName);
        params.put("performanceLevel", paramObject.get("performanceLevel"));
        params.put("srcParentReplicaSetName", paramObject.get("srcParentReplicaSetName"));
        params.put("destDiskSizeGB", paramObject.get("extendedDestDiskSizeGB"));
        params.put("destDiskSizeMB", paramObject.get("destDiskSizeMB"));
        params.put("transId", paramObject.get("trans_id"));
        params.put("targetInitOptimizedWrites", paramObject.get("targetInitOptimizedWrites"));

        // 只读复用一个备份集
        ReplicaSet replicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), replicaSetName, false);
        if (READONLY == replicaSet.getInsType()) {
            params.put("backupSetId", backupSetId);
        }

        // 根据源实例信息判断走哪种重搭
        String activityName = SHRINK_REPLACE_NODE_FOR_STANDARD;
        ReplicaSet srcReplicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), replicaSetName, null);
        if (StringUtils.equals(srcReplicaSet.getCategory(), SERVERLESS_STANDARD_LEVEL))
            activityName = SHRINK_REPLACE_NODE_FOR_SERVERLESS_STANDARD;
        if (StringUtils.equals(srcReplicaSet.getCategory(), CLUSTER_LEVEL)) {
            activityName = SHRINK_REPLACE_NODE_FOR_CLUSTER;
            params.put("srcReplicaId", null);
            // 是否是mgr
            Map<String, String> labels = dBaasMetaClient.getDefaultmetaApi().listReplicaSetLabels(RequestSession.getRequestId(), replicaSetName);
            boolean isMgr = ReplicaSetMetaHelper.isMgr(labels);
            if (isMgr) {
                params.put("isMgr", true);
            }
        }

        // 构造子任务
        modulesPlan.put("replace_for_shrink", new ShrinkModuleBuilder() {
            @Override
            public ModulePlan build(String srcReplicaSetName, String destReplicaSetName, Long backupSetId, String activityName) throws Exception {
                ModulePlan modulePlan = new ModulePlan();
                modulePlan.setTargetType("replica");
                modulePlan.setModuleName(activityName);
                modulePlan.setParams((JSON.toJSONString(params)));
                return modulePlan;
            }
        });

        Map<String, ModulePlan> resultMap = new HashMap<>();
        for (String stepName : modulesPlan.keySet()) {
            resultMap.put(stepName, modulesPlan.get(stepName).build(replicaSetName, destReplicaSetName, null, activityName));
        }
        return Result.returnSuccessResult(resultMap);
    }

    @ApiOperation(value = "构建缩容时集群版各源目标节点一一对应关系")
    @PostMapping("/build_cluster_slaves_for_shrink_modules_plan")
    public Result<Map<String, Object>> buildClusterSlavesForShrinkPlan(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                                       @RequestParam(name = "srcReplicaId", required = false) Long srcReplicaId,
                                                                       @RequestParam(name = "destReplicaId") Long destReplicaId,
                                                                       @RequestParam(name = "transId") String transId) throws Exception {
        Map<String, Object> result = new HashMap<>();
        result.put("destReplicaId", destReplicaId);
        Long replicaId;
        if (null != srcReplicaId) {
            replicaId = srcReplicaId;
        } else {
            TransferTask transferTask = dBaasMetaClient.getDefaultmetaApi().getTransferTask(RequestSession.getRequestId(), replicaSetName, Integer.valueOf(transId));
            if (null == transferTask || null == transferTask.getParameter()) {
                throw new ActivityException("not found trans task with specified trans id.");
            }
            Map<String, Object> parameters = JSONObject.parseObject(transferTask.getParameter(), Map.class);
            List<TransParam> transParams = JSONObject.parseArray(JSON.toJSONString(parameters.get("transParamList")),TransParam.class);
            TransParam object = transParams.stream().filter(t -> Objects.equals(t.getDstReplicaId(), destReplicaId)).findFirst().orElse(null);
            if (null == object || null == object.getSrcReplicaId()) {
                log.error("not found src replica id with specified dest replica id [{}], transParam: {}", destReplicaId, JSON.toJSONString(transParams));
                throw new ActivityException("not found src replica id with specified dest replica id.");
            }
            replicaId = object.getSrcReplicaId();
        }
        // 检查一下要重搭的节点是否是slave
        ReplicaResource replica = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), replicaId, null);
        if (Replica.RoleEnum.SLAVE != replica.getReplica().getRole()){
            throw new CheckException(String.format("rebuild src replica [%s] is not slave, please check.", replicaId));
        }
        result.put("srcReplicaId", replicaId);
        return Result.returnSuccessResult(result);
    }


    @ApiOperation(value = "构建缩容时链路切换任务")
    @PostMapping("/build_switch_for_shrink_task")
    public Result<Map<String, ModulePlan>> buildSwitchForShrink(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                                @RequestParam(name = "destReplicaSetName") String destReplicaSetName,
                                                                @RequestParam(name = "backupSetId") Long backupSetId,
                                                                @RequestParam(name = "extraParams") String extraParams) throws Exception {
        Map<String, ShrinkModuleBuilder> modulesPlan = new HashMap<>();

        // 获取参数
        Map<String, Object> paramObject = JSON.parseObject(extraParams, Map.class);

        // 获取源实例信息
        ReplicaSet srcReplicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), replicaSetName, null);
        List<Replica> srcReplicas = dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(RequestSession.getRequestId(), replicaSetName, null, null, null, null).getItems();
        boolean isSingleNode = 1 == srcReplicas.size() ? true : false;
        Replica.RoleEnum srcReplicaRole = isSingleNode ? Replica.RoleEnum.MASTER : Replica.RoleEnum.SLAVE;
        String targetReplicaName = StringUtils.equals(srcReplicaSet.getCategory(), CLUSTER_LEVEL) ? String.valueOf(paramObject.get("targetReplicaName")) : null;
        Replica srcReplica = srcReplicas.stream().filter(r -> srcReplicaRole == r.getRole() && (null == targetReplicaName || targetReplicaName.equalsIgnoreCase(r.getName()))).findFirst().orElse(null);
        if (null == srcReplica) {
            log.error("specified src replica not found, targetReplicaName is [{}]", targetReplicaName);
            throw new ActivityException("specified src replica not found");
        }

        // 高可用版和集群版的目标实例1已被删除
        if(StringUtils.equals(srcReplicaSet.getCategory(), SERVERLESS_STANDARD_LEVEL)
                || StringUtils.equals(srcReplicaSet.getCategory(), STANDARD_LEVEL)
                || StringUtils.equals(srcReplicaSet.getCategory(), CLUSTER_LEVEL)){
            destReplicaSetName = null;
            log.info("This instance is standard level or cluster level, destReplicaSet has been deleted!");
        }

        // 模块参数设置
        Map<String, Object> params = new HashMap<>();
        params.put("srcReplicaSetName", replicaSetName);
        params.put("srcReplicaId", srcReplica.getId());
        if(Objects.nonNull(destReplicaSetName)){
            List<Replica> destReplicas = dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(RequestSession.getRequestId(), destReplicaSetName, null, null, null, null).getItems();
            Replica destReplica = destReplicas.get(0);
            params.put("destReplicaSetName", destReplicaSetName);
            params.put("destReplicaId", destReplica.getId());
        }
        params.put("switchInfo", paramObject.get("switchInfo"));
        params.put("performanceLevel", paramObject.get("performanceLevel"));
        params.put("srcParentReplicaSetName", paramObject.get("srcParentReplicaSetName"));
        params.put("destDiskSizeGB", paramObject.get("extendedDestDiskSizeGB"));
        params.put("destDiskSizeMB", paramObject.get("destDiskSizeMB"));
        params.put("backupSetId", backupSetId);
        params.put("transId", paramObject.get("trans_id"));
        if(replicaSetMetaHelper.isReplicaSetExternalReplication(replicaSetName)){
            params.put("channel",MAINTAIN_THREAD_CHANNEL);
        }

        // 不用备份恢复数据盘
        params.put("skipRestoreFromBak", "true");

        // 根据系列构建不同的链路切换任务
        String activityName = null;
        // 需要先判断serverless版本，否则1 == srcReplicas.size()这个条件会把serverless基础版覆盖掉
        if (StringUtils.equals(srcReplicaSet.getCategory(), SERVERLESS_BASIC_LEVEL)) {
            activityName = SHRINK_SWITCH_FOR_SERVERLESS_BASIC;
        } else if (StringUtils.equals(srcReplicaSet.getCategory(), SERVERLESS_STANDARD_LEVEL)) {
            activityName = SHRINK_SWITCH_FOR_SERVERLESS_STANDARD;
        } else if (StringUtils.equals(srcReplicaSet.getCategory(), BASIC_LEVEL) || 1 == srcReplicas.size()) {
            activityName = SHRINK_SWITCH_FOR_BASIC;
        } else if (StringUtils.equals(srcReplicaSet.getCategory(), STANDARD_LEVEL)) {
            activityName = SHRINK_SWITCH_FOR_STANDARD;
        } else if (StringUtils.equals(srcReplicaSet.getCategory(), CLUSTER_LEVEL)) {
            activityName = SHRINK_SWITCH_FOR_STANDARD;
            // 是否是mgr
            Map<String, String> labels = dBaasMetaClient.getDefaultmetaApi().listReplicaSetLabels(RequestSession.getRequestId(), replicaSetName);
            boolean isMgr = ReplicaSetMetaHelper.isMgr(labels);
            if (isMgr) {
                params.put("isMgr", true);
            }
            // 指定第一次ha的srcReplica
            params.put("targetReplicaName", paramObject.get("targetReplicaName"));
        } else {
            throw new ActivityException(String.format("no support current replicaSet category [%s] to Shrink", srcReplicaSet.getCategory()));
        }

        modulesPlan.put("switch_for_shrink", new ShrinkModuleBuilder() {
            @Override
            public ModulePlan build(String srcReplicaSetName, String destReplicaSetName, Long backupSetId, String activityName) throws Exception {
                ModulePlan modulePlan = new ModulePlan();
                modulePlan.setTargetType("replica");
                modulePlan.setModuleName(activityName);
                modulePlan.setParams((JSON.toJSONString(params)));
                return modulePlan;
            }
        });

        Map<String, ModulePlan> resultMap = new HashMap<>();
        for (String stepName : modulesPlan.keySet()) {
            resultMap.put(stepName, modulesPlan.get(stepName).build(replicaSetName, destReplicaSetName, backupSetId, activityName));
        }
        return Result.returnSuccessResult(resultMap);
    }
}
