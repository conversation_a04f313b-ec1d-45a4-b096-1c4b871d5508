package com.aliyun.app.activityprovider.activity.mysql;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.app.activityprovider.base.actionmanager.*;
import com.aliyun.app.activityprovider.base.actionmanager.actions.ConfigFileAction;
import com.aliyun.app.activityprovider.base.actionmanager.actions.InitDbAction;
import com.aliyun.app.activityprovider.base.actionmanager.actions.StopAction;
import com.aliyun.app.activityprovider.base.aurora.AuroraConsts;
import com.aliyun.app.activityprovider.base.cli.CliTools;
import com.aliyun.app.activityprovider.base.common.CommonProviderClient;
import com.aliyun.app.activityprovider.base.link.EndpointWeightConfig;
import com.aliyun.app.activityprovider.base.maxscale.MaxscaleProviderService;
import com.aliyun.app.activityprovider.base.meta.DbaasMetaClient;
import com.aliyun.app.activityprovider.base.rdsapi.RdsAPIBaseService;
import com.aliyun.app.activityprovider.base.sqlexec.MySQLCommand;
import com.aliyun.app.activityprovider.base.sqlexec.SQLBaseExecutor;
import com.aliyun.app.activityprovider.base.sqlexec.SQLResult;
import com.aliyun.app.activityprovider.base.sqlexec.SQLTemplateSet;
import com.aliyun.app.activityprovider.base.workflow.WorkFlowBaseService;
import com.aliyun.app.activityprovider.base.workflow.WorkFlowContext;
import com.aliyun.app.activityprovider.base.workflow.model.Task;
import com.aliyun.app.activityprovider.bizservice.mysql.MysqlBizService;
import com.aliyun.app.activityprovider.bizservice.mysql.MysqlMigrateBizService;
import com.aliyun.app.activityprovider.bizservice.mysql.MysqlService;
import com.aliyun.app.activityprovider.bizservice.mysql.MysqlServiceFactory;
import com.aliyun.app.activityprovider.bizservice.rdsreplication.ReplicationBizService;
import com.aliyun.app.activityprovider.bizservice.xdb.XdbBizService;
import com.aliyun.app.activityprovider.common.annotation.RetryAnnotation;
import com.aliyun.app.activityprovider.common.consts.*;
import com.aliyun.app.activityprovider.common.exception.*;
import com.aliyun.app.activityprovider.common.utils.*;
import com.aliyun.app.activityprovider.meta.ReplicaMetaHelper;
import com.aliyun.app.activityprovider.meta.ReplicaSetMetaHelper;
import com.aliyun.app.activityprovider.web.RequestSession;
import com.aliyun.app.activityprovider.web.Result;
import com.aliyun.apsaradb.activityprovider.model.CmdStatusOutput;
import com.aliyun.apsaradb.activityprovider.model.EndPoint;
import com.aliyun.apsaradb.activityprovider.model.ReplicaSetResourceRequest;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

import static com.aliyun.app.activityprovider.common.consts.CommonConsts.*;
import static com.aliyun.app.activityprovider.common.consts.ContextKeyConsts.MYSQL_INIT;
import static com.aliyun.app.activityprovider.common.consts.ErrorCode.SERVICE_RETURN_CODE_INTERNAL_ERROR;
import static com.aliyun.app.activityprovider.common.consts.MysqlConsts.*;

/**
 * <AUTHOR> on 2020/4/15.
 */
@Api(tags = "MySQL相关Activity")
@RestController
@RequestMapping("/api/v1/mysql")
@Slf4j
public class MySQLEngineActivity {

    @Resource
    private CommonProviderClient commonProviderClient;
    @Resource
    private DbaasMetaClient dBaasMetaClient;
    @Resource
    private SQLBaseExecutor sqlBaseExecutor;
    @Resource
    private ActionJobManager actionJobManager;
    @Resource
    private WorkFlowBaseService workFlowBaseService;
    @Resource
    private ActionCmdManager actionCmdManager;
    @Resource
    private MysqlBizService mysqlBizService;
    @Resource
    private XdbBizService xdbBizService;
    @Resource
    private MysqlMigrateBizService mysqlMigrateBizService;
    @Resource
    private ReplicaSetMetaHelper replicaSetMetaHelper;
    @Resource
    protected DbaasMetaClient dbaasMetaClient;
    @Resource
    private ReplicaMetaHelper replicaMetaHelper;
    @Resource
    private RdsAPIBaseService rdsAPIBaseService;
    @Resource
    private MysqlServiceFactory mysqlServiceFactory;
    @Resource
    private MaxscaleProviderService maxscaleProviderService;
    @Resource
    private ResourceKeyUtil resourceKeyUtil;
    @Resource
    private ReplicationBizService replicationBizService;


    @ApiOperation(value = "初始化实例的数据库")
    @PostMapping("/install_db")
    public Result<String> installDB(@RequestParam(name = "replicaId") Long replicaId) throws Exception {
        ReplicaResource replica = dBaasMetaClient
                .getDefaultmetaApi()
                .getReplica(RequestSession.getRequestId(), replicaId, false);
        List<String> cmdList = mysqlServiceFactory.get(replica).buildInitCmdList();
        Map<String, String> result = actionJobManager.run(
                ActionJobParam.builder()
                        .resource(InitDbAction.RESOURCE)
                        .action(InitDbAction.ACTION)
                        .replicaSetName(replica.getReplicaSetName())
                        .replicaIdList(ImmutableList.of(replicaId))
                        .podEntryPoint(PodEntryPoint.builder().podArgs(cmdList).build())
                        .params(new HashMap<>())
                        .build()
        );
        return Result.returnSuccessResult(RequestSession.getRequestId());
    }


    @ApiOperation(value = "异步初始化实例的数据库")
    @PostMapping("/install_db_async")
    public Result<String> installDBAsync(@RequestParam(name = "replicaId") Long replicaId) throws Exception {
        ReplicaResource replica = dBaasMetaClient
                .getDefaultmetaApi()
                .getReplica(RequestSession.getRequestId(), replicaId, false);

        List<String> cmdList = mysqlServiceFactory.get(replica).buildInitCmdList();
        List<String> jobList = actionJobManager.runAsync(
                ActionJobParam.builder()
                        .resource(InitDbAction.RESOURCE)
                        .action(InitDbAction.ACTION)
                        .replicaSetName(replica.getReplicaSetName())
                        .replicaIdList(ImmutableList.of(replicaId))
                        .podEntryPoint(PodEntryPoint.builder().podArgs(cmdList).build())
                        .async(true)
                        .params(new HashMap<>())
                        .build()
        );
        workFlowBaseService.updateWorkflowContext(ImmutableMap.of(MYSQL_INIT, true));
        return Result.returnSuccessResult(jobList.get(0));
    }

    @ApiOperation(value = "备份上云可能遇到表结构不对的备份，无法自动填充id，下面生成插入timezone的sql执行会报错，这里给id加上默认值， 为了保证兼容性，这里执行sql不报错")
    @PostMapping("/fix_timezone_tables")
    public Result<String> fixTimezoneTables(@RequestParam(name = "replicaSetName") String replicaSetName,
                                            @RequestParam(name = "replicaIds", required = false) List<Long> replicaIds) throws Exception {
        DefaultApi metaApi = dBaasMetaClient.getDefaultmetaApi();
        List<Replica> replicas = metaApi.listReplicasInReplicaSet(RequestSession.getRequestId(), replicaSetName,
                null, null, null, null).getItems();
        if (CollectionUtils.isEmpty(replicas)) {
            throw new CheckException("no replicas found for ReplicaSet: " + replicaSetName);
        }
        if (CollectionUtils.isEmpty(replicaIds)) {
            replicaIds = new ArrayList<>();
            for (Replica replica : replicas) {
                replicaIds.add(replica.getId());
            }
        }
        for (Long replicaId : replicaIds) {
            try {
                sqlBaseExecutor.execSQLInReplica(replicaId, new MySQLCommand(("set SQL_LOG_bin=0; use mysql; alter table time_zone modify column Time_zone_id int unsigned auto_increment;")));
            } catch (Exception e) {
                log.warn("Table time_zone add auto_increment for primary key failed (can be ignored)", e);
            }
        }
        return Result.returnSuccessResult(RequestSession.getRequestId());
    }

    @ApiOperation(value = "初始化实例的时区表，支持命名时区")
    @PostMapping("/insert_timezone_tables")
    public Result<String> insertTimezoneTables(@RequestParam(name = "replicaSetName") String replicaSetName,
                                               @RequestParam(name = "replicaIds", required = false) List<Long> replicaIds) throws Exception {
        String requestId = RequestSession.getRequestId();
        DefaultApi metaApi = dBaasMetaClient.getDefaultmetaApi();
        List<Replica> replicas = metaApi.listReplicasInReplicaSet(requestId, replicaSetName,
                null, null, null, null).getItems();
        if (CollectionUtils.isEmpty(replicas)) {
            throw new CheckException("no replicas found for ReplicaSet: " + replicaSetName);
        }

        if (CollectionUtils.isEmpty(replicaIds)) {
            replicaIds = new ArrayList<>();
            for (Replica replica : replicas) {
                replicaIds.add(replica.getId());
            }
        }

        boolean isXdb = replicaSetMetaHelper.isXdbReplicaSet(replicaSetName);
        String binaryPath = isXdb ? XCLUSTER_TZINFO_TO_SQL_PATH : ALISQL_TZINFO_TO_SQL_PATH;
        String mysqlPath = isXdb ? XCLUSTER_CLIENT_PATH : ALISQL_CLIENT_PATH;
        String forceRevise = isXdb ? "set force_revise=ON;" : "";

        String exec = MySQLSecBox.wrapperCmd(String.format("%s -ualiyun_root -h127.0.0.1 -P%%s mysql", mysqlPath));
        String cmdTemplate = String.format("var=$(%s %s);var=\"set SQL_LOG_bin=0;%s${var}\";" +
                        " echo ${var} | %s ", binaryPath, SYSTEM_TIMEZONE_PATH, forceRevise, exec);
        Map<Long, String> replicasCmd = new HashMap<>();
        for (Replica replica : replicas) {
            replicasCmd.put(replica.getId(), String.format(cmdTemplate, Objects.requireNonNull(replica.getPorts()).get(0), Objects.requireNonNull(replica.getPorts()).get(0)));
        }
        actionCmdManager.runCmdInReplicaSet(replicaSetName, replicasCmd);
        return Result.returnSuccessResult(RequestSession.getRequestId());
    }

    @ApiOperation(value = "使用工具镜像固化的timezone.sql 初始化实例的时区表，支持命名时区")
    @PostMapping("/insert_timezone_tables_use_file")
    public Result<String> insertTimezoneTablesUseFile(@RequestParam(name = "replicaSetName") String replicaSetName) throws Exception {
        String requestId = RequestSession.getRequestId();
        DefaultApi metaApi = dBaasMetaClient.getDefaultmetaApi();
        List<Replica> replicas = metaApi.listReplicasInReplicaSet(requestId, replicaSetName,null, null, null, null).getItems();
        if (CollectionUtils.isEmpty(replicas)) {
            throw new CheckException("no replicas found for ReplicaSet: " + replicaSetName);
        }

        List<Long> execReplica = new ArrayList<>();
        Map<String, String> envs = new HashMap<>();
        envs.put("timezone_path", INIT_TIMEZONE_SQL_PATH);
        for (Replica replica : replicas){
            execReplica.add(replica.getId());
        }
        actionJobManager.run(
                ActionJobParam.builder()
                        .resource(ConfigFileAction.InitTimeZone.RESOURCE)
                        .action(ConfigFileAction.InitTimeZone.ACTION)
                        .replicaSetName(replicaSetName)
                        .replicaIdList(execReplica)
                        .params(envs)
                        .build()
        );

        ReplicaSet replicaSet = metaApi.getReplicaSet(requestId, replicaSetName, null);
        String mysqlPath = CliTools.getBinaryPath(replicaSet.getService(), CliTools.Category.CLIENT_BIN);
        String execCmd = MySQLSecBox.wrapperCmd(String.format("%s -ualiyun_root -h127.0.0.1 -P%%s mysql", mysqlPath));
        String cmdTemplate = String.format("var=$(cat %s);var=\"set SQL_LOG_bin=0;${var}\";" + " echo ${var} | %s ", INIT_TIMEZONE_SQL_PATH, execCmd);
        Map<Long, String> replicasCmd = new HashMap<>();
        for (Replica replica : replicas) {
            replicasCmd.put(replica.getId(), String.format(cmdTemplate, Objects.requireNonNull(replica.getPorts()).get(0), Objects.requireNonNull(replica.getPorts()).get(0)));
        }

        actionCmdManager.runCmdInReplicaSet(replicaSetName, replicasCmd);
        return Result.returnSuccessResult(RequestSession.getRequestId());
    }

    @ApiOperation(value = "创建实例优雅停止的脚本文件")
    @PostMapping("/create_shutdown_file")
    public Result<String> createShutdownFile(@RequestParam(name = "replicaId") Long replicaId) throws Exception {
        ReplicaResource replica = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), replicaId, false);
        ActionJobParam actionJobParam = ActionJobParam.builder()
                .resource(StopAction.RESOURCE)
                .action(StopAction.ACTION)
                .replicaSetName(replica.getReplicaSetName())
                .replicaIdList(Collections.singletonList(replicaId))
                .build();
        actionJobManager.run(actionJobParam);
        return Result.returnSuccessResult("success");
    }


    @ApiOperation(value = "创建实例优雅停止的脚本文件")
    @PostMapping("/create_replicaset_shutdown_file")
    public Result<String> createReplicaSetShutdownFile(@RequestParam(name = "replicaSetName") String replicaSetName) throws Exception {
        List<Long> replicaIds = dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(RequestSession.getRequestId(), replicaSetName, null, null, null, null).getItems()
                .stream().map(Replica::getId).collect(Collectors.toList());
        for (Long replicaId : replicaIds) {
            createShutdownFile(replicaId);
        }
        return Result.returnSuccessResult(RequestSession.getRequestId());
    }


    @ApiOperation(value = "检查实例是否启动(检查pod状态)")
    @PostMapping("/check_instance_service_started")
    public Result<Boolean> checkInstanceServiceStarted(
            @RequestParam(name = "replicaId") Long replicaId,
            @RequestParam(name = "expectIsStarted", defaultValue = "true") Boolean expectIsStarted,
            @RequestParam(name = "isAllowCrash", defaultValue = "false") Boolean isAllowCrash
    ) throws Exception {
        log.info("check Pod Started for replicaId is {}", replicaId);
        ReplicaResource replica = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), replicaId, false);
        String response = mysqlServiceFactory.get(replica).checkServiceStatus(replica);
        Set<String> statusSet = new HashSet<>();
        if (isAllowCrash) {
            statusSet.add(CommonConsts.POD_CRASH);
        }
        log.info("pod status is {}", response);
        if (expectIsStarted) {
            statusSet.add(CommonConsts.POD_RUNNING);
        } else {
            statusSet.add(CommonConsts.POD_STOPPED);
        }
        if (!statusSet.contains(response)) {
            throw new ActivityException(String.format("un-except status. pod is %s", response));
        }
        log.info("Pod has Started");
        return Result.returnSuccessResult(true);
    }

    @RetryAnnotation(timeout = 900, interval = 10, retry = 3)
    @ApiOperation(value = "删除ReplicaSet的实例Pod")
    @PostMapping("/delete_instance_service_of_replicaset")
    public Result<Boolean> deleteInstanceServiceForReplicaset(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                              @RequestParam(name = "gracePeriodSeconds", defaultValue = "180") Integer gracePeriodSeconds,
                                                              @RequestParam(name = "force", defaultValue = "true") Boolean force) throws Exception {
        // 检查是否临时实例
        ReplicaSet replicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), replicaSetName, true);
        if (replicaSet == null || replicaSet.getInsType() != ReplicaSet.InsTypeEnum.TMP) {
            throw new NotRetryException("current's replicaSet is not tmp, cannot supported");
        }

        ReplicaListResult replicaListResult = dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(RequestSession.getRequestId(), replicaSetName, null, null, null, null);
        if (CollectionUtils.isEmpty(replicaListResult.getItems())) {
            throw new NotRetryException("Cannot find replica in replicaSet " + replicaSetName);
        }

        for (Replica item : replicaListResult.getItems()) {
            Long replicaId = item.getId();
            String response = commonProviderClient.getDefaultApi().getReplicaStatus(RequestSession.getRequestId(), replicaSetName, replicaId);
            log.info("replica {} pod status is {}", replicaId, response);
            if (!CommonConsts.POD_STOPPED.equals(response)) {
                deleteInstanceService(item.getId(), gracePeriodSeconds, force);
            }
        }
        return Result.returnSuccessResult(true);
    }

    @RetryAnnotation(timeout = 900, interval = 5, retry = 3)
    @ApiOperation(value = "删除Replica的实例Pod")
    @PostMapping("/delete_instance_service")
    public Result<String> deleteInstanceService(@RequestParam(name = "replicaId") Long replicaId,
                                                @RequestParam(name = "gracePeriodSeconds", defaultValue = "180") Integer gracePeriodSeconds,
                                                @RequestParam(name = "force", defaultValue = "true") Boolean force) throws Exception {
        ReplicaResource replica = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), replicaId, false);
        if (!force) {
            MysqlService mysqlService = mysqlServiceFactory.get(replica);
            mysqlService.checkCurrentIsNotMaster(replicaId);
        }
        boolean isSuccess = mysqlServiceFactory.get(replica).stopService(replica, gracePeriodSeconds);
        if (isSuccess) {
            return Result.returnSuccessResult(RequestSession.getRequestId());
        } else {
            return Result.returnFailureResult(SERVICE_RETURN_CODE_INTERNAL_ERROR, "delete failed");
        }
    }

    @RetryAnnotation(timeout = 900, interval = 5, retry = 3)
    @ApiOperation(value = "启动Replica的实例Pod")
    @PostMapping("/start_instance_service")
    public Result<String> startInstance(@RequestParam(name = "replicaId", required = false) Long replicaId,
                                        @RequestParam(name = "replicaSetName", required = false) String replicaSetName,
                                        @RequestParam(name = "role", required = false) String role,
                                        @RequestParam(name = "targetComposeTag", required = false) String targetComposeTag) throws Exception {
        if (replicaId == null && (replicaSetName == null || role == null)) {
            throw new NotRetryException("replicaId or role must be set.");
        }
        if (replicaId == null) {
            replicaId = replicaSetMetaHelper.getReplica(replicaSetName, role).getId();
        }
        ReplicaResource replica = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), replicaId, false);
        boolean isSuccess =  mysqlServiceFactory.get(replica).startService(replica, targetComposeTag);
        if (isSuccess) {
            return Result.returnSuccessResult(RequestSession.getRequestId());
        } else {
            throw new ActivityException("start failed.");
        }
    }


    @ApiOperation(value = "检查实例是否启动(检查SQL是否可执行)")
    @PostMapping("/check_started")
    public Result<String> checkStarted(@RequestParam(name = "replicaId") Long replicaId) throws Exception {
        SQLResult sqlResult = sqlBaseExecutor.execSQLInReplica(replicaId, new MySQLCommand("select 1"));
        if (!sqlResult.isSuccess()) {
            // TODO fetch mysql-error.log
            throw new NotRetryException(String.format("replica %s not started yet!", replicaId));
        }
        return Result.returnSuccessResult(RequestSession.getRequestId());
    }


    @ApiOperation(value = "检查实例备库磁盘的健康状态")
    @PostMapping("/check_slave_disk_health")
    public Result<Boolean> checkSlaveDiskHealth(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                @RequestParam(name = "replicaId", required = false) Long replicaId,
                                                @RequestParam(name = "replicationType", required = false) String replicationType,
                                                @RequestParam(name = "destDiskSizeMB", required = false) Long destDiskSizeMB) throws Exception {
        ReplicaSet replicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), replicaSetName, false);
        if(!replicaSetMetaHelper.isTmpIns(replicaSet)){
            log.info("is not tmp ins ,skip check.");
            return Result.returnSuccessResult(true);
        }
        MysqlService mysqlService;
        if (StringUtils.isNotEmpty(replicationType)) {
            mysqlService = mysqlServiceFactory.get(replicaSet.getCategory(), replicationType);
        } else {
            mysqlService = mysqlServiceFactory.get(replicaSet);
        }
        //检查磁盘大小，避免切换后锁定
        try {
            mysqlService.checkSlaveDiskUsageHealth(replicaSet, replicaId,destDiskSizeMB);
        } catch (NotRetryException e) {
            throw e;
        }
        return Result.returnSuccessResult(true);
    }
    @ApiOperation(value = "检查实例备库的健康状态和延迟")
    @PostMapping("/check_slave_health_and_delay")
    public Result<Boolean> checkSlaveHealthAndDelay(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                    @RequestParam(name = "replicaId", required = false) Long replicaId,
                                                    @RequestParam(name = "role", required = false) String role,
                                                    @RequestParam(name = "delayLimit", defaultValue = "5") int delayLimit,
                                                    @RequestParam(name = "waitSecondsBeforeCheck", defaultValue = "0") Long waitSecondsBeforeCheck,
                                                    @RequestParam(name = "retryCounter", defaultValue = "0") Long retryCounter,
                                                    @RequestParam(name = "replicationType", required = false) String replicationType,
                                                    @RequestParam(name = "switchFlag", defaultValue = AuroraConsts.AURORA_SWITCH_FLAG_TASK) String switchFlag,
                                                    @RequestParam(name = "targetReplicaName", required = false) String targetReplicaName) throws Exception {
        log.info("switch flag: {}, skip: {}", switchFlag, StringUtils.equals(AuroraConsts.AURORA_SWITCH_FLAG_DBA_FORCE, switchFlag) || StringUtils.equals(AuroraConsts.AURORA_SWITCH_FLAG_API_FORCE, switchFlag));
        if (StringUtils.equals(AuroraConsts.AURORA_SWITCH_FLAG_DBA_FORCE, switchFlag) || StringUtils.equals(AuroraConsts.AURORA_SWITCH_FLAG_API_FORCE, switchFlag)) {
            log.info("switch force, skip slave check.");
            return Result.returnSuccessResult(true);
        }
        // 任务流里面可能会重启replica后立即开始复制检查，此时内核状态还没达到一致，所以首次检查先等待10秒，
        if (retryCounter == 0 && waitSecondsBeforeCheck > 0) {
            Thread.sleep(waitSecondsBeforeCheck * 1000);
        }
        if (Objects.isNull(replicaId) && StringUtils.isNotEmpty(targetReplicaName)) {
            Optional<Replica> targetReplicaOption = dbaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(RequestSession.getRequestId(), replicaSetName, null, null, null, null).getItems()
                    .stream().filter(rp -> Objects.equals(rp.getName(), targetReplicaName)).findFirst();
            if (targetReplicaOption.isPresent()) {
                replicaId = targetReplicaOption.get().getId();
                log.info("find targetReplicaName {} replicaId: {}", targetReplicaName, replicaId);
            }
            else {
                throw new NotRetryException("targetReplicaName not found: " + targetReplicaName);
            }
        }

        ReplicaSet replicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), replicaSetName, false);
        MysqlService mysqlService;
        // 按时间点克隆追增量的时候，无论是普通实例还是MGR实例，主备之间必然是使用普通的复制方式。
        // 原因是，追增量的过程中，主库涉及多次重启，而MGR集群不支持关闭选主，重启主库会导致切换。
        // 从而，会出现MGR实例使用普通复制的情况，所以这里支持通过复制方式来获取service
        if (StringUtils.isNotEmpty(replicationType)) {
            mysqlService = mysqlServiceFactory.get(replicaSet.getCategory(), replicationType);
        } else {
            mysqlService = mysqlServiceFactory.get(replicaSet);
        }
        try {
            String channel = null;
            if(replicaSetMetaHelper.isReplicaSetExternalReplication(replicaSetName)){
                channel=MAINTAIN_THREAD_CHANNEL;
            }
            mysqlService.checkSlaveHealthAndDelay(replicaSetName, replicaId, role, delayLimit, retryCounter,channel);
        } catch (NotRetryException e) {
            throw e;
        }
        return Result.returnSuccessResult(true);
    }

    @ApiOperation(value = "检查多个节点的健康状态和延迟")
    @PostMapping("/check_nodes_health_and_delay")
    public Result<Boolean> checkNodesHealthAndDelay(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                    @RequestParam(name = "delayLimit", defaultValue = "5") int delayLimit,
                                                    @RequestParam(name = "waitSecondsBeforeCheck", defaultValue = "10") Long waitSecondsBeforeCheck,
                                                    @RequestParam(name = "retryCounter", defaultValue = "0") Long retryCounter,
                                                    @RequestParam(name = "replicationType", required = false) String replicationType,
                                                    @RequestParam(name = "targetReplicaName", required = false) String targetReplicaName) throws Exception {

        // 任务流里面可能会重启replica后立即开始复制检查，此时内核状态还没达到一致，所以首次检查先等待10秒，
        if (retryCounter == 0 && waitSecondsBeforeCheck > 0) {
            Thread.sleep(waitSecondsBeforeCheck * 1000);
        }
        ReplicaSet replicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), replicaSetName, false);
        MysqlService mysqlService;
        // 按时间点克隆追增量的时候，无论是普通实例还是MGR实例，主备之间必然是使用普通的复制方式。
        // 原因是，追增量的过程中，主库涉及多次重启，而MGR集群不支持关闭选主，重启主库会导致切换。
        // 从而，会出现MGR实例使用普通复制的情况，所以这里支持通过复制方式来获取service
        if (StringUtils.isNotEmpty(replicationType)) {
            mysqlService = mysqlServiceFactory.get(replicaSet.getCategory(), replicationType);
        } else {
            mysqlService = mysqlServiceFactory.get(replicaSet);
        }
        List<Replica> replicas = dbaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(RequestSession.getRequestId(), replicaSetName, null, null, null, null).getItems();
        for (Replica replica : replicas) {
            Long replicaId = replica.getId();
            String role = replica.getRole().getValue();
            try {
                mysqlService.checkSlaveHealthAndDelay(replicaSetName, replicaId, role, delayLimit, retryCounter);
            } catch (NotRetryException e) {
                throw e;
            }
        }
        return Result.returnSuccessResult(true);
    }



    @ApiOperation(value = "检查实例备库的sql_delay是否设置成功")
    @PostMapping("/check_slave_sql_delay")
    public Result<String> checkSlaveSqlDelay(@RequestParam(name = "replicaSetName") String replicaSetName,
                                             @RequestParam(name = "delaySeconds") int delaySeconds) throws Exception {
        ReplicaListResult replicaListResult = dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(RequestSession.getRequestId(), replicaSetName, null, null, null, null);
        for (Replica item : replicaListResult.getItems()) {
            Long replicaId = item.getId();
            SlaveStatus slaveStatus = mysqlBizService.getSlaveStatus(replicaSetName, replicaId);
            if (slaveStatus == null) {
                throw new NotRetryException("Slave replica not found!");
            }
            boolean isXdb = slaveStatus.isXdb();
            log.info("Slave status is {} replicaId {}", slaveStatus.getSlaveInfoStr(), replicaId);
            if (!slaveStatus.isSlaveSQLRunning()) {
                throw new NotRetryException("SQL running is No, Error msg is " + slaveStatus.getLastSQLError());
            }
            // XDB不检查IO_RUNNING
            if (!isXdb && !slaveStatus.isSlaveIORunning()) {
                throw new NotRetryException("IO running is No, Error msg is " + slaveStatus.getLastIOError());
            }
            if (slaveStatus.getSqlDelay() != delaySeconds) {
                throw new NotRetryException(ErrorInfo.SLAVE_DATA_SYNC_IN_PROCESSING, RequestSession.getRequestId());
            }
        }
        return Result.returnSuccessResult(RequestSession.getRequestId());
    }

    @RetryAnnotation(timeout = 900, retry = 4, interval = 30)
    @ApiOperation(value = "检查临时实例是否可删除")
    @PostMapping("/check_drop_tmp_ins")
    public Result<String> checkDropTmpIns(@RequestParam(name = "replicaSetName") List<String> replicaSetNames) throws Exception {
        for (String replicaSetName : replicaSetNames) {
            if (StringUtils.isEmpty(replicaSetName)) {
                log.warn("skip when replicaSetName is empty", replicaSetName);
                continue;
            }
            ReplicaSet replicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), replicaSetName, true);
            if (null == replicaSet) {
                log.warn("replicaSet:{} already in deleting, skip", replicaSetName);
                continue;
            }
            if (replicaSet.getInsType() != ReplicaSet.InsTypeEnum.TMP) {
                throw new CheckException("current replicaSet is not tmp ins");
            }

            Map<String, String> config = resourceKeyUtil.getMapConfig(ResourceKeyConsts.RES_KEY_DROP_TMP_INS_CHECK_CONFIG);
            boolean checkMaster = Boolean.parseBoolean(config.getOrDefault("checkMaster", "true"));
            boolean checkGtid = Boolean.parseBoolean(config.getOrDefault("checkGtid", "true"));
            Integer gtidThreshold = Integer.parseInt(config.getOrDefault("gtidThreshold", "10"));

            MysqlService service = mysqlServiceFactory.get(replicaSet);
            if (checkMaster) {
                List<Replica> replicas = replicaSetMetaHelper.getReplicas(replicaSetName);
                for (Replica replica : replicas) {
                    log.info("start check replica {} is not master", replica.getId());
                    service.checkCurrentIsNotMaster(replica.getId());
                }
            }

            // because check gtid cost lots of time
            // and serverless does not support check master from maxscale now
            // so just check gtid for serverless
            // external replication instance skip check gtid
            if (!ReplicaSetMetaHelper.isServerless(replicaSet)
                    || replicaSetMetaHelper.isReplicaSetExternalReplication(replicaSet)) {
                return Result.returnSuccessResult(RequestSession.getRequestId());
            }

            if (checkGtid) {
                log.info("start check replicaSet {} gtid is contains in primary ins", replicaSetName);
                service.checkTmpInsGtidBehindPrimary(replicaSetName, gtidThreshold);
            }
        }
        return Result.returnSuccessResult(RequestSession.getRequestId());
    }

    @ApiOperation(value = "下发删除临时实例任务")
    @PostMapping("/start_drop_tmp_ins")
    public Result<String> startDropTmpIns(@RequestParam(name = "replicaSetName") List<String> replicaSetNames,
                                          @RequestParam(name = "skipKillProcess", defaultValue = "true", required = false) String skipKillProcess,
                                          @RequestParam(name = "taskKey", required = false) String taskKey
    ) throws Exception {
        for (String replicaSetName : replicaSetNames) {
            if (StringUtils.isEmpty(replicaSetName)) {
                log.warn("skip when replicaSetName is empty", replicaSetName);
                continue;
            }
            ReplicaSet replicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), replicaSetName, true);
            if (null == replicaSet) {
                log.warn("replicaSet:{} already in deleting, skip", replicaSetName);
                continue;
            }
            if (replicaSet.getInsType() != ReplicaSet.InsTypeEnum.TMP) {
                throw new CheckException("current replicaSet is not tmp ins");
            }
            // 为了防止残留脏实例，这里不进行skip，但如果未来发现有较多冲突（重复下发删除任务的情况），可以将该代码块放开预防冲突
            if (replicaSet.getStatus().equals(ReplicaSet.StatusEnum.DELETING)) {
                log.warn("replicaSet:{} already in deleting, skip", replicaSetName);
                continue;
            }

            //If Serverless instance
            if (ReplicaSetMetaHelper.isServerless(replicaSet)) {
                log.info("replicaSet:{} category is{},use delete serverless ins task", replicaSetName,replicaSet.getCategory());
                taskKey = "delete_serverless_ins";
            }

            taskKey = taskKey != null?taskKey:"delete_ins";
            workFlowBaseService.dispatchTask(replicaSetName, taskKey, skipKillProcess != null ? "{\"skip_kill_process\":true}" : null, null);
            dBaasMetaClient.getDefaultmetaApi().updateReplicaSetStatus(RequestSession.getRequestId(), replicaSetName, ReplicaSet.StatusEnum.DELETING.toString());
        }
        return Result.returnSuccessResult(RequestSession.getRequestId());
    }

    @ApiOperation(value = "杀掉MySQL用户进程")
    @PostMapping("/kill_all_processes")
    public Result<String> killAllProcesses(@RequestParam(name = "replicaSetName", required = false) String replicaSetName,
                                           @RequestParam(name = "replicaId", required = false) List<Long> replicaIds,
                                           @RequestParam(name = "ignoreException", defaultValue = "false") boolean ignoreException)
            throws Exception {
        replicaIds = Objects.isNull(replicaIds) ? new ArrayList<>() : replicaIds;
        try {
            Set<String> sysUsers = Sets.newHashSet("system", "replicator", "aurora", "Xtrabak", "aliyun_root", "event_scheduler", "unauthenticated");

            if (replicaIds.size() == 0) {
                List<Replica> replicas = dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(RequestSession.getRequestId(), replicaSetName, null, null, null, null).getItems();
                Optional<Replica> masterReplica = replicas.stream().filter(r -> r.getRole() == Replica.RoleEnum.MASTER).findFirst();
                if (masterReplica.isPresent()) {
                    replicaIds.add(masterReplica.get().getId());
                }
            }
            if (replicaIds.size() != 0) {
                for (var replicaId : replicaIds) {
                    log.info("start kill replica {}", replicaId);
                    int retry = 0;
                    while (retry++ < 3) {
                        SQLResult sqlResult = sqlBaseExecutor.execQueryInReplica(replicaId, new MySQLCommand("show processlist;"));
                        if (sqlResult == null || sqlResult.getRawResult().contains("connect db failed")) {
                            log.warn("connect db failed:{}", sqlResult == null ? null : sqlResult.getRawResult());
                            break;
                        } else if (!sqlResult.isSuccess()) {
                            if (ignoreException) {
                                break;
                            } else {
                                throw new NotRetryException("show processlist failed: " + sqlResult.getRawResult());
                            }
                        }
                        // Id       | User         | Host                 | db    | Command | Time  | State          | Info
                        StringBuilder killSql = new StringBuilder();
                        sqlResult.getResultMap().stream()
                                .filter(r -> !sysUsers.contains(r.get("User").toString()))
                                .forEach(v -> killSql.append(String.format("kill %s;", v.get("Id").toString())));

                        if (killSql.length() > 0) {
                            try {
                                SQLResult killResult = sqlBaseExecutor.execSQLInReplica(replicaId, new MySQLCommand(killSql.toString()));
                                log.info("kill result:{}", killResult.getResult());
                            } catch (Exception e) {
                                log.warn(e.getMessage());
                                continue;
                            }
                        }
                        break;
                    }
                }
            } else {
                log.warn("replica not provided, and cannot find master role, nothing to do");
            }
        } catch (Exception e) {
            log.error("kill process failed.", e);
            if (!ignoreException) {
                throw e;
            }
        }
        return Result.returnSuccessResult(RequestSession.getRequestId());
    }

    @ApiOperation(value = "通过对mysql进行kill，依赖k8s的自动拉起实现重启功能，返回kill的pid")
    @PostMapping("/restart_mysqld_by_kill")
    public Result<ImmutableMap<String, Serializable>> restartMysqldByKill(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                                          @RequestParam(name = "replicaId") Long replicaId,
                                                                          @RequestParam(name = "signal", defaultValue = "15") String signal,
                                                                          @RequestParam(name = "notThrowable", required = false, defaultValue = "false") Boolean notThrowable,
                                                                          @RequestParam(name = "checkRole", required = false, defaultValue = "false") Boolean checkRole) throws Exception {
        ReplicaResource replicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), replicaId, false);
        ReplicaSet replicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), replicaSetName, false);
        if (ReplicaSetMetaHelper.isAliYun(replicaSet.getBizType())) {
            MysqlService mysqlService = mysqlServiceFactory.get(replicaSet);
            try {
                if (checkRole != null && checkRole && !Replica.RoleEnum.SLAVE.equals(replicaResource.getReplica().getRole())) {
                    throw new ActivityException("current replica's role is not slave, can not stop resource node");
                }
                return mysqlService.restart(replicaSet, replicaResource);
            } catch (Exception e) {
                if (notThrowable) {
                    return Result.returnFailureResult(SERVICE_RETURN_CODE_INTERNAL_ERROR, e.getMessage());
                } else {
                    throw e;
                }
            }
        }

        String pidPath = String.format(PID_FILE, replicaResource.getReplica().getPorts().get(0));
        CmdStatusOutput cmdStatusOutput = commonProviderClient.getDefaultApi().execCmdInReplicaGetStatusOutput(RequestSession.getRequestId(), replicaSetName, replicaId,
                String.format("cat %s", pidPath));
        String output = cmdStatusOutput.getDetailLog().trim();
        if (StringUtils.isEmpty(output)) {
            throw new ActivityException("pid cannot find in file, retry it");
        }
        Long pid = LangUtil.getLong(output);
        cmdStatusOutput = commonProviderClient.getDefaultApi().execCmdInReplicaGetStatusOutput(RequestSession.getRequestId(), replicaSetName, replicaId, String.format("stat /proc/%s | grep Modify", pid));
        Matcher matcher = MysqlConsts.patternStatModifyTime.matcher(cmdStatusOutput.getDetailLog());
        if (!matcher.find()) {
            log.error("cannot find {}", String.format("stat /proc/%s | grep Modify", pid));
            throw new ActivityException(ErrorCode.SERVICE_RETURN_COMMAND_ERROR);
        }
        String previousStartTime = matcher.group(0);
        boolean isXDB = replicaSetMetaHelper.isXdbReplicaSet(replicaSetName);

        if (isXDB) {
            xdbBizService.shutdownMysql(replicaResource);
        } else {
            mysqlBizService.killMysql(replicaResource, pid, signal);
        }
        log.info("shutdown mysql success,  pid is {}", pid);
        workFlowBaseService.updateWorkflowContext(ImmutableMap.of(ContextKeyConsts.MYSQL_KILL_TIME, System.currentTimeMillis()));
        return Result.returnSuccessResult(ImmutableMap.of("pid", pid, "startTime", previousStartTime));
    }

    @ApiOperation(value = "通过对xdb进程进行kill，依赖k8s的自动拉起实现重启功能，返回kill的pid")
    @PostMapping("/restart_mysqld_by_kill_and_check")
    public Result<ImmutableMap<String, Serializable>> restartMysqldByKillAndCheck
            (@RequestParam(name = "replicaSetName") String replicaSetName,
             @RequestParam(name = "replicaId") Long replicaId,
             @RequestParam(name = "signal", defaultValue = "15") String signal,
             @RequestParam(name = "force", required = false, defaultValue = "false") Boolean force) throws Exception {
        if (!force && !replicaSetMetaHelper.isSingleNode(replicaSetName)) {
            boolean isXDB = replicaSetMetaHelper.isXdbReplicaSet(replicaSetName);
            if (isXDB) {
                xdbBizService.checkCurrentIsNotMaster(replicaId);
            } else {
                ReplicaResource replicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), replicaId, false);
                MysqlService mysqlService = mysqlServiceFactory.get(replicaResource);
                mysqlService.checkCurrentIsNotMaster(replicaId);
            }
        }
        return restartMysqldByKill(replicaSetName, replicaId, signal, false, false);
    }

    @ApiOperation(value = "检查pid是否发生变化，变化则认为已重启，返回该步骤花费的时间(ms), 返回-1表示超时未完成重启")
    @PostMapping("/check_mysqld_restarted")
    public Result<Long> checkMysqldRestarted(@RequestParam(name = "replicaSetName") String replicaSetName,
                                             @RequestParam(name = "replicaId") Long replicaId,
                                             @RequestParam(name = "previousPid") Long previousPid,
                                             @RequestParam(name = "previousStartTime") String previousStartTime,
                                             @RequestParam(name = "timeout", defaultValue = "0") Long timeout,
                                             @RequestParam(name = "retryCounter", defaultValue = "0") Long retryCounter,
                                             @RequestParam(name = "maxRetryTimes", defaultValue = "1") Long maxRetryTimes) throws Exception {
        long begin = LangUtil.getLong(workFlowBaseService.getWorkflowContext().get(ContextKeyConsts.MYSQL_KILL_TIME));
        try {
            ReplicaResource replicaResource = dBaasMetaClient
                    .getDefaultmetaApi()
                    .getReplica(RequestSession.getRequestId(), replicaId, false);

            ReplicaSet replicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), replicaSetName, false);
            if (ReplicaSetMetaHelper.isAliYun(replicaSet.getBizType())) {
                MysqlService mysqlService = mysqlServiceFactory.get(replicaSet);
                return mysqlService.isRestarted(replicaSet, replicaResource, timeout, maxRetryTimes, retryCounter);
            }

            // 检查重启前的Pid是否在存在
            String checkCmd = String.format("if [ -d /proc/%s ]; then echo true;fi", previousPid);
            CmdStatusOutput cmdStatusOutput = commonProviderClient
                    .getDefaultApi()
                    .execCmdInReplicaGetStatusOutput(RequestSession.getRequestId(), replicaSetName, replicaId, checkCmd);
            log.info("{}, {}", checkCmd, cmdStatusOutput.toString());

            boolean isCmdSuc = cmdStatusOutput.getExitedCode() != null && cmdStatusOutput.getExitedCode() == 0;
            boolean isProcExist = "true".equals(cmdStatusOutput.getDetailLog().trim());
            // Pid已经不存在，证明重启成功
            if (isCmdSuc && !isProcExist) {
                return Result.returnSuccessResult(System.currentTimeMillis() - begin);
            }

            // 进程启动时间发生变化，证明mysql重启成功(重启前后PID不变)
            checkCmd = String.format("stat /proc/%s | grep Modify", previousPid);
            cmdStatusOutput = commonProviderClient
                    .getDefaultApi()
                    .execCmdInReplicaGetStatusOutput(RequestSession.getRequestId(), replicaSetName, replicaId, checkCmd);
            log.info("{}, {}", checkCmd, cmdStatusOutput.toString());

            isCmdSuc = cmdStatusOutput.getExitedCode() != null && cmdStatusOutput.getExitedCode() == 0;
            if (isCmdSuc) {
                Matcher matcher = MysqlConsts.patternStatModifyTime.matcher(cmdStatusOutput.getDetailLog());
                matcher.find();
                String startTime = matcher.group(0);
                if (startTime.compareTo(previousStartTime) > 0) {
                    return Result.returnSuccessResult(System.currentTimeMillis() - begin);
                }
            }

            // 重启超时情况下，kill 9 mysql
            if (timeout > 0 && System.currentTimeMillis() - begin > timeout * 1000) {
                boolean isKilled = workFlowBaseService.getWorkflowContext().get(ContextKeyConsts.MYSQL_KILL_9) != null;
                if (!isKilled) {
                    log.info("wait mysql restart timeout, kill 9 mysql");
                    workFlowBaseService.updateWorkflowContext(ImmutableMap.of(ContextKeyConsts.MYSQL_KILL_9, System.currentTimeMillis()));
                    mysqlBizService.killMysql(replicaResource, previousPid, "9");
                }
            }
        } catch (Exception ignored) {
            log.error("check failed.", ignored);
        }
        if (maxRetryTimes - 1 > retryCounter) {
            throw new InProcessingException(ErrorInfo.ACTIVITY_IN_PROCESSING, "last process not exited yet, waiting next retry");
        }
        // last retry failed by timeout, do not throw exception for next step can kill force.
        return Result.returnSuccessResult((long) -1);
    }


    @ApiOperation(value = "检查Replica是否可可服务（远程方式探测）")
    @PostMapping("/check_replica_available")
    public Result<Boolean> checkReplicaAvailable(@RequestParam(name = "replicaId") Long replicaId,
                                                 @RequestParam(name = "timeout", defaultValue = "0") Long timeout) throws Exception {
        // 指定timeout时，在超时后直接返回false，便于任务流做下一步处理
        if (timeout > 0) {
            Long begin = null;
            if (workFlowBaseService.getWorkflowContext() != null) {
                begin = LangUtil.getLong(workFlowBaseService.getWorkflowContext().get(ContextKeyConsts.STEP_START_TIME));
            }

            if (begin == null) {
                workFlowBaseService.updateWorkflowContext(ImmutableMap.of(ContextKeyConsts.STEP_START_TIME, System.currentTimeMillis()));
            } else if (System.currentTimeMillis() - begin > timeout * 1000) {
                workFlowBaseService.updateWorkflowContext(ImmutableMap.of(ContextKeyConsts.STEP_START_TIME, ""));
                return Result.returnSuccessResult(false);
            }
        }

        SQLResult sqlResult = sqlBaseExecutor.execQueryInReplica(replicaId, new MySQLCommand("SELECT 1"));
        if (!sqlResult.isSuccess()) {
            log.error("Replica is still not available, Error is {}", sqlResult.getResult());
            throw new InProcessingException(ErrorInfo.ACTIVITY_IN_PROCESSING, RequestSession.getRequestId());
        }

        return Result.returnSuccessResult(true);
    }


    @ApiOperation(value = "检查Replica是否可服务（本地方式探测）")
    @PostMapping("/check_replica_available_in_local")
    public Result<Boolean> checkReplicaAvailableInLocal(@RequestParam(name = "replicaId") Long replicaId,
                                                        @RequestParam(name = "user", defaultValue = "aliyun_root") String user) throws Exception {
        ReplicaResource replicaResource = dbaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), replicaId, false);
        Replica currentReplica = replicaResource.getReplica();
        MysqlService mysqlService = mysqlServiceFactory.get(currentReplica);
        boolean isReplicaAvailable = mysqlService.isReplicaAvailable(currentReplica, user);
        if (isReplicaAvailable) {
            return Result.returnSuccessResult(true);
        } else {
            // 部分场景会在没有刷账号的情况下，通过这个接口检查可用性，所以root和aliyun_root都使用来检测
            user = "aliyun_root".equals(user) ? "root" : "aliyun_root";
            isReplicaAvailable = mysqlService.isReplicaAvailable(currentReplica, user);
            if (isReplicaAvailable) {
                return Result.returnSuccessResult(true);
            } else {
                log.error("Replica [{}] is still not available.", replicaId);
                throw new InProcessingException(ErrorInfo.ACTIVITY_IN_PROCESSING, RequestSession.getRequestId());
            }

        }
    }

    @ApiOperation(value = "检查实例是否已经无法连接")
    @PostMapping("/check_replica_is_down")
    public Result<Boolean> checkReplicaIsDown(@RequestParam(name = "replicaId", required = false) Long replicaId,
                                              @RequestParam(name = "user", defaultValue = "aliyun_root") String user,
                                              @RequestParam(name = "replicaSetName", required = false) String replicaSetName,
                                              @RequestParam(name = "role", required = false) String role) throws Exception {
        if (replicaId == null && (replicaSetName == null || role == null)) {
            throw new NotRetryException("replicaId or role must be set.");
        }
        Replica replica;
        if (replicaId == null) {
            replica = replicaSetMetaHelper.getReplica(replicaSetName, role);
            replica.setReplicaSetName(replicaSetName);
        } else {
            replica = dbaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), replicaId, false).getReplica();
        }
        boolean isReplicaAvailable = mysqlServiceFactory.get(replica).isReplicaAvailable(replica, user);
        return Result.returnSuccessResult(!isReplicaAvailable);
    }

    @ApiOperation(value = "Replace rs 4 endpoint")
    @PostMapping("/replace_endpoint_real_server")
    public Result<String> replaceEndPointRealServer(
            @RequestParam(name = "replicaId") Long replicaId,
            @RequestParam(name = "replicaSetName") String replicaSetName
    ) throws Exception {
        ReplicaSet replicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(
                RequestSession.getRequestId(), replicaSetName, null);
        if (ReplicaSetMetaHelper.isServerless(replicaSet)) {
            log.info("replicaSet is serverless, skip replace real server");
            return Result.returnSuccessResult(RequestSession.getRequestId());
        }

        EndpointListResult endpointListResult = dBaasMetaClient.getDefaultmetaApi().listReplicaSetEndpoints(RequestSession.getRequestId(),
                replicaSetName, null, null, null, null);
        for (Endpoint e : endpointListResult.getItems()) {
            commonProviderClient.getDefaultApi().replaceEndPointRealServer(RequestSession.getRequestId(), replicaSetName, e.getVip(), Collections.singletonList(replicaId));
        }
        return Result.returnSuccessResult(RequestSession.getRequestId());
    }

    @ApiOperation(value = "重置Slave指向不存在的目标端，用于生成relay log")
    @PostMapping("/change_slave_master")
    public Result<Boolean> changeSlaveMaster(@RequestParam(name = "replicaId") Long replicaId,
                                             @RequestParam(name = "ip") String ip,
                                             @RequestParam(name = "port") Integer port) throws Exception {
        String channel = null;
        ReplicaResource replica = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), replicaId, null);
        if(replicaSetMetaHelper.isReplicaSetExternalReplication(replica.getReplicaSetName())) {
            channel = MAINTAIN_THREAD_CHANNEL;
        }
        mysqlBizService.changeMaster(replicaId, ip, port, channel);
        return Result.returnSuccessResult(true);
    }

    @ApiOperation(value = "删除复制连接信息")
    @PostMapping("/reset_slave")
    public Result<Boolean> resetSlave(@RequestParam(name = "replicaId") Long replicaId,
                                      @RequestParam(name = "isResetSlaveAll", defaultValue = "true") Boolean isResetSlaveAll,
                                      @RequestParam(name = "execCmd", defaultValue = "false") Boolean execCmd) throws Exception {
        DefaultApi metaApi = dBaasMetaClient.getDefaultmetaApi();
        ReplicaResource replicaResource = metaApi.getReplica(RequestSession.getRequestId(), replicaId, false);
        if (replicaSetMetaHelper.isReplicaSetExternalReplication(replicaResource.getReplicaSetName())) {
            replicationBizService.resetSlaveForExternalReplication(replicaResource);
        } else {
            SQLTemplateSet.SQLCommand cmd = isResetSlaveAll ? SQLTemplateSet.SQLCommand.RESET_SLAVE_ALL_CMD : SQLTemplateSet.SQLCommand.RESET_SLAVE_CMD;
            String template = SQLTemplateSet.getSQLTemplateCommand(CommonConsts.DB_TYPE_MYSQL, cmd);
            if (execCmd) {
                CmdStatusOutput cmdStatusOutput = actionCmdManager.runCmdInReplicaUsingSQL(replicaResource.getReplicaSetName(), replicaResource.getReplica(), template);
                if (cmdStatusOutput.getExitedCode() != 0) {
                    log.error("reset slave failed, {}", cmdStatusOutput.getDetailLog());
                    throw new ActivityException("reset slave failed");
                }
            } else {
                SQLResult sqlResult = sqlBaseExecutor.execSQLInReplica(replicaId, new MySQLCommand(template));
                if (!sqlResult.isSuccess()) {
                    throw new ActivityException("delete replication conn info error, " + sqlResult.getResult());
                }
            }
        }
        return Result.returnSuccessResult(true);
    }


    @ApiOperation(value = "启动复制线程")
    @PostMapping("/start_slave")
    public Result<Boolean> startSlave(@RequestParam(name = "replicaId") Long replicaId,
                                      @RequestParam(name = "execCmd", defaultValue = "false") Boolean execCmd) throws Exception {
        DefaultApi metaApi = dBaasMetaClient.getDefaultmetaApi();
        ReplicaResource replicaResource = metaApi.getReplica(RequestSession.getRequestId(), replicaId, false);
        String template = SQLTemplateSet.getSQLTemplateCommand(CommonConsts.DB_TYPE_MYSQL, SQLTemplateSet.SQLCommand.START_SLAVE_CMD);
        if (execCmd) {
            CmdStatusOutput cmdStatusOutput = actionCmdManager.runCmdInReplicaUsingSQL(replicaResource.getReplicaSetName(), replicaResource.getReplica(), template);
            if (cmdStatusOutput.getExitedCode() != 0) {
                log.error("start slave failed, {}", cmdStatusOutput.getDetailLog());
                throw new ActivityException("start slave failed");
            }
        } else {
            SQLResult sqlResult = sqlBaseExecutor.execSQLInReplica(replicaId, new MySQLCommand(template));
            if (!sqlResult.isSuccess()) {
                throw new ActivityException("start replication thread error, " + sqlResult.getResult());
            }
        }
        return Result.returnSuccessResult(true);
    }

    @ApiOperation(value = "暂停复制线程")
    @PostMapping("/stop_slave")
    public Result<Boolean> stopSlave(@RequestParam(name = "replicaId") Long replicaId,
                                      @RequestParam(name = "execCmd", defaultValue = "false") Boolean execCmd) throws Exception {
        DefaultApi metaApi = dBaasMetaClient.getDefaultmetaApi();
        ReplicaResource replicaResource = metaApi.getReplica(RequestSession.getRequestId(), replicaId, false);
        String template = SQLTemplateSet.getSQLTemplateCommand(CommonConsts.DB_TYPE_MYSQL, SQLTemplateSet.SQLCommand.STOP_SLAVE_CMD);
        if (execCmd) {
            CmdStatusOutput cmdStatusOutput = actionCmdManager.runCmdInReplicaUsingSQL(replicaResource.getReplicaSetName(), replicaResource.getReplica(), template);
            if (cmdStatusOutput.getExitedCode() != 0) {
                log.error("stop slave failed, {}", cmdStatusOutput.getDetailLog());
                throw new ActivityException("stop slave failed");
            }
        } else {
            SQLResult sqlResult = sqlBaseExecutor.execSQLInReplica(replicaId, new MySQLCommand(template));
            if (!sqlResult.isSuccess()) {
                throw new ActivityException("stop replication thread error, " + sqlResult.getResult());
            }
        }
        return Result.returnSuccessResult(true);
    }

    /**
     * pengine迁移新架构忽略
     *
     * @param replicaSetName
     * @param replicaId
     * @param srcReplicaSetName
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "跨VPC搭建复制同步关系")
    @PostMapping("/cross_vpc_build_replication")
    public Result<String> crossVPCBuildReplication(
            @RequestParam(name = "replicaSetName") String replicaSetName,
            @RequestParam(name = "replicaId") Long replicaId,
            @RequestParam(name = "srcReplicaSetName") String srcReplicaSetName
    ) throws Exception {
        DefaultApi metaApi = dBaasMetaClient.getDefaultmetaApi();
        String srcMasterIp = "";
        String srcMasterPort = "";
        ReplicaSetResource srcReplicasetResource = metaApi.getReplicaSetBundleResource(RequestSession.getRequestId(), srcReplicaSetName);
        ReplicaSet replicaSet = metaApi.getReplicaSetBundleResource(RequestSession.getRequestId(), replicaSetName).getReplicaSet();

        for (ReplicaResource resource : srcReplicasetResource.getReplicaResources()) {
            if (resource.getReplica().getRole().toString().equalsIgnoreCase(Replica.RoleEnum.MASTER.toString())) {
                VpcInnerMapping srcVpcInnerMapping = dBaasMetaClient.getDefaultmetaApi().getReplicaSetVpcInnerMapping(RequestSession.getRequestId(),
                        resource.getReplicaSetName(),
                        resource.getReplica().getCtrlIp(),
                        resource.getReplica().getPorts().get(0), null);
                srcMasterIp = srcVpcInnerMapping.getMappingIp();
                srcMasterPort = srcVpcInnerMapping.getMappingPort().toString();
                break;
            }
        }

        // 获取复制账号
        Account account = metaApi.getReplicaSetAccount(RequestSession.getRequestId(),
                srcReplicasetResource.getReplicaSet().getName(), AccountConsts.ACCOUNT_REPLICATE, false);
        if (account == null) {
            throw new CheckException("cannot find Replica's aurora replicate");
        }

        // 搭建复制
        String template = SQLTemplateSet.getSQLTemplateCommand(CommonConsts.DB_TYPE_MYSQL, SQLTemplateSet.SQLCommand.BUILD_REPLICATION_CMD);
        SQLResult sqlResult = sqlBaseExecutor.execSQLInReplica(replicaId, new MySQLCommand(template, srcMasterIp, account.getBackwardAccount(), AesCfb.decrypt(account.getBackwardPassword()), srcMasterPort));
        if (!sqlResult.isSuccess()) {
            throw new ActivityException("change master error, " + sqlResult.getResult());
        }

        return Result.returnSuccessResult(RequestSession.getRequestId());
    }

    @ApiOperation(value = "清理Replica里面的/slave-relay.*")
    @PostMapping("/clear_relay_log")
    public Result<Boolean> clearRelayLog(@RequestParam(name = "replicaId") Long replicaId) throws Exception {
        try {
            ReplicaResource replicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), replicaId, false);
            mysqlBizService.clearRelayLog(replicaResource.getReplica());
        } catch (Exception e) {
            log.error("clear relay log failed", e);
            throw new NotRetryException(String.format("clear relay log failed %s", e.getMessage()));
        }
        return Result.returnSuccessResult(true);
    }

    @ApiOperation(value = "切换基础版的RS(适配只读)")
    @PostMapping("/replace_endpoint_real_server_for_basic")
    public Result<Boolean> replaceEndpointRsForBasic(@RequestParam(name = "srcReplicaId") Long srcReplicaId,
                                                     @RequestParam(name = "destReplicaId") Long destReplicaId,
                                                     @RequestParam(name = "force", defaultValue = "false") Boolean force
    ) throws Exception {
        ReplicaResource srcReplicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), srcReplicaId, false);
        ReplicaResource destReplicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), destReplicaId, false);
        ReplicaSet srcReplicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), srcReplicaResource.getReplicaSetName(), false);
        ReplicaSet destReplicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), destReplicaResource.getReplicaSetName(), false);
        boolean isTmpReplicaSet = destReplicaSet.getInsType() == ReplicaSet.InsTypeEnum.MIRROR || destReplicaSet.getInsType() == ReplicaSet.InsTypeEnum.TMP;
        if (!isTmpReplicaSet || (srcReplicaSet.getInsType() == ReplicaSet.InsTypeEnum.MAIN && !Objects.equals(destReplicaSet.getPrimaryInsName(), srcReplicaResource.getReplicaSetName()))) {
            log.info(destReplicaSet.toString());
            throw new NotRetryException("dest replicaSet is not TMP type or PrimaryInsName != srcReplicaSetName.");
        }
        try {
            if (srcReplicaSet.getInsType() == ReplicaSet.InsTypeEnum.READONLY) {
                mysqlBizService.switchRS4BasicReadonly(srcReplicaId, destReplicaId);
            } else if (replicaSetMetaHelper.isReplicaSetExternalReplication(srcReplicaSet.getName())) {
                mysqlBizService.switchRSForExternalReplication(srcReplicaId, destReplicaId, MAINTAIN_THREAD_CHANNEL, force);
            } else {
                mysqlBizService.switchRS4Basic(srcReplicaId, destReplicaId, null);  // basic ins only switch normal endpoint.
            }
        } catch (NotRetryException | InProcessingException e) {
            throw e;
        } catch (Exception e) {
            log.error("switch rs failed!", e);
            throw new NotRetryException(e.getMessage());
        }

        return Result.returnSuccessResult(true);
    }

    //集群版交换只读、直连链路的endpoint
    @ApiOperation(value = "Replace rs 4 slave endpoints")
    @PostMapping("/replace_endpoint_real_server_for_slave_endpoints")
    public Result<String> replaceEndPointRealServerForEndpoints(
            @RequestParam(name = "replicaSetName") String replicaSetName,
            @RequestParam(name = "replicaMapping") String replicaMapping
    ) throws Exception {
        Map<String, String> replicaMappingMap = StringUtil.mapStringToMap(replicaMapping);
        log.info("replicaMapping:{}", JSON.toJSONString(replicaMappingMap));
        List<Replica> srcReplicas = dBaasMetaClient.getDefaultmetaApi()
                .listReplicasInReplicaSet(RequestSession.getRequestId(), replicaSetName, null, null, null, null)
                .getItems();
        List<Endpoint> endpoints = dBaasMetaClient.getDefaultmetaApi().listReplicaSetEndpoints(RequestSession.getRequestId(),
                replicaSetName, null, null, null, null).getItems();
        List<EndpointGroup> endpointGroups = dbaasMetaClient.getDefaultmetaApi().listEndpointGroups(RequestSession.getRequestId(), replicaSetName).getItems();
        log.info("endpointGroups:{}", JSON.toJSONString(endpointGroups));
        //直连链路
        List<EndpointGroup> directEndpointList = endpointGroups.stream().filter(ep -> ep.getType().toString().equalsIgnoreCase(EndPoint.EndPointTypeEnum.NODE.getValue()) && ep.getStatus() == 1).collect(Collectors.toList());
        for (EndpointGroup epGroup : directEndpointList) {
            EndpointGroup endpointGroup = dbaasMetaClient.getDefaultmetaApi().getEndpointGroup(RequestSession.getRequestId(),replicaSetName, epGroup.getGroupName(),false);
            Endpoint endpoint = endpoints.stream().filter(ep -> Objects.equals(ep.getEndpointGroupId(), endpointGroup.getId())).findFirst().get();
            String srcNodeName = endpointGroup.getLabels().get(EndpointWeightConfig.NODE_KEY);
            if (srcNodeName == null) {
                throw new NotRetryException("Failed to find endpoint config");
            }
            Long srcReplicaId = srcReplicas.stream().filter(replica -> replica.getName().equalsIgnoreCase(srcNodeName)).findFirst().get().getId();
            Long destReplicaId = LangUtil.getLong(replicaMappingMap.get(srcReplicaId.toString()));
            log.info("srcReplicaId:{} destReplicaId:{}", srcReplicaId, destReplicaId);
            log.info("start srcReplicaId:{}", srcReplicaId);
            commonProviderClient.getDefaultApi().replaceEndPointRealServer(RequestSession.getRequestId(), replicaSetName, endpoint.getVip(), Collections.singletonList(destReplicaId));
            log.info("end srcReplicaId:{}", srcReplicaId);
        }
        //只读链路
        List<EndpointGroup> readOnlyEndpointList = endpointGroups.stream().filter(ep -> ep.getType().toString().equalsIgnoreCase(EndPoint.EndPointTypeEnum.READONLY.getValue()) && ep.getStatus() == 1).collect(Collectors.toList());
        for (EndpointGroup roEpGroup : readOnlyEndpointList) {
            EndpointGroup endpointGroup = dbaasMetaClient.getDefaultmetaApi().getEndpointGroup(RequestSession.getRequestId(),replicaSetName, roEpGroup.getGroupName(),false);
            EndpointWeightConfig endpointWeightConfig = JSONObject.parseObject(endpointGroup.getLabels().get(EndpointWeightConfig.CONFIG_KEY), EndpointWeightConfig.class);
            List<Long> roSrcReplicaIds = new ArrayList<>();
            List<Long> roDestReplicaIds = new ArrayList<>();
            for (EndpointWeightConfig.WeightConfig nodeWeight : endpointWeightConfig.getNodeWeights()) {
                String nodeId = nodeWeight.getNodeId();
                Long replicaId = srcReplicas.stream().filter(replica -> replica.getName().equalsIgnoreCase(nodeId)).findFirst().get().getId();
                roSrcReplicaIds.add(replicaId);
            }
            for (Long roSrcReplicaId : roSrcReplicaIds) {
                Long destReplicaId = LangUtil.getLong(replicaMappingMap.get(roSrcReplicaId.toString()));
                roDestReplicaIds.add(destReplicaId);
            }
            Endpoint endpoint = endpoints.stream().filter(ep -> Objects.equals(ep.getEndpointGroupId(), endpointGroup.getId())).findFirst().get();
            log.info("roSrcReplicaIds:{} roDestReplicaIds:{}", roSrcReplicaIds, roDestReplicaIds);
            log.info("start srcReplicaIds:{}", roSrcReplicaIds);
            commonProviderClient.getDefaultApi().replaceEndPointRealServer(RequestSession.getRequestId(), replicaSetName, endpoint.getVip(), roDestReplicaIds);
            log.info("end srcReplicaIds:{}", roSrcReplicaIds);
        }

        return Result.returnSuccessResult(RequestSession.getRequestId());
    }

    @ApiOperation(value = "切换基础版只读的RS")
    @PostMapping("/replace_endpoint_real_server_for_basic_readonly")
    public Result<Boolean> replaceEndpointRsForBasicReadonly(@RequestParam(name = "srcReplicaId") Long srcReplicaId,
                                                             @RequestParam(name = "destReplicaId") Long destReplicaId) throws Exception {
        ReplicaResource srcReplicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), srcReplicaId, false);
        ReplicaResource destReplicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), destReplicaId, false);
        ReplicaSet destReplicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), destReplicaResource.getReplicaSetName(), false);
        log.info("start to switch RS for srcReplicaResource:{} destReplicaResource:{} destReplicaSet:{}", JSONObject.toJSONString(srcReplicaResource),
                JSONObject.toJSONString(destReplicaResource), JSONObject.toJSONString(destReplicaSet));
        boolean isTmpReplicaSet = destReplicaSet.getInsType() == ReplicaSet.InsTypeEnum.MIRROR || destReplicaSet.getInsType() == ReplicaSet.InsTypeEnum.TMP;
        if (!isTmpReplicaSet) {
            throw new NotRetryException("dest replicaSet is not TMP type or PrimaryInsName != srcReplicaSetName.");
        }
        try {
            mysqlBizService.switchRS4BasicReadonly(srcReplicaId, destReplicaId);
        } catch (NotRetryException | InProcessingException e) {
            throw e;
        } catch (Exception e) {
            log.error("switch rs failed!", e);
            throw new NotRetryException(e.getMessage());
        }

        return Result.returnSuccessResult(true);
    }

    @ApiOperation(value = "交换域名挂载的vip")
    @PostMapping("/switch_conn_vip")
    public Result<Boolean> switchConnVip(
            @RequestParam(name = "srcReplicaSetName") String srcReplicaSetName,
            @RequestParam(name = "destReplicaSetName") String destReplicaSetName,
            @RequestParam(name = "srcReplicaId") Long srcReplicaId,
            @RequestParam(name = "destReplicaId") Long destReplicaId,
            @RequestParam(name = "delayLimit", required = false, defaultValue = "5") Integer delayLimit) throws Exception {
        String workflowId = RequestSession.getWorkflowId();
        if (StringUtils.isBlank(workflowId)) {
            throw new NotRetryException("workflowId is not set");
        }

        Map<String, Object> workflowInfo = workFlowBaseService.findWorkflowInfoById(workflowId);
        Map<String, Object> taskInfo = (Map<String, Object>) workflowInfo.get("task");
        Integer taskId = Integer.valueOf(LangUtil.getString(taskInfo.get("id")));

        // 检查待处理changelog信息
        EndpointChangeLogListResult endpointChangeLogListResult = dBaasMetaClient.getDefaultmetaApi()
                .listReplicaSetEndpointChangeLogs(RequestSession.getRequestId(), srcReplicaSetName, null, taskId);
        List<EndpointChangeLog> endpointChangeLogList = endpointChangeLogListResult.getItems();
        EndpointChangeLog changeLog = endpointChangeLogList.stream()
                .filter(v -> v.getAction() == EndpointChangeLog.ActionEnum.UPDATE).findFirst().orElse(null);

        if (changeLog == null) {
            throw new NotRetryException("Cannot find change log!");
        }

        // 保证接口幂等，不会重复切换
        if (Objects.equals(changeLog.getStatus().ordinal(), EndpointChangeLog.StatusEnum.APPLIED.ordinal())) {
            log.info("changelog {} has applied, skip", changeLog.getId());
            return Result.returnSuccessResult(true);
        }
        Replica masterReplica = mysqlBizService.findMasterReplica(srcReplicaSetName);
        String channel = null;
        if(replicaSetMetaHelper.isReplicaSetExternalReplication(srcReplicaSetName)){
            channel = MAINTAIN_THREAD_CHANNEL;
        }
        // 开始切换，异常进行回滚
        mysqlBizService.switchConnVip(masterReplica.getId(), destReplicaId, changeLog, delayLimit, channel);

        return Result.returnSuccessResult(true);
    }

    /**
     * 通过是否传proxyReplicaSetName判断切换链路的方式
     * 若传入proxy信息，则切换到代理
     * 若未传入proxy信息，则切换到目标
     * @param replicaSetName 源实例
     * @param proxyReplicaSetName 代理实例
     * @param destReplicaSetName 目标实例
     * @return true/false
     * @throws Exception 切换链路异常
     */
    @ApiOperation(value = "切换链路")
    @PostMapping("/switch_rs")
    public Result<Boolean> switch_rs(@RequestParam(name = "replicaSetName") String replicaSetName,
                                     @RequestParam(name = "proxyReplicaSetName", required = false) String proxyReplicaSetName,
                                     @RequestParam(name = "destReplicaSetName") String destReplicaSetName,
                                     @RequestParam(name = "direction") String direction) throws Exception {
        // 若传了代理信息，则切换到代理; 若未传，切换到临时实例
        if (StringUtils.isBlank(proxyReplicaSetName)) {
            Long srcReplicaId = mysqlBizService.findMasterReplica(replicaSetName).getId();
            Long destReplicaId = mysqlBizService.findMasterReplica(destReplicaSetName).getId();
            return replaceEndpointRsForBasic(srcReplicaId, destReplicaId, false);
        } else if ("toProxy".equals(direction)) {
            return switchRsToProxyTmpIns(replicaSetName, proxyReplicaSetName, destReplicaSetName);
        } else if ("toTmpIns".equals(direction)) {
            return switchProxyRsToTmpIns(replicaSetName, proxyReplicaSetName, destReplicaSetName);
        } else {
            throw new NotRetryException("invalid switch rs condition");
        }
    }

    /**
     * 将域名vip rs刷新为代理节点，最后链路为 用户->代理->临时实例
     * 注意：需要提前将代理指向临时实例，可调用update_proxy_ip_port_to_tmp_ins 实现
     * 用于serverless v1 升v2、provision 升serverless 场景
     * @param replicaSetName 主实例
     * @param proxyReplicaSetName 代理实例
     * @param tmpReplicaSetName 临时实例
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "切换链路到代理-临时实例")
    @PostMapping("/switch_rs_to_proxy_tmp_ins")
    public Result<Boolean> switchRsToProxyTmpIns(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                 @RequestParam(name = "proxyReplicaSetName") String proxyReplicaSetName,
                                                 @RequestParam(name = "tmpReplicaSetName") String tmpReplicaSetName) throws Exception {
        // 仅允许实例与其代理交换域名
        String proxyName = maxscaleProviderService.getMaxScaleName(replicaSetName);
        String destProxyName = maxscaleProviderService.getMaxScaleName(tmpReplicaSetName);
        ReplicaSet tmpReplicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), tmpReplicaSetName, false);
        if (!StringUtils.equals(proxyReplicaSetName, proxyName) && !StringUtils.equals(proxyReplicaSetName, destProxyName)) {
            throw new NotRetryException("proxyReplicaSetName is not src's or dest's proxy!");
        } else if (!StringUtils.equals(replicaSetName, tmpReplicaSet.getPrimaryInsName())) {
            throw new NotRetryException("tmpReplicaSetName is not src's tmp ins!");
        }

        Integer delayLimit = Integer.valueOf(workFlowBaseService.getValueFromContext("delayLimit", 5).toString());
        Integer waitSyncTimeout = Integer.valueOf(workFlowBaseService.getValueFromContext("waitSyncTimeout", 30).toString());
        mysqlBizService.switchRsToProxyTmpIns(replicaSetName, proxyReplicaSetName, tmpReplicaSetName, delayLimit, waitSyncTimeout);

        return Result.returnSuccessResult(true);
    }

    /**
     * 将代理域名vip rs刷新为代理节点，最后链路为 用户->临时实例
     * 用于serverless 迁移provision场景
     * @param replicaSetName 主实例
     * @param proxyReplicaSetName 代理实例
     * @param tmpReplicaSetName 临时实例
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "切换代理rs到临时实例")
    @PostMapping("/switch_proxy_rs_to_tmp_ins")
    public Result<Boolean> switchProxyRsToTmpIns(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                 @RequestParam(name = "proxyReplicaSetName") String proxyReplicaSetName,
                                                 @RequestParam(name = "tmpReplicaSetName") String tmpReplicaSetName) throws Exception {
        // 仅允许实例与其代理交换域名
        String proxyName = maxscaleProviderService.getMaxScaleName(replicaSetName);
        ReplicaSet tmpReplicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), tmpReplicaSetName, false);
        if (!StringUtils.equals(proxyReplicaSetName, proxyName)) {
            throw new NotRetryException("proxyReplicaSetName is not src's proxy!");
        }
        if (!StringUtils.equals(replicaSetName, tmpReplicaSet.getPrimaryInsName())) {
            throw new NotRetryException("tmpReplicaSetName is not src's tmp ins!");
        }

        Integer delayLimit = Integer.valueOf(workFlowBaseService.getValueFromContext("delayLimit", 5).toString());
        Integer waitSyncTimeout = Integer.valueOf(workFlowBaseService.getValueFromContext("waitSyncTimeout", 30).toString());
        mysqlBizService.switchProxyRsToTmpIns(replicaSetName, proxyReplicaSetName, tmpReplicaSetName, delayLimit, waitSyncTimeout);

        return Result.returnSuccessResult(true);
    }


    @ApiOperation(value = "确保当前节点不是Master(ALISQL内核使用)")
    @PostMapping("/ensure_replica_is_not_master")
    public Result<String> checkCurrentIsNotMaster(@RequestParam(name = "replicaId") Long replicaId) throws Exception {
        ReplicaResource replicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), replicaId, false);
        MysqlService mysqlService = mysqlServiceFactory.get(replicaResource);
        mysqlService.checkCurrentIsNotMaster(replicaId);
        return Result.returnSuccessResult(RequestSession.getRequestId());
    }


    @ApiOperation(value = "Pengine迁移新架构标准化数据文件目录")
    @PostMapping("/migrate_k8s_format_data_dir")
    public Result<String> migrateK8SFormatDir(@RequestParam(name = "replicaId") Long replicaId,
                                              @RequestParam(name = "srcReplicaSetName", required = false) String srcReplicaSetName,
                                              @RequestParam(name = "backupSetId", required = false) Long backupSetId,
                                              @RequestParam(name = "backupKindCode", required = false) Integer backupKindCode) throws Exception {
        mysqlMigrateBizService.migrateDataDir(replicaId, srcReplicaSetName, backupSetId, backupKindCode);
        return Result.returnSuccessResult("success");
    }

    @ApiOperation(value = "本地盘迁移新架构标准化数据文件目录")
    @PostMapping("/init_k8s_format_data_dir")
    public Result<String> initK8SFormatDir(
            @RequestParam(name = "replicaId") Long replicaId) throws Exception {
        ReplicaResource replica = dBaasMetaClient
                .getDefaultmetaApi()
                .getReplica(RequestSession.getRequestId(), replicaId, false);

        List<String> cmdList = Lists.newArrayList(
                String.format("mkdir -p %s %s %s", DATA_BASE_DIR, LOG_BASE_DIR, DATA_DIR),
                String.format("mkdir -p %s/dbs", DATA_BASE_DIR),
                String.format("mkdir -p %s/mysql", DATA_BASE_DIR),
                String.format("mkdir -p %s/tmp", DATA_BASE_DIR),
                String.format("mkdir -p %s/backup", DATA_BASE_DIR),
                String.format("mkdir -p %s/mysql", LOG_BASE_DIR),
                String.format("mkdir -p %s/tmp", LOG_BASE_DIR),
                String.format("if [ ! -f %s/auto.cnf ]; then touch %s/auto.cnf; fi", INS_DATA_PATH, INS_DATA_PATH),
                String.format("chown -R mysql: %s %s %s", DATA_BASE_DIR, LOG_BASE_DIR, DATA_DIR));
        String cmd = String.join(";", cmdList);
        Map<String, String> stringStringMap = actionCmdManager.runCmdInReplicaWithAction(replica.getReplicaSetName(), replicaId, cmd);
        log.info("init_k8s_format_data_dir:cmd {} : stringStringMap: {}", cmd, stringStringMap);
        return Result.returnSuccessResult(RequestSession.getRequestId());
    }


    @ApiOperation(value = "Pengine迁移新架构标准化日志文件目录")
    @PostMapping("/migrate_k8s_format_log_dir")
    public Result<String> migrateK8SFormatLogDir(@RequestParam(name = "replicaId") Long replicaId) throws Exception {
        mysqlMigrateBizService.migrateLogDir(replicaId);
        return Result.returnSuccessResult("success");
    }

    @ApiOperation(value = "Pengine迁移新架构搭建复制同步关系")
    @PostMapping("/migrate_to_k8s_build_replication")
    public Result<String> migrateToK8SBuildReplication(
            @RequestParam(name = "srcReplicaSetName") String srcReplicaSetName,
            @RequestParam(name = "replicaSetName") String replicaSetName,
            @RequestParam(name = "replicaId", required = false) Long replicaId
    ) throws Exception {
        mysqlMigrateBizService.migrateToK8SBuildReplication(srcReplicaSetName, replicaSetName, replicaId);
        // 搭建复制后暂停10秒，避免second behind master这些值没有更新
        Thread.sleep(10 * 1000);
        return Result.returnSuccessResult(RequestSession.getRequestId());
    }

    @ApiOperation(value = "存储实例当前的Event信息")
    @PostMapping("/store_mysql_event")
    public Result<String> storeMysqlEvent(@RequestParam(name = "replicaSetName") String replicaSetName) throws Exception {
        ReplicaSet replicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), replicaSetName, false);
        Replica masterReplica = mysqlBizService.findMasterReplica(replicaSetName);
        Long masterReplicaId = masterReplica.getId();

        String getEventSql = SQLTemplateSet.getSQLTemplateCommand(CommonConsts.DB_TYPE_MYSQL,
                replicaSet.getServiceVersion(), SQLTemplateSet.SQLCommand.GET_EVENT_CMD);
        SQLResult sqlResult = sqlBaseExecutor.execQueryInReplica(masterReplicaId, new MySQLCommand(getEventSql));
        if (!sqlResult.isSuccess()) {
            throw new NotRetryException("get event failed: " + sqlResult.getRawResult());
        }
        List<Map<String, Object>> events = sqlResult.getResultMap();
        if (CollectionUtils.isEmpty(events)) {
            log.info("events is empty, skip it");
            return Result.returnSuccessResult(RequestSession.getRequestId());
        }
        log.info(events.toString());
        // 去掉存储event，直接set global event_scheduler=ON，能够让event调度恢复
//        workFlowBaseService.updateWorkflowContext(ImmutableMap.of(ContextKeyConsts.STORE_EVENT_INFO, sqlResult.getResultMap()));
        return Result.returnSuccessResult(RequestSession.getRequestId());
    }

    @ApiOperation(value = "恢复实例Event配置")
    @PostMapping("/enable_mysql_event")
    public Result<String> enableMysqlEvent(@RequestParam(name = "replicaSetName") String replicaSetName) throws Exception {
        ReplicaSet replicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), replicaSetName, false);
        Replica masterReplica = mysqlBizService.findMasterReplica(replicaSetName);
        Long masterReplicaId = masterReplica.getId();
        String alertEventSql = SQLTemplateSet.getSQLTemplateCommand(CommonConsts.DB_TYPE_MYSQL, SQLTemplateSet.SQLCommand.ALERT_EVENT_CMD);
        List<MySQLCommand> commandList = Lists.newArrayList();
        WorkFlowContext workFlowContext = workFlowBaseService.getWorkflowContext();
        if (workFlowContext != null && workFlowContext.containsKey(ContextKeyConsts.STORE_EVENT_INFO)) {
            List<Map<String, String>> storeEventInfo = (List<Map<String, String>>) workFlowContext.get(ContextKeyConsts.STORE_EVENT_INFO);
            for (Map<String, String> event : storeEventInfo) {
                String eventSchema = event.get("event_schema");
                String eventName = event.get("event_name");
                String status = event.get("status").toLowerCase().startsWith("enable") ? "enable" : "disable";
                String definer = event.get("definer").split("@")[0];
                commandList.add(new MySQLCommand(alertEventSql, eventSchema, definer, eventName, status));
            }
        }
        if (!commandList.isEmpty()) {
            List<SQLResult> sqlResults = sqlBaseExecutor.execSQLInReplica(null, masterReplicaId, commandList);
            if (!sqlResults.get(0).isSuccess()) {
                throw new NotRetryException("alert event failed!");
            }

        }
        return Result.returnSuccessResult(RequestSession.getRequestId());
    }

    @ApiOperation(value = "升级内核小版本")
    @PostMapping("/upgrade_instance")
    public Result<String> upgradeInstance(@RequestParam(name = "replicaId") Long replicaId) throws Exception {
        ReplicaResource replica = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), replicaId, false);
        ReplicaSet replicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), replica.getReplica().getReplicaSetName(), false);
        MysqlService mysqlService = mysqlServiceFactory.get(replicaSet);
        String jobId = mysqlService.upgradeMinorVersion(replicaSet, replica.getReplica());
        if (StringUtils.isEmpty(jobId)) {
            return Result.returnFailureResult(ErrorCode.SERVICE_RETURN_NO_NEED, "skip it.");
        }
        return Result.returnSuccessResult(jobId);
    }


    /**
     * 当前实例为主实例时，对所有只读实例下发升级小版本的任务
     *
     * @param replicaSetName replicaSetName of PRIMARY ins
     * @return
     * @throws Exception
     * <AUTHOR>
     */
    @ApiOperation(value = "主实例下发同步升级的任务")
    @PostMapping("/upgrade_readins")
    public Result<Object> buildSyncUpgradeReadInsPlan(
            @RequestParam(name = "replicaSetName") String replicaSetName,
            @RequestParam(name = "releaseDate") String releaseDate,
            @RequestParam(name = "targetMinorVersion") String targetMinorVersion,
            @RequestParam(name = "switchTimeMode") String switchTimeMode,
            @RequestParam(name = "switchTime", required = false) String switchTime,
            @RequestParam(name = "readInsTargetMinorVersion", required = false) String readInsTargetMinorVersion
    ) throws Exception {
        val metaApi = dBaasMetaClient.getDefaultmetaApi();
        val replicaSet = metaApi.getReplicaSet(RequestSession.getRequestId(), replicaSetName, null);
        val isReadIns = replicaMetaHelper.isReadIns(replicaSet);

        if (isReadIns) {
            log.info("Type of current custins {} is readonly, skip.", replicaSetName);
            return Result.returnSuccessResult(null);
        }

        String replicaSetArch = LangUtil.getString(
                metaApi.getReplicaSetLabel(RequestSession.getRequestId(), replicaSet.getName(), CommonConsts.CPU_ARCH),
                ReplicaSetResourceRequest.ArchEnum.X86.getValue()); // 获取主实例的CPU架构，默认x86

        List<ReplicaSet> readOnlyReplicaSetList = ObjectUtils.firstNonNull(metaApi.listReplicaSetSubIns(RequestSession.getRequestId(), replicaSetName, ReplicaSet.InsTypeEnum.READONLY.toString()).getItems(), new ArrayList<>());
        WorkFlowContext workflowContext = ObjectUtils.firstNonNull(workFlowBaseService.getWorkflowContext(), new WorkFlowContext());
        Map<String, String> taskIdMap;
        String READ_INS_UPDATE_TASKS = "readInsUpdateTasks";
        if (!workflowContext.containsKey(READ_INS_UPDATE_TASKS)) {
            taskIdMap = new HashMap<>(readOnlyReplicaSetList.size());
            workflowContext.put(READ_INS_UPDATE_TASKS, taskIdMap);
        } else {
            taskIdMap = (Map<String, String>) workflowContext.get(READ_INS_UPDATE_TASKS);
        }

        List<String> failedList = new ArrayList<>();
        for (val readonlyReplicaSet : readOnlyReplicaSetList) {
            if (taskIdMap.containsKey(readonlyReplicaSet.getName())) {
                log.info("{} has already resolved, skip.", readonlyReplicaSet.getName());
                continue;
            }

            Map<String, String> labels = dbaasMetaClient.getDefaultmetaApi()
                    .listReplicaSetLabels(RequestSession.getRequestId(), readonlyReplicaSet.getName());

            // 分析型只读实例内核版本与主实例解藕
            String isAnalyticReadOnlyInsValue = labels.get("isAnalyticReadOnlyIns");
            boolean isAnalyticReadOnlyIns = StringUtils.isNotBlank(isAnalyticReadOnlyInsValue) &&
                ("true".equalsIgnoreCase(isAnalyticReadOnlyInsValue));

            val currentReleaseDate = StringUtils.substringAfterLast(
                    labels.get("minor_version"),
                    StringUtils.contains(labels.get("minor_version"), "_") ? "_" : ":"
            );
            String currentPrefix = StringUtils.substringBeforeLast(
                    labels.get("minor_version"),
                    StringUtils.contains(labels.get("minor_version"), "_") ? "_" : ":"
            );
            String targetPrefix = StringUtils.substringBeforeLast(targetMinorVersion, "_");
            log.info("currentPrefix:{},targetPrefix:{}", currentPrefix, targetPrefix);
            if (!isAnalyticReadOnlyIns && currentReleaseDate.compareTo(releaseDate) >= 0 && StringUtils.equalsIgnoreCase(currentPrefix, targetPrefix)) {
                log.info("[{}] Current version: {}, request version: {}. Skip it", readonlyReplicaSet.getName(), currentReleaseDate, releaseDate);
                continue;
            }

            // 分析型实例的内核版本只要落后于主实例目标版本 就升级为最新内核小版本
            if (isAnalyticReadOnlyIns) {
                if (currentReleaseDate.compareTo(releaseDate) >= 0 || readInsTargetMinorVersion == null) {
                    log.info("[{}] Current version: {},  readInsTargetMinorVersion :{} for analytic read only instance. Skip it", readonlyReplicaSet.getName(), currentReleaseDate, readInsTargetMinorVersion);
                    continue;
                }

                String readInsReleaseData = readInsTargetMinorVersion.substring(readInsTargetMinorVersion.lastIndexOf("_") + 1);
                if (currentReleaseDate.compareTo(readInsReleaseData) >= 0) {
                    log.info("[{}] Current version: {}, request minor version: {}. Skip it", readonlyReplicaSet.getName(), currentReleaseDate, readInsTargetMinorVersion);
                    continue;
                }
            }

            Map<String, Object> request = new HashMap<>();

            if (StringUtils.isNotEmpty(switchTimeMode)) {
                request.put("SwitchTimeMode", switchTimeMode);
            }
            if (StringUtils.isNotEmpty(switchTime)) {
                request.put("SwitchTime", switchTime);
            }

            if (StringUtils.isNotEmpty(targetMinorVersion)) {
                request.put("TargetMinorVersion", targetMinorVersion);
            }
            if (isAnalyticReadOnlyIns) {
                request.put("TargetMinorVersion", readInsTargetMinorVersion);
            }

            request.put("primaryReplicaSetName", replicaSetName);

            // RDSAPI in online envs is replay-proofed
            // Using the same requestId as the current session will fail
            request.put("RequestId", UUID.randomUUID());

            Map<String, Object> response = null;
            try {
                response = rdsAPIBaseService.invoke(
                        readonlyReplicaSet.getName(),
                        "UpgradeDBVersion",
                        request,
                        Map.class
                );
            } catch (BaseServiceException e) {
                String readReplicaSetArch = labels.getOrDefault(CommonConsts.CPU_ARCH, ReplicaSetResourceRequest.ArchEnum.X86.getValue());
                Set<ReplicaSet.StatusEnum> versionTransing = Sets.newHashSet(ReplicaSet.StatusEnum.MINOR_VERSION_TRANSING, ReplicaSet.StatusEnum.VERSION_TRANSING);
                if (!StringUtils.equalsIgnoreCase(readReplicaSetArch, replicaSetArch) && e.getCode().contains("InvalidMinorVerison.NotFound")) {
                    // 主实例和只读实例的CPU架构不一致情况，内核版本可能会有差异，报错找不到，这里直接跳过只读实例升级
                    log.error("{}[{}] cpu arch is different from primary ins, target minor verion not found", readonlyReplicaSet.getName(), readReplicaSetArch);
                    throw e;
                } else if (versionTransing.contains(readonlyReplicaSet.getStatus()) && e.getCode().contains("IncorrectDBInstanceState")) {
                    // 只读实例升级中，这里可以忽略跳过
                    log.warn("[{}] readonly status [{}], ignore it", readonlyReplicaSet.getName(), readonlyReplicaSet.getStatus());
                    continue;
                }
                failedList.add(readonlyReplicaSet.getName());
                continue;
            }
            String taskId = LangUtil.getString(response.get("TaskId"));

            if (taskId == null) {
                throw new ActivityException("Empty taskId");
            } else {
                taskIdMap.put(readonlyReplicaSet.getName(), taskId);
                workFlowBaseService.updateWorkflowContext(workflowContext);
            }
        }

        if (!failedList.isEmpty()) {
            log.error("[{}] has failed, retry it", StringUtils.join(failedList, ","));
            throw new InProcessingException(ErrorInfo.SERVICE_EXCEPTION_OCCUR, RequestSession.getRequestId());
        }

        val taskIds = StringUtils.join(taskIdMap.values().toArray(), ",");
        log.info("taskIds: [{}]", taskIds);
        return Result.returnSuccessResult(taskIds);
    }


    @RetryAnnotation(timeout = 300, retry = 3, interval = 30)
    @ApiOperation(value = "关闭节点前的检查")
    @PostMapping("/pre_stop_check")
    public Result<Boolean> preStopCheck(@RequestParam(name = "replicaId") Long replicaId) throws Exception {
        ReplicaResource replicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), replicaId, false);
        MysqlService mysqlService = mysqlServiceFactory.get(replicaResource);
        boolean isSuccess = mysqlService.preStopCheck(replicaResource);
        if (!isSuccess) {
            throw new ActivityException("Pre stop check failed.");
        }
        return Result.returnSuccessResult(true);
    }

    @ApiOperation(value = "检查实例是否可以释放节点")
    @PostMapping("/check_cluster_can_release_node")
    public Result<Boolean> checkReplicaRole(@RequestParam(name = "replicaSetName") String replicaSetName,
                                            @RequestParam(name = "replicaIds") String replicaIds) throws Exception {
        //slave节点数量大于等2才允许释放一个节点, 校验释放后节点数不小于2
        val replicaIdList = Arrays.asList(replicaIds.split(","));
        List<Replica> replicas  = replicaMetaHelper.getReplicas(replicaSetName, Replica.RoleEnum.SLAVE.toString());
        if (replicas.size() <= 1 || (replicas.size() - replicaIdList.size()) < 1){
            throw new Exception("replicaSet slave node is less than 2, not permit release");
        }

        Map<String, String> labels = dBaasMetaClient.getDefaultmetaApi().listReplicaSetLabels(RequestSession.getRequestId(), replicaSetName);
        boolean isMgr = ReplicaSetMetaHelper.isMgr(labels);
        // mgr节点需要大于等于3才允许释放一个节点， 校验释放手节点数不小于3
        if (isMgr && (replicas.size() <= 2 || (replicas.size() - replicaIdList.size()) < 2)){
            throw new Exception("replicaSet is mgr, slave node is less than 3, not permit release");
        }

        //校验replicaId
        for (String replicaId: replicaIdList){
            ReplicaResource replicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), Long.parseLong(replicaId), false);
            if (replicaResource == null){
                throw new Exception("replica is not found, please check. replicaId: " + replicaId );
            }
            Replica srcReplica = replicaResource.getReplica();
            if (!StringUtils.equalsIgnoreCase(replicaSetName, replicaResource.getReplicaSetName())){
                throw new Exception("replica is replicaSet is not "+ replicaSetName);
            }
            if (StringUtils.equalsIgnoreCase(srcReplica.getRole().toString(), Replica.RoleEnum.MASTER.toString())){
                throw new Exception("replica is master node, not permit remove");
            }
        }

        return Result.returnSuccessResult(true);
    }


    @ApiOperation(value = "检查node是否在释放列表中")
    @PostMapping("/check_node_in_remove_list")
    public Result<Boolean> checkReplicaInRemoveList(@RequestParam(name = "removeReplicaIds") String removeReplicaIds,
                                                    @RequestParam(name = "replicaId") String replicaId) throws Exception {
        val replicaIdList = Arrays.asList(removeReplicaIds.split(","));
        if (!replicaIdList.contains(replicaId)){
            throw new Exception("replica not in remove list, replica: " + replicaId);
        }
        return Result.returnSuccessResult(true);
    }

    @RetryAnnotation(timeout = 300, retry = 3, interval = 30)
    @ApiOperation(value = "关闭节点前的操作，将备节点在集群只读连接里面的权重配置为0")
    @PostMapping("/pre_stop")
    public Result<Boolean> preStop(@RequestParam(name = "replicaId") Long replicaId) throws Exception {
        ReplicaResource replicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), replicaId, false);
        MysqlService mysqlService = mysqlServiceFactory.get(replicaResource);
        boolean isSuccess = mysqlService.preStop(replicaResource);
        if (!isSuccess) {
            throw new ActivityException("Pre stop failed.");
        }
        return Result.returnSuccessResult(true);
    }

    @RetryAnnotation(timeout = 300, retry = 60, interval = 5)
    @ApiOperation(value = "启动节点后的检查，检查节点的复制健康情况")
    @PostMapping("/post_start_check")
    public Result<Boolean> postStartCheck(@RequestParam(name = "replicaId") Long replicaId) throws Exception {
        ReplicaResource replicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), replicaId, false);
        MysqlService mysqlService = mysqlServiceFactory.get(replicaResource);
        boolean isSuccess = mysqlService.postStartCheck(replicaResource);
        if (!isSuccess) {
            log.error("Post start check failed.");
            throw new InProcessingException(ErrorInfo.ACTIVITY_IN_PROCESSING, RequestSession.getRequestId());
        }
        return Result.returnSuccessResult(true);
    }


    @RetryAnnotation(timeout = 300, retry = 60, interval = 5)
    @ApiOperation(value = "启动节点后的操作，恢复备节点在集群只读连接里面的权重")
    @PostMapping("/post_start")
    public Result<Boolean> postStart(@RequestParam(name = "replicaId") Long replicaId) throws Exception {
        ReplicaResource replicaResource = dBaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), replicaId, false);
        MysqlService mysqlService = mysqlServiceFactory.get(replicaResource);
        boolean isSuccess = mysqlService.postStart(replicaResource);
        if (!isSuccess) {
            throw new ActivityException("Post start failed.");
        }
        return Result.returnSuccessResult(true);
    }

    @RetryAnnotation(timeout = 300, retry = 60, interval = 5)
    @ApiOperation(value = "根据Endpoint Config重新刷新只读Endpoint")
    @PostMapping("/refresh_ro_endpoint")
    public Result<Boolean> refreshRoRs(@RequestParam(name = "replicaSetName") String replicaSetName) throws Exception {
        ReplicaSet replicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), replicaSetName, false);
        MysqlService mysqlService = mysqlServiceFactory.get(replicaSet);
        boolean isSuccess = mysqlService.replaceReplicaRs(replicaSetName);
        if (!isSuccess) {
            throw new ActivityException("refresh rs failed.");
        }
        return Result.returnSuccessResult(true);
    }

    @RetryAnnotation(timeout = 300, retry = 60, interval = 5)
    @ApiOperation(value = "只读Endpoint移除node")
    @PostMapping("/remove_node_from_ro_endpoint")
    public Result<Boolean> removeNodeFromRoRs(@RequestParam(name = "replicaSetName") String replicaSetName,
                                              @RequestParam(name = "replicaId") Long replicaId) throws Exception {
        ReplicaSet replicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), replicaSetName, false);
        MysqlService mysqlService = mysqlServiceFactory.get(replicaSet);
        boolean isSuccess = mysqlService.removeNodeFromEndpointRs(replicaSetName, replicaId);
        if (!isSuccess) {
            throw new ActivityException("refresh rs failed.");
        }
        return Result.returnSuccessResult(true);
    }

    @ApiOperation(value = "拉起工具镜像，执行ibd文件校验")
    @PostMapping("/start_check_ibd_files")
    public Result<String> startCheckIbdFiles(@RequestParam(name = "replicaSetName") String replicaSetName,
                                              @RequestParam(name = "replicaId") Long replicaId,
                                              @RequestParam(name = "printLogs", defaultValue = "0") Integer printLogs) throws Exception {
        String requestId = RequestSession.getRequestId();
        Replica replica = dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(requestId, replicaSetName, null, null, null, null).getItems()
                .stream().filter(rp -> replicaId.equals(rp.getId())).collect(Collectors.toList()).get(0);

        Map<String, String> envs = new HashMap<>();
        envs.put("cpu_cores", replica.getCpuCores() + "");
        envs.put("print_logs", printLogs + "");
        List<String> opsJobs = actionJobManager.runAsync(
                ActionJobParam.builder()
                        .resource(ConfigFileAction.Checksum.RESOURCE)
                        .action(ConfigFileAction.Checksum.ACTION)
                        .replicaSetName(replicaSetName)
                        .replicaIdList(ImmutableList.of(replica.getId()))
                        .params(envs)
                        .build()
        );
        return Result.returnSuccessResult(StringUtils.join(opsJobs, ","));
    }

    @ApiOperation(value = "等待工具镜像执行完毕，获取错误信息")
    @PostMapping("/wait_check_ibd_finished")
    public Result<String> waitCheckIbdFinished(@RequestParam(name = "workflowId") String workflowId,
                                               @RequestParam(name = "replicaSetName") String replicaSetName,
                                               @RequestParam(name = "jobNames") String jobNames,
                                               @RequestParam(name = "regionId", required = false) String regionId) throws Exception {
        var replicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), replicaSetName, false);
        var labels = replicaSet.getLabels();
        if (labels == null) {
            labels = new HashMap<>();
        }
        String[] jobList = jobNames.split(",");
        ActionJobStatus actionJobStatus = null;
        for (String name : jobList) {
            try {
                actionJobStatus = actionJobManager.getAsyncJobStatus(regionId, name);
                actionJobManager.checkActionJobStatus(actionJobStatus, regionId, name);
            } catch (InProcessingException e) {
                throw e;
            } catch (Exception e) {
                if (actionJobStatus != null && actionJobStatus.getDetailLog() != null && actionJobStatus.getDetailLog().contains("ibd is invalid:")) {
                    // update replicaSet label
                    labels.put("check_ibd_result", "invalid: " + workflowId + " - " + new Date().getTime());
                    continue;  // 跳过 for 循环
                }
                if (e instanceof NotRetryException) {
                    throw e;
                }
                log.error(e.getMessage(), e);
                throw new NotRetryException(e.getMessage());
            }
            labels.put("check_ibd_result", "success: " + workflowId + " - " + new Date().getTime());
        }
        dbaasMetaClient.getDefaultmetaApi().updateReplicaSetLabels(RequestSession.getRequestId(), replicaSetName, labels);

        return Result.returnSuccessResult(RequestSession.getRequestId());
    }


    @ApiOperation(value = "等待只读实例完成变配")
    @PostMapping("/wait_read_ins_finish_change")
    public Result<Boolean> waitReadInsFinishChange(@RequestParam(name = "replicaSetName") String replicaSetName) throws Exception {
        ReplicaSet replicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), replicaSetName, null);
        if (replicaSetMetaHelper.isReadIns(replicaSet)) {
            log.info("current's replicaSet is readins, skip it");
            return Result.returnSuccessResult(true);
        }
        //获得对应只读实例
        ReplicaSetListResult readOnlyReplicaSetListResult = dBaasMetaClient.getDefaultmetaApi().listReplicaSetSubIns(RequestSession.getRequestId(), replicaSetName, ReplicaSet.InsTypeEnum.READONLY.toString());
        if (readOnlyReplicaSetListResult == null || CollectionUtils.isEmpty(readOnlyReplicaSetListResult.getItems())) {
            log.info("Read replicaSets not found, skip it");
            return Result.returnSuccessResult(true);
        }
        for (ReplicaSet readOnlyReplicaSet : readOnlyReplicaSetListResult.getItems()) {
            if (readOnlyReplicaSet.getStatus() == ReplicaSet.StatusEnum.CLASS_CHANGING) {
                //实例是变配状态，进入等待队列中
                List<Task> taskList = null;
                try {
                    taskList = workFlowBaseService.findTaskByStatus(readOnlyReplicaSet.getName(), null, Collections.singletonList(CommonConsts.WORKFLOW_STATUS_PAUSE));
                } catch (BaseServiceException e) {
                    log.warn("find task failed, {}", e.getMessage());
                    //just ignore
                }
                if (CollectionUtils.isNotEmpty(taskList)) {
                    //再查看实例的当前任务，如果是pause状态，可以忽略
                    log.info("pause task exists, skip it");
                    continue;
                }
                log.info("[{}] is class changing, waiting...", readOnlyReplicaSet.getName());
                throw new InProcessingException(ErrorInfo.REPLICASET_MODIFYING_IN_PROCESSING, RequestSession.getRequestId());
            }
        }
        return Result.returnSuccessResult(true);
    }


    @ApiOperation(value = "若是由于临时空间使用过多造成锁定，设置lock reason")
    @PostMapping("/set_lock_by_large_tmp_size")
    public Result<String> setLockByLargeTmpSize(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                @RequestParam(name = "retryCounter", defaultValue = "0") Long retryCounter,
                                                @RequestParam(name = "maxRetryTimes", defaultValue = "1") Long maxRetryTimes) throws Exception {
        ReplicaSet replicaSet = dBaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), replicaSetName, null);
        if (!ReplicaSet.LockModeEnum.DISKFULL.equals(replicaSet.getLockMode()) &&
                !ReplicaSet.LockModeEnum.READINS_DISKFULL.equals(replicaSet.getLockMode())) {
            log.info("lock mode not DISKFULL/READINS_DISKFULL, skip.");
            return Result.returnSuccessResult(RequestSession.getRequestId());
        }
        Long replicaId = Objects.requireNonNull(dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(RequestSession.getRequestId(), replicaSetName, null, null, null, null).getItems()).stream().filter(r -> Objects.equals(r.getRole(), Replica.RoleEnum.MASTER)).map(Replica::getId).findFirst().orElse(null);
        String cmd = "du -csk /home/<USER>/data/dbs/#innodb_temp /home/<USER>/data/mysql/ibtmp1 /home/<USER>/log/tmp/*  2>/dev/nul | tail -1 | awk '{print $1;}'";

        Integer tmpFileTotalSize = 0;
        try {
            Map<String, String> output = actionCmdManager.runCmdInReplicaWithAction(replicaSetName, replicaId, cmd);
            String outputString = output.get(String.valueOf(replicaId));
            try {
                String[] split = outputString.split("\n");
                tmpFileTotalSize = Integer.valueOf(split[split.length - 1].trim());
            } catch (NumberFormatException e) {
                throw new NotRetryException(String.format("failed to get tmpFileTotalSize for replica %s: %s", replicaId, outputString));
            }
        } catch (Exception e) {
            if (maxRetryTimes - 1 > retryCounter) {
                throw new InProcessingException(ErrorInfo.ACTIVITY_IN_PROCESSING, e.getMessage());
            }
            log.warn("get tmp file size failed retried {}/{} times, skip", retryCounter, maxRetryTimes);
        }
        if (tmpFileTotalSize > 2 * 1024 * 1024) {
            replicaSet.setLockReason(LOCK_REASON_DISK_FULL_CAUSED_BY_BIG_TMP_TABLE);
            dBaasMetaClient.getDefaultmetaApi().updateReplicaSet(RequestSession.getRequestId(), replicaSetName, replicaSet);
            log.info("update replicaSet's lock reason success");
        }

        return Result.returnSuccessResult(RequestSession.getRequestId());
    }

    /**
     *
     * @param replicaSetName
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "刷新通用云盘预配置IOPS")
    @PostMapping("/refresh_general_essd_conf")
    public Result<String> refreshGeneralEssdConf(@RequestParam(name = "replicaSetName") String replicaSetName) throws Exception {
        // preCheck storageType
        Replica.StorageTypeEnum storageType = replicaMetaHelper.getReplicaSetStorageType(replicaSetName, RequestSession.getRequestId());
        if(Replica.StorageTypeEnum.CLOUD_AUTO != storageType){
            log.info("storageType is [{}] skip RefreshGeneralEssdConf.", storageType);
            return Result.returnSuccessResult(null);
        }
        // get workflow context
        WorkFlowContext workflowContext = ObjectUtils.firstNonNull(workFlowBaseService.getWorkflowContext(), new WorkFlowContext());
        Map<String, String> taskIdMap;
        String REFRESH_GENERAL_ESSD_CONF_TASK = "refreshGeneralEssdConf";
        if (!workflowContext.containsKey(REFRESH_GENERAL_ESSD_CONF_TASK)) {
            taskIdMap = new HashMap<>();
            workflowContext.put(REFRESH_GENERAL_ESSD_CONF_TASK, taskIdMap);
        } else {
            taskIdMap = (Map<String, String>) workflowContext.get(REFRESH_GENERAL_ESSD_CONF_TASK);
        }
        if (taskIdMap.containsKey(replicaSetName)) {
            log.info("{} has already resolved, skip.", replicaSetName);
            return Result.returnSuccessResult(taskIdMap.get(replicaSetName));
        }

        // call rdsapi for RefreshGeneralEssdConf
        Map<String, Object> request = new HashMap<>();
        request.put("DBInstanceName", replicaSetName);
        // RDSAPI in online envs is replay-proofed
        // Using the same requestId as the current session will fail
        request.put("RequestId", UUID.randomUUID());
        request.put("IsSkipCheckStatus", true);
        Map<String, Object> response = null;
        try {
            response = rdsAPIBaseService.invoke(
                    replicaSetName,
                    "RefreshGeneralEssdConf",
                    request,
                    Map.class
            );
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        String taskId = LangUtil.getString(response.get("taskId"));
        if (StringUtils.isBlank(taskId)) {
            throw new ActivityException("Empty taskId");
        }
        taskIdMap.put(replicaSetName, taskId);
        workFlowBaseService.updateWorkflowContext(workflowContext);
        return Result.returnSuccessResult(taskId);
    }
    @ApiOperation(value = "批量下发临时实例释放任务")
    @PostMapping("/drop_tmp_ins_for_batch_ins")
    public Result<String> dropTmpInsForBatchIns(@RequestParam(name = "replicaSetName") String replicaSetName) throws Exception {
        List<ReplicaSet> readOnlyReplicaSets = ObjectUtils.firstNonNull(dbaasMetaClient.getDefaultmetaApi().listReplicaSetSubIns(RequestSession.getRequestId(), replicaSetName, ReplicaSet.InsTypeEnum.READONLY.toString()).getItems(), new ArrayList<>());
        if(readOnlyReplicaSets.isEmpty()){
            return Result.returnSuccessResult(null);
        }
        for(ReplicaSet readOnlyReplicaSet:readOnlyReplicaSets){
            List<ReplicaSet> tmpReplicaSets =  ObjectUtils.firstNonNull(dbaasMetaClient.getDefaultmetaApi().listReplicaSetSubIns(RequestSession.getRequestId(), readOnlyReplicaSet.getName(), ReplicaSet.InsTypeEnum.TMP.toString()).getItems(), new ArrayList<>());
            List<String> tmpReplicaSetNameList = tmpReplicaSets.stream().map(ReplicaSet::getName).collect(Collectors.toList());
            startDropTmpIns(tmpReplicaSetNameList, null, null);

        }
        return Result.returnSuccessResult(null);
    }


    @ApiOperation(value = "根据用户杀MySQL进程")
    @PostMapping("/kill_processes_by_user")
    public Result<String> killProcessesByUser(@RequestParam(name = "replicaSetName", required = false) String replicaSetName,
                                              @RequestParam(name = "replicaId", required = false) Long replicaId,
                                              @RequestParam(name = "users") String users) throws Exception {
        List<String> userList = Arrays.stream(users.split("\\s,\\s")).collect(Collectors.toList());
        log.info("parsed userList: {}", userList);
        if (userList.isEmpty()) {
            throw new NotRetryException("user not provided");
        }
        if (Objects.isNull(replicaId)) {
            if (StringUtils.isBlank(replicaSetName)) {
                throw new NotRetryException("Both replicaSetName and replicaId are empty");
            }
            List<Replica> replicas = dBaasMetaClient.getDefaultmetaApi().listReplicasInReplicaSet(RequestSession.getRequestId(), replicaSetName, null, null, null, null).getItems();
            Optional<Replica> masterReplica = replicas.stream().filter(r -> r.getRole() == Replica.RoleEnum.MASTER).findFirst();
            if (masterReplica.isPresent()) {
                replicaId = masterReplica.get().getId();
            } else {
                throw new NotRetryException("replica not provided, and cannot find master role");
            }
        }
        mysqlBizService.killProcessesByUser(replicaId, userList);

        return Result.returnSuccessResult(RequestSession.getRequestId());
    }

    @ApiOperation(value = "查看实例是否为rund")
    @PostMapping("/check_replicaset_rund")
    public Result<String> checkReplicaSetRund(@RequestParam(name = "replicaSetName") String replicaSetName) throws Exception {
        if (!replicaMetaHelper.isReplicaSetRund(replicaSetName)) {
            throw new NotRetryException("replicaSet is not rund yet");
        }
        return Result.returnSuccessResult(RequestSession.getRequestId());
    }

    @ApiOperation(value = "根据业务场景做架构迁移")
    @PostMapping("/migrate_for_biz")
    public Result<Object> migrateForBiz(@RequestParam(name = "replicaSetName") String replicaSetName,
                                        @RequestParam(name = "bizScene") String bizScene) throws Exception {

        try {
            // 判断是否已有迁移任务
            List<Task> tasks = workFlowBaseService.findTaskByStatus(replicaSetName, "migrate_basic_ins", new ArrayList<>(INPROCESSING_WORKFLOW_STATUS));
            if (!tasks.isEmpty()) {
                throw new BizServiceException("There are existed migrate tasks");
            }
            String taskId = mysqlBizService.migrateForBiz(replicaSetName, bizScene);
            Map<String, String> taskIdMap = new HashMap<>();
            taskIdMap.put(replicaSetName, taskId);
            String taskIds = StringUtils.join(taskIdMap.values().toArray(), ",");
            log.info("migrate taskIds: [{}]", taskIds);
            return Result.returnSuccessResult(taskIds);
        } catch (BizServiceException be) {
            if ("NO_NEED_MIGRATE".equals(be.getMessage())) {
                log.info("No need to migrate, skip migrate task");
                return Result.returnSuccessResult(null);
            }
            throw new ActivityException(be);
        } catch (Exception e) {
            throw new ActivityException(e);
        }
    }
}
