package com.aliyun.app.activityprovider.activity.common;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.aliyun.app.activityprovider.base.param.service.ParamBaseService;
import com.aliyun.app.activityprovider.base.sqlexec.*;
import com.aliyun.app.activityprovider.base.ssl.SSLMetaHelper;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.app.activityprovider.base.actionmanager.ActionCmdManager;
import com.aliyun.app.activityprovider.base.actionmanager.ActionJobManager;
import com.aliyun.app.activityprovider.base.actionmanager.ActionJobParam;
import com.aliyun.app.activityprovider.base.actionmanager.actions.CLSAction;
import com.aliyun.app.activityprovider.base.actionmanager.actions.TdeAction;
import com.aliyun.app.activityprovider.base.backup.BackupBaseService;
import com.aliyun.app.activityprovider.base.backup.response.GetBackupSetResponse.SlaveStatus;
import com.aliyun.app.activityprovider.base.cls.CLSConsts;
import com.aliyun.app.activityprovider.base.meta.DbaasMetaClient;
import com.aliyun.app.activityprovider.base.param.utils.ParameterHelper;
import com.aliyun.app.activityprovider.base.ssl.CertConfigWithAES;
import com.aliyun.app.activityprovider.base.ssl.SSLBaseService;
import com.aliyun.app.activityprovider.base.ssl.SSLConst;
import com.aliyun.app.activityprovider.base.tde.EncryptionDataKey;
import com.aliyun.app.activityprovider.base.tde.ServiceCnf;
import com.aliyun.app.activityprovider.base.tde.TDEBaseService;
import com.aliyun.app.activityprovider.base.tde.TdeConsts;
import com.aliyun.app.activityprovider.base.workflow.WorkFlowBaseService;
import com.aliyun.app.activityprovider.base.workflow.WorkFlowConsts;
import com.aliyun.app.activityprovider.common.consts.CommonConsts;
import com.aliyun.app.activityprovider.common.consts.MysqlConsts;
import com.aliyun.app.activityprovider.common.exception.ActivityException;
import com.aliyun.app.activityprovider.common.exception.BaseServiceException;
import com.aliyun.app.activityprovider.common.exception.NotRetryException;
import com.aliyun.app.activityprovider.common.utils.LangUtil;
import com.aliyun.app.activityprovider.web.RequestSession;
import com.aliyun.app.activityprovider.web.Result;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyuncs.sts.model.v20150401.AssumeRoleWithServiceIdentityResponse.Credentials;
import com.google.common.collect.ImmutableMap;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import static com.aliyun.app.activityprovider.base.ssl.SSLConst.MYSQL_SSL_REQUIRE_SECURE_TRANSPORT;

/**
 * 安全相关Activity（SSL、TDE）
 *
 * <AUTHOR> on 2021/4/21.
 */
@Api(tags = "安全相关Activity（SSL、TDE）")
@RestController
@RequestMapping("/api/v1/ssl")
@Slf4j
public class SecureActivity {

    @Resource
    private SSLBaseService sslBaseService;
    @Resource
    private DbaasMetaClient dbaasMetaClient;
    @Resource
    private ActionCmdManager actionCmdManager;
    @Resource
    private SQLBaseExecutor sqlBaseExecutor;
    @Autowired
    private ActionJobManager actionJobManager;
    @Autowired
    private TDEBaseService tdeBaseService;
    @Autowired
    private BackupBaseService backupBaseService;
    @Autowired
    private WorkFlowBaseService workFlowBaseService;
    @Autowired
    private ParamBaseService paramBaseService;
    @Resource
    private SSLMetaHelper sslMetaHelper;

    @ApiOperation(value = "申请SSL证书")
    @PostMapping("/create_instance_cert")
    public Result<Map<String, String>> configSSLForReplica(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                           @RequestParam(name = "commonName") String commonName,
                                                           @RequestParam(name = "caType", defaultValue = "aliyun") String caType,
                                                           @RequestParam(name = "certFormat", defaultValue = "pem") String certFormat,
                                                           @RequestParam(name = "validDays", defaultValue = "0") Integer validDays) throws Exception {
        Map<String, String> data = new HashMap<>();
        CertConfigWithAES certConfigWithAES;
        SSLConst.CertFormat format = SSLConst.CertFormat.valueOf(certFormat.toUpperCase());
        if (StringUtils.equalsIgnoreCase(caType, SSLConst.CaType.CUSTOM.toString())) {
            log.info("caType is custom, get cert from metaDB");
            certConfigWithAES = sslBaseService.getInstanceCert(replicaSetName, format);
        } else {
            log.info("caType is aliyun, create cert from caServer");
            if (0 >= validDays) {
                validDays = sslBaseService.getSSLValidDays();
            }
            // 目前每个实例只支持一个证书
            // createInstanceCert 接口幂等，如果已经创建则会更新已有证书
            log.info("===> create/update instance cert, replicaSetName: {}, commonName: {}, format: {}, validDays: {}",
                    replicaSetName, commonName, format, validDays);
            certConfigWithAES = sslBaseService.createInstanceCert(replicaSetName, commonName, format, validDays);
        }
        if (certConfigWithAES == null) {
            throw new NotRetryException("cert is empty, please to check");
        }
        data.put("encryptKey", certConfigWithAES.getKey());
        data.put("encryptCert", certConfigWithAES.getCert());
        data.put("caIntermediateCert", certConfigWithAES.getCaIntermediateCert());
        log.info("===> create success: {}", JSONObject.toJSONString(data));
        return Result.returnSuccessResult(data);
    }

    @ApiOperation(value = "配置节点SSL配置")
    @PostMapping("/config_ssl")
    public Result<String> configSSLForReplica(@RequestParam(name = "replicaSetName") String replicaSetName,
                                              @RequestParam(name = "replicaId") Long replicaId,
                                              @RequestParam(name = "encryptKey") String encryptKey,
                                              @RequestParam(name = "encryptCert") String encryptCert,
                                              @RequestParam(name = "enable", defaultValue = "true") boolean enable,
                                              @RequestParam(name = "forceEncryption", defaultValue = "0") String forceEncryption,
                                              @RequestParam(name = "caIntermediateCert", defaultValue = "") String caIntermediateCert) throws Exception {
        String commonName = dbaasMetaClient.getDefaultmetaApi()
                .getReplicaSetLabel(RequestSession.getRequestId(), replicaSetName, SSLConst.LABEL_PARAM_CERT_COMMON_NAME);
        String oldForceEncryption = dbaasMetaClient.getDefaultmetaApi()
                .getReplicaSetLabel(RequestSession.getRequestId(), replicaSetName, SSLConst.MYSQL_SSL_FORCE_ENCRYPTION);
        CertConfigWithAES certConfigWithAES = new CertConfigWithAES(encryptKey, encryptCert, true, commonName, caIntermediateCert);
        String requireSecureTransport = "OFF";
        if (enable) {
            if ("1".equalsIgnoreCase(forceEncryption)){
                requireSecureTransport = "ON";
            }
            log.info("===> create SSL config for replicaSet {}-{}: {}", replicaSetName, replicaId, commonName);
            sslBaseService.initMysqlSSLCertConfig(replicaSetName, replicaId, certConfigWithAES, requireSecureTransport);
        } else {
            log.info("===> clear SSL config for replicaSet {}-{}: {}", replicaSetName, replicaId, commonName);
            sslBaseService.clearMysqlSSLCertConfig(replicaSetName, replicaId);
            if (StringUtils.isNotBlank(oldForceEncryption)&&"1".equalsIgnoreCase(oldForceEncryption)) {
                ParameterHelper.doReloadConfigFileAction(actionJobManager, replicaSetName, replicaId, ImmutableMap.of("loose_require_secure_transport", requireSecureTransport), false);
                Map<String, String> requireSecureTransportParams = new HashMap<>();
                requireSecureTransportParams.put(MYSQL_SSL_REQUIRE_SECURE_TRANSPORT, requireSecureTransport);
                dbaasMetaClient.getDefaultmetaApi().updateReplicaSetConfigs(RequestSession.getRequestId(), replicaSetName, requireSecureTransportParams, null);
            }

        }
        return Result.returnSuccessResult(RequestSession.getRequestId());
    }

    @ApiOperation(value = "更新实例SSL元数据")
    @PostMapping("/update_replica_label_for_ssl")
    public Result<String> configSSLForReplica(@RequestParam(name = "replicaSetName") String replicaSetName,
                                              @RequestParam(name = "enable", defaultValue = "true") boolean enable,
                                              @RequestParam(name = "forceEncryption", defaultValue = "0") String forceEncryption) throws Exception {
        Map<String, String> replicaSetParam = new HashMap<>();
        replicaSetParam.put(SSLConst.LABEL_PARAM_SSL_ENABLE, enable ? "1" : "0");
        replicaSetParam.put(SSLConst.MYSQL_SSL_FORCE_ENCRYPTION, enable ? forceEncryption : "0");
        replicaSetParam.put(SSLConst.MYSQL_SSL_CA_MODE, "intermediate");
        dbaasMetaClient.getDefaultmetaApi().updateReplicaSetLabels(RequestSession.getRequestId(), replicaSetName, replicaSetParam);
        return Result.returnSuccessResult(RequestSession.getRequestId());
    }

    @ApiOperation(value = "更新实例节点SSL配置")
    @PostMapping("/refresh_ssl_config_for_replica")
    public Result<String> configSSLForReplica(@RequestParam(name = "replicaId") Long replicaId) throws Exception {
        ReplicaResource replicaResource = dbaasMetaClient.getDefaultmetaApi()
                .getReplica(RequestSession.getRequestId(), replicaId, false);
        sslBaseService.refreshSSLCertConfig(replicaResource.getReplicaSetName(), replicaId);
        return Result.returnSuccessResult(RequestSession.getRequestId());
    }

    @ApiOperation(value = "获取旧SSL证书起始时间")
    @PostMapping("/get_ssl_start_time")
    public Result<Map<String, String>> getSSLStartTime(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                       @RequestParam(name = "replicaId") Long replicaId ) throws Exception {

        SQLResult sqlResult = sqlBaseExecutor.execQueryInReplica(replicaId,
                new MySQLCommand("SHOW GLOBAL STATUS where Variable_name='Ssl_server_not_before';"));
        if (!sqlResult.isSuccess()) {
            log.error("show SSL global status error");
            throw new ActivityException("check ssl/tls status error, "+ RequestSession.getRequestId());
        }
        String value = LangUtil.getString(sqlResult.getResultMap().get(0).get("Value"));
        log.info("===> get instance cert start time, replicaSetName: {}, startTime: {}",
                replicaSetName, value);
        Map<String, String> data = new HashMap<>();
        data.put("sslStartTime", value);
        log.info("===> get instance cert start time success: {}", JSONObject.toJSONString(data));
        return Result.returnSuccessResult(data);
    }

    @ApiOperation(value = "根据当前值重新配置活动的SSL上下文")
    @PostMapping("/alter_instance_reload_tls")
    public Result<String> dynamicLoadCertificates(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                  @RequestParam(name = "replicaId") Long replicaId) throws Exception {
        DefaultApi metaApi = dbaasMetaClient.getDefaultmetaApi();
        ReplicaResource replicaResource = metaApi.getReplica(RequestSession.getRequestId(), replicaId, false);
        ReplicaSet replicaSet = metaApi.getReplicaSetBundleResource(RequestSession.getRequestId(), replicaResource.getReplicaSetName()).getReplicaSet();
        String template = SQLTemplateSet.getSQLTemplateCommand(CommonConsts.DB_TYPE_MYSQL, replicaSet.getServiceVersion(), SQLTemplateSet.SQLCommand.DYNAMIC_LOAD_CERTIFICATES);
        log.info(template);
        SSLConst.CaType caType = sslMetaHelper.getReplicaSetCaType(replicaSetName);
        List<MySQLCommand> commandList = Lists.newArrayList();
        if (caType == SSLConst.CaType.ALIYUN) {
            String sslCaPath = sslBaseService.getMySQLSSLCaIntermediateCertFilePath(replicaSetName);
            String pathTemplate = SQLTemplateSet.getSQLTemplateCommand(CommonConsts.DB_TYPE_MYSQL, replicaSet.getServiceVersion(), SQLTemplateSet.SQLCommand.DYNAMIC_REFRESH_SSL_CA_PATH);
            log.info(pathTemplate);
            commandList.add(new MySQLCommand(pathTemplate, sslCaPath));
        }
        commandList.add(new MySQLCommand(template));
        List<SQLResult> sqlResultList = sqlBaseExecutor.execSQLInReplica(replicaId, commandList);
        for (SQLResult sqlResult : sqlResultList){
            if (!sqlResult.isSuccess()) {
                log.error("alter instance reload tls error");
                throw new ActivityException("alter instance reload tls error, " + sqlResult.getResult());
            }
        }
        return Result.returnSuccessResult(RequestSession.getRequestId());
    }


    //新证书after在老证书before之后即可，不一定比老证书要新
    @ApiOperation(value = "检查SSL是否配置成功")
    @PostMapping("/check_ssl_status")
    public Result<String> checkSSLForReplica(@RequestParam(name = "replicaSetName") String replicaSetName,
                                             @RequestParam(name = "replicaId") Long replicaId,
                                             @RequestParam(name = "oldSSLStartTime") String oldSSLStartTime) throws Exception {

        SQLResult sqlResult = sqlBaseExecutor.execQueryInReplica(replicaId,
                new MySQLCommand("SHOW GLOBAL STATUS where Variable_name='Ssl_server_not_after';"));
        if (!sqlResult.isSuccess()) {
            log.error("show SSL global status error");
            throw new ActivityException("show ssl global status error, "+ RequestSession.getRequestId());
        }
        String value = LangUtil.getString(sqlResult.getResultMap().get(0).get("Value"));

        if (StringUtils.isEmpty(value)) {
            log.error("ssl/tls status error");
            throw new ActivityException("ssl/tls status error, "+ RequestSession.getRequestId());
        }
        if (!StringUtils.isEmpty(oldSSLStartTime)){//Jun 16 11:54:54 2023 GMT
            DateFormat format = new SimpleDateFormat("MMM dd HH:mm:ss yyyy 'GMT'", Locale.ENGLISH);
            Date afterTime = format.parse(value);
            Date beforeTime = format.parse(oldSSLStartTime);
            if (afterTime.compareTo(beforeTime) >= 0) {
                log.info("dynamic update ssl success");
            } else {
                log.error("dynamic update ssl failed, before is {}, after is {}", beforeTime, afterTime);
                throw new NotRetryException("dynamic update ssl failed");
            }
        }
        return Result.returnSuccessResult(RequestSession.getRequestId());
    }


    @ApiOperation(value = "删除实例的SSL证书元数据")
    @PostMapping("/delete_replicaset_cert")
    public Result<String> deleteReplicaSetCert(@RequestParam(name = "replicaSetName") String replicaSetName,
                                               @RequestParam(name = "ignoreError", defaultValue = "false") Boolean ignoreError) throws Exception {
        ReplicaSet replicaSet = dbaasMetaClient.getDefaultmetaApi().getReplicaSet(RequestSession.getRequestId(), replicaSetName, null);
        if (replicaSet.getInsType() == ReplicaSet.InsTypeEnum.TMP) {
            log.info("this is tmp ins, skip it");
            return Result.returnSuccessResult(RequestSession.getRequestId());
        }
        if (replicaSet.getStatus() != ReplicaSet.StatusEnum.DELETING &&
                replicaSet.getStatus() != ReplicaSet.StatusEnum.DESTROYED &&
                replicaSet.getStatus() != ReplicaSet.StatusEnum.REPLICA_DELETING) {
            throw new NotRetryException("current's replicaSet status is " + replicaSet.getStatus() + ", not allow");
        }
        try {
            sslBaseService.deleteInstanceCert(replicaSetName);
        } catch (Exception e) {
            if (ignoreError) {
                log.warn("delete instance cert error, ignore it", e);
            } else {
                throw e;
            }
        }
        return Result.returnSuccessResult(RequestSession.getRequestId());
    }

    @ApiOperation(value = "修改只读实例TDE")
    @PostMapping("/modify_tde_for_read_ins")
    public Result<Boolean> modifyTdeForReadIns(@RequestParam(name = "replicaSetName") String replicaSetName) throws Exception {
        String requestId = RequestSession.getRequestId();
        try {
            ReplicaSetListResult replicaSetListResult = dbaasMetaClient.getDefaultmetaApi().listReplicaSetSubIns(requestId, replicaSetName, "readonly");
            if (replicaSetListResult != null) {
                List<String> readInsName = replicaSetListResult.getItems().stream().map(ReplicaSet::getName).collect(Collectors.toList());
                if (readInsName.isEmpty()) {
                    log.info("no read only instance, skip it");
                    return Result.returnSuccessResult(false);
                }
                JSONObject workflowParams = new JSONObject();
                workflowParams.put(TdeConsts.KMS_KEY_ID, workFlowBaseService.getValueFromContext(TdeConsts.KMS_KEY_ID, "").toString());
                workflowParams.put(TdeConsts.ENCRYPTION_MODE, workFlowBaseService.getValueFromContext(TdeConsts.ENCRYPTION_MODE, "").toString());
                workflowParams.put(TdeConsts.ENCRYPTION_DATA_KEY, workFlowBaseService.getValueFromContext(TdeConsts.ENCRYPTION_DATA_KEY, "").toString());
                for (String insName : readInsName) {
                    log.info("modify tde for read only instance: {}", insName);
                    workFlowBaseService.dispatchTask(insName, "modify_tde", workflowParams.toJSONString(), WorkFlowConsts.TASK_PRIORITY_COMMON);
                }
                return Result.returnSuccessResult(true);
            }
            return Result.returnSuccessResult(false);
        } catch (Exception e) {
            String msg = String.format("modifyTdeForReadIns encounter exception, requestId: %s, replicaSetName: %s", requestId, replicaSetName);
            log.error(msg, e);
            throw new ActivityException(e);
        }
    }

    @ApiOperation(value = "修改只读实例CLS")
    @PostMapping("/modify_cls_for_read_ins")
    public Result<String> modifyCLSForReadIns(@RequestParam(name = "replicaSetName") String replicaSetName) throws Exception {
        String requestId = RequestSession.getRequestId();
        try {
            List<String> subTaskIds = new ArrayList<>();
            ReplicaSetListResult replicaSetListResult = dbaasMetaClient.getDefaultmetaApi().listReplicaSetSubIns(requestId, replicaSetName, "readonly");
            if (replicaSetListResult != null) {
                List<String> readInsName = replicaSetListResult.getItems().stream().map(ReplicaSet::getName).collect(Collectors.toList());
                if (readInsName.isEmpty()) {
                    log.info("no read only instance, skip it");
                }
                JSONObject workflowParams = new JSONObject();
                workflowParams.put(CLSConsts.CLS_KEY_MODE, workFlowBaseService.getValueFromContext(CLSConsts.CLS_KEY_MODE, "").toString());
                workflowParams.put("encryptionStatus", workFlowBaseService.getValueFromContext("encryptionStatus", 0).toString());
                workflowParams.put(CLSConsts.KMS_KEY_ID, workFlowBaseService.getValueFromContext(CLSConsts.KMS_KEY_ID, "").toString());
                workflowParams.put(CLSConsts.ENCRYPTION_DATA_KEY, workFlowBaseService.getValueFromContext(CLSConsts.ENCRYPTION_DATA_KEY, "").toString());
                for (String insName : readInsName) {
                    log.info("modify cls for read only instance: {}", insName);
                    subTaskIds.add(workFlowBaseService.dispatchTask(insName, "modify_cls", workflowParams.toJSONString(), WorkFlowConsts.TASK_PRIORITY_COMMON));
                }
            }
            return Result.returnSuccessResult(StringUtils.join(subTaskIds, ","));
        } catch (Exception e) {
            String msg = String.format("modifyCLSForReadIns encounter exception, requestId: %s, replicaSetName: %s", requestId, replicaSetName);
            log.error(msg, e);
            throw new ActivityException(e);
        }
    }

    @ApiOperation(value = "安装 kms_agent")
    @PostMapping("/install_kms_agent")
    public Result<Boolean> installKmsAgent(@RequestParam(name = "replicaSetName") String replicaSetName, @RequestParam(name = "replicaId", required = false) Long replicaId)
        throws Exception {
        String requestId = RequestSession.getRequestId();
        try {
            List<Long> replicaIds;
            if (replicaId == null) {
                replicaIds = dbaasMetaClient.getDefaultmetaApi()
                .listReplicasInReplicaSet(requestId, replicaSetName, null, null, null, null).getItems().stream()
                .map((r) -> r.getId()).collect(Collectors.toList());
            } else {
                replicaIds = Collections.singletonList(replicaId);
            }
            Map<String, String> params = new HashMap<>();
            log.info("installing kms agent");
            actionJobManager
                .run(ActionJobParam.builder().resource(TdeAction.RESOURCE).action(TdeAction.InstallAgent.ACTION)
                    .replicaSetName(replicaSetName).replicaIdList(replicaIds).params(params).build());
            return Result.returnSuccessResult(true);
        } catch (Exception e) {
            String msg = String.format("installKmsAgent encounter exception, requestId: %s, replicaSetName: %s",
                requestId, replicaSetName);
            log.error(msg, e);
            throw new ActivityException(e);
        }
    }

    @ApiOperation(value = "刷新 kms service 配置文件")
    @PostMapping("/refresh_kms_service_config")
    public Result<Boolean> refreshKmsServiceConfig(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                   @RequestParam(name = "keyType", defaultValue = "TDE") String keyType,
                                                   @RequestParam(name = "replicaId", required = false) Long replicaId) throws Exception {
        String requestId = RequestSession.getRequestId();
        try {
            // get the user id and the role arn
            ReplicaSet replicaSet = dbaasMetaClient.getDefaultmetaApi().getReplicaSet(requestId, replicaSetName, null);
            String aliUid = replicaSet.getUserId().split("_")[1];
            UserRoleArnRel userRoleArnRel;
            // TDE and CLS use same rolearn
            userRoleArnRel = dbaasMetaClient.getDefaultmetaApi()
                    .getUserRoleArnRel(requestId, aliUid, null, TdeConsts.ROLE_ARN_TYPE_KMS, null).getItems().get(0);
            String roleArn = userRoleArnRel.getRoleArn();
            String region =
                dbaasMetaClient.getDefaultmetaApi().getReplicaSetLocation(requestId, replicaSetName).getRegion();
            // generate sts token credentials
            Credentials credentials = tdeBaseService.getSLRCredentials(requestId, region, aliUid, roleArn, null);
            // use resource to get KMS VPC endpoint
            ResourceKV res =
                dbaasMetaClient.getDefaultmetaApi().getResValue(requestId, TdeConsts.KMS_VPC_ENDPOINT_RESOURCE_KEY);
            Map<String, String> kmsVpcEndpoints = JSON.parseObject(res.getRealValue(), Map.class);
            String kmsVpcHostname = kmsVpcEndpoints.getOrDefault(region, null);
            ServiceCnf serviceCnf = new ServiceCnf(kmsVpcHostname, 443, credentials.getAccessKeyId(),
                credentials.getAccessKeySecret(), credentials.getSecurityToken(), region);
            String configDict = serviceCnf.toConfigDictJSONString();
            Map<String, String> params = new HashMap<>();
            params.put("config_dict", configDict);
            List<Long> replicaIds;
            if (replicaId != null) {
                replicaIds = Collections.singletonList(replicaId);
            } else {
                List<Replica> replicas = dbaasMetaClient.getDefaultmetaApi()
                    .listReplicasInReplicaSet(requestId, replicaSetName, null, null, null, null).getItems();
                replicaIds = replicas.stream().map((replica) -> replica.getId()).collect(Collectors.toList());
            }
            actionJobManager
                .run(ActionJobParam.builder()
                        .resource(keyType.equals(TdeConsts.TDE_KEY_TYPE) ? TdeAction.RESOURCE : CLSAction.RESOURCE)
                        .action(keyType.equals(TdeConsts.TDE_KEY_TYPE) ? TdeAction.UpdateServiceConfig.ACTION : CLSAction.UpdateServiceConfig.ACTION)
                        .replicaSetName(replicaSetName).replicaIdList(replicaIds).params(params).build());
            return Result.returnSuccessResult(true);
        } catch (Exception e) {
            String msg = String.format("refreshKmsAgentConfig encounter exception, requestId: %s, replicaSetName: %s",
                requestId, replicaSetName);
            log.error(msg, e);
            throw new ActivityException(e);
        }
    }

    @ApiOperation(value = "生成数据密钥")
    @PostMapping("/generate_data_key")
    public Result<EncryptionDataKey> generateDataKey(@RequestParam(name = "replicaSetName") String replicaSetName,
        @RequestParam(name = "extraParams") String extraParams)
        throws Exception {
        String requestId = RequestSession.getRequestId();
        try {
            Map<String, Object> extraParamsMap = JSON.parseObject(extraParams, Map.class);
            String kmsKeyId = extraParamsMap.getOrDefault(TdeConsts.KMS_KEY_ID, "").toString();
            String encryptionMode = extraParamsMap.getOrDefault(TdeConsts.ENCRYPTION_MODE, "").toString();
            if (StringUtils.isBlank(kmsKeyId) || StringUtils.isBlank(encryptionMode)) {
                throw new Exception("invalid param");
            }
            String encryptionDataKey = extraParamsMap.getOrDefault(TdeConsts.ENCRYPTION_DATA_KEY, "").toString();
            ReplicaSet replicaSet = dbaasMetaClient.getDefaultmetaApi().getReplicaSet(requestId, replicaSetName, null);
            String aliUid = replicaSet.getUserId().split("_")[1];
            UserRoleArnRel userRoleArnRel = dbaasMetaClient.getDefaultmetaApi()
                .getUserRoleArnRel(requestId, aliUid, null, TdeConsts.ROLE_ARN_TYPE_KMS, null).getItems().get(0);
            String roleArn = userRoleArnRel.getRoleArn();
            String region =
                dbaasMetaClient.getDefaultmetaApi().getReplicaSetLocation(requestId, replicaSetName).getRegion();
            // prepare the data key
            EncryptionDataKey dataKey;
            if (StringUtils.isNotBlank(encryptionDataKey)) {
                dataKey = JSONObject.parseObject(encryptionDataKey, EncryptionDataKey.class);
            } else {
                dataKey = tdeBaseService.generateDataKey(requestId, region, kmsKeyId, aliUid, roleArn, TDEBaseService.KeyType.TDE);
            }
            // update kms_audit_chain
            KmsAuditChain kmsAuditChain = new KmsAuditChain().custinsId(Math.toIntExact(replicaSet.getId()))
                .regionId(region).encryptionMode(encryptionMode).kmsCmkId(kmsKeyId).taskId(0).rolearn(roleArn)
                .kmsEndpoint(dataKey.getKmsEndPoint()).stsEndpoint(dataKey.getStsEndPoint())
                .rdsMkId(dataKey.getMasterKeyId()).ciphertext(dataKey.getCiphertextBlob())
                .plainMd5(dataKey.getPlaintextMd5()).cipherMd5(dataKey.getCiphertextBlobMd5()).status(1);
            log.info("inserting KmsAuditChain:{}", kmsAuditChain);
            dbaasMetaClient.getDefaultmetaApi().addKmsAuditChain(requestId, kmsAuditChain);
            Map<String, Object> context = new HashMap<>();
            context.put(TdeConsts.KMS_KEY_ID, kmsKeyId);
            context.put(TdeConsts.ENCRYPTION_MODE, encryptionMode);
            context.put(TdeConsts.KMS_AUDIT_CHAIN, kmsAuditChain);
            context.put(TdeConsts.ENCRYPTION_DATA_KEY, dataKey);
            workFlowBaseService.updateWorkflowContext(context);
            return Result.returnSuccessResult(dataKey);
        } catch (Exception e) {
            String msg = String.format("generateDataKey encounter exception, requestId: %s, replicaSetName: %s",
                requestId, replicaSetName);
            log.error(msg, e);
            throw new ActivityException(e);
        }
    }

    @ApiOperation(value = "为列加密生成数据密钥")
    @PostMapping("/generate_cls_data_key")
    public Result<EncryptionDataKey> generateDataKeyCLS(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                        @RequestParam(name = "extraParams") String extraParams)
            throws Exception {
        // 只有当需要设置密钥时任务流需要包含这一步。
        String requestId = RequestSession.getRequestId();
        try {
            ReplicaSet replicaSet = dbaasMetaClient.getDefaultmetaApi().getReplicaSet(requestId, replicaSetName, null);
            Map<String, Object> context = new HashMap<>();
            // 需要生成datakey
            Map<String, Object> extraParamsMap = JSON.parseObject(extraParams, Map.class);
            String kmsKeyId = extraParamsMap.getOrDefault(CLSConsts.KMS_KEY_ID, "").toString();
            Integer encryptionStatus = (Integer)extraParamsMap.getOrDefault("encryptionStatus", 0);
            if (StringUtils.isBlank(kmsKeyId)) {
                throw new Exception("invalid param");
            }
            String aliUid = replicaSet.getUserId().split("_")[1];
            UserRoleArnRel userRoleArnRel = dbaasMetaClient.getDefaultmetaApi()
                    .getUserRoleArnRel(requestId, aliUid, null, CLSConsts.ROLE_ARN_TYPE_KMS, null).getItems().get(0);
            String roleArn = userRoleArnRel.getRoleArn();
            String region =
                    dbaasMetaClient.getDefaultmetaApi().getReplicaSetLocation(requestId, replicaSetName).getRegion();
            // prepare the data key
            String encryptionDataKey = extraParamsMap.getOrDefault(CLSConsts.ENCRYPTION_DATA_KEY, "").toString();
            EncryptionDataKey dataKey;
            if (StringUtils.isNotBlank(encryptionDataKey)) {
                dataKey = JSONObject.parseObject(encryptionDataKey, EncryptionDataKey.class);
            } else {
                dataKey = tdeBaseService.generateDataKey(requestId, region, kmsKeyId, aliUid, roleArn, TDEBaseService.KeyType.CLS);
            }
            // 查询已有的kms audit chain，避免taskid冲突
            KmsAuditChain kmsAuditChain = null;
            KmsAuditChainListResult kmsAuditChainListResult = dbaasMetaClient.getDefaultmetaApi().getKmsAuditChain(requestId, Math.toIntExact(replicaSet.getId()),
                    null, null);
            int nextTaskId = 1;
            if (kmsAuditChainListResult != null && kmsAuditChainListResult.getItems() != null && kmsAuditChainListResult.getItems().size() > 0) {
                for (int i = kmsAuditChainListResult.getItems().size() - 1; i >= 0; i--) {
                    kmsAuditChain = kmsAuditChainListResult.getItems().get(i);
                    if (kmsAuditChain.getTaskId() >= nextTaskId) {
                        nextTaskId = kmsAuditChain.getTaskId() + 1;
                    }
                }
            }
            // update kms_audit_chain
            kmsAuditChain = new KmsAuditChain().custinsId(Math.toIntExact(replicaSet.getId()))
                    .regionId(region).encryptionMode("byok_key").kmsCmkId(kmsKeyId).rolearn(roleArn).taskId(nextTaskId)
                    .kmsEndpoint(dataKey.getKmsEndPoint()).stsEndpoint(dataKey.getStsEndPoint())
                    .rdsMkId(dataKey.getMasterKeyId()).ciphertext(dataKey.getCiphertextBlob())
                    .plainMd5(dataKey.getPlaintextMd5()).cipherMd5(dataKey.getCiphertextBlobMd5()).status(1);
            log.info("inserting KmsAuditChain:{}", kmsAuditChain);
            dbaasMetaClient.getDefaultmetaApi().addKmsAuditChain(requestId, kmsAuditChain);
            context.put(CLSConsts.KMS_KEY_ID, kmsKeyId);
            context.put(CLSConsts.KMS_AUDIT_CHAIN, kmsAuditChain);
            context.put(CLSConsts.ENCRYPTION_DATA_KEY, dataKey);
            context.put("encryptionStatus", encryptionStatus);
            workFlowBaseService.updateWorkflowContext(context);
            return Result.returnSuccessResult(dataKey);
        } catch (Exception e) {
            String msg = String.format("generateDataKey encounter exception, requestId: %s, replicaSetName: %s",
                    requestId, replicaSetName);
            log.error(msg, e);
            throw new ActivityException(e);
        }
    }

    @ApiOperation(value = "安装 kms config")
    @PostMapping("/install_kms_config")
    public Result<Boolean> installKmsConfig(@RequestParam(name = "replicaSetName") String replicaSetName,
        @RequestParam(name = "masterKeyId") String masterKeyId,
        @RequestParam(name = "plaintextMd5") String plaintextMd5,
        @RequestParam(name = "ciphertextBlob") String ciphertextBlob,
        @RequestParam(name = "keyType") String keyType,
        @RequestParam(name = "replicaId", required = false) Long replicaId) throws Exception {
        String requestId = RequestSession.getRequestId();
        try {
            List<Long> replicaIds;
            if (replicaId != null) {
                replicaIds = Collections.singletonList(replicaId);
            } else {
                replicaIds = dbaasMetaClient.getDefaultmetaApi()
                .listReplicasInReplicaSet(requestId, replicaSetName, null, null, null, null).getItems().stream()
                .map((r) -> r.getId()).collect(Collectors.toList());
            }
            Map<String, String> params = new HashMap<>();
            if (keyType.equals(TdeConsts.TDE_KEY_TYPE)) {
                params.put(TdeConsts.MASTER_KEY_ID, masterKeyId);
                params.put(TdeConsts.MASTER_KEY_CIPHERTEXT, ciphertextBlob);
                params.put(TdeConsts.MASTER_KEY_PLAINTEXT_MD5, plaintextMd5);
                actionJobManager
                        .run(ActionJobParam.builder().resource(TdeAction.RESOURCE).action(TdeAction.InstallConfig.ACTION)
                                .replicaSetName(replicaSetName).replicaIdList(replicaIds).params(params).build());
            } else if (keyType.equals(CLSConsts.CLS_KEY_TYPE)) {
                params.put(CLSConsts.MASTER_KEY_ID, masterKeyId);
                params.put(CLSConsts.MASTER_KEY_CIPHERTEXT, ciphertextBlob);
                params.put(CLSConsts.MASTER_KEY_PLAINTEXT_MD5, plaintextMd5);
                actionJobManager
                        .run(ActionJobParam.builder().resource(CLSAction.RESOURCE).action(CLSAction.InstallConfig.ACTION)
                                .replicaSetName(replicaSetName).replicaIdList(replicaIds).params(params).build());
            } else {
                throw new ActivityException("unsupported keyType " + keyType);
            }
            return Result.returnSuccessResult(true);
        } catch (Exception e) {
            String msg = String.format("installKmsConfig encounter exception, requestId: %s, replicaSetName: %s",
                requestId, replicaSetName);
            log.error(msg, e);
            throw new ActivityException(e);
        }
    }

    @ApiOperation(value = "恢复时检查设置开启TDE")
    @PostMapping("/setup_tde_config_for_replica")
    public Result<Map<String, Object>> setupTdeConfigForReplica(
        @RequestParam(name = "replicaId") Long replicaId,
        @RequestParam(name = "srcReplicaSetName", required = false) String srcReplicaSetName,
        @RequestParam(name = "backupSetId", required = false) Long backupSetId,
        @RequestParam(name = "tdeEnabled", required = false) Boolean tdeEnabled,
        @RequestParam(name = "tdeEncryptionKeyId", required = false) String tdeEncryptionKeyId) throws Exception {
        String requestId = RequestSession.getRequestId();
        try {
            ReplicaResource destReplica = dbaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), replicaId, false);
            String destReplicaSetName = destReplica.getReplicaSetName();
            ReplicaSet destReplicaSet = dbaasMetaClient.getDefaultmetaApi().getReplicaSet(requestId, destReplicaSetName, false);
            // 针对临时实例，获取源实例名
            if (StringUtils.isBlank(srcReplicaSetName) && StringUtils.isNotBlank(destReplicaSet.getPrimaryInsName())) {
                srcReplicaSetName = destReplicaSet.getPrimaryInsName();
            }
            Map<String, Object> returnData = new HashMap<>();
            returnData.put(TdeConsts.TDE_ENABLED, false);
            returnData.put(TdeConsts.NEED_GENERATE_KEY, false);
            // prepare data
            boolean destReplicaSetTdeEnabled = false;
            String destReplicaSetTdeMode = null;
            String destEncryptionKeyId = tdeEncryptionKeyId;
            // 创建时已经检查过TDE开启
            if (tdeEnabled != null && tdeEnabled) {
                destReplicaSetTdeEnabled = true;
            }
            // 叠加备份集标记
            if (srcReplicaSetName != null && backupSetId != null) {
                log.info("checking backup set tde status");
                SlaveStatus slaveStatus = backupBaseService.getBackupSet(srcReplicaSetName, backupSetId).getSlaveStatusObj();
                if (slaveStatus.getTdeEnabled() != null && slaveStatus.getTdeEnabled()) {
                    destReplicaSetTdeEnabled = true;
                    destReplicaSetTdeMode    = slaveStatus.getTdeMode();
                    destEncryptionKeyId      = slaveStatus.getTdeEncryptionKeyId();
                    log.info(String.format("tde enabled in backup set, tde_mode:%s, kms_key_id:%s", destReplicaSetTdeMode, destEncryptionKeyId));
                }
            }
            // 叠加源实例参数
            ReplicaSet srcReplicaSet = null;
            if (StringUtils.isNotBlank(srcReplicaSetName)) {
                log.info("checking source replica set tde status");
                srcReplicaSet = dbaasMetaClient.getDefaultmetaApi().getReplicaSet(requestId, srcReplicaSetName, null);
                Map<String, String> labels = dbaasMetaClient.getDefaultmetaApi().listReplicaSetLabels(requestId, srcReplicaSetName);
                if (labels != null) {
                    String srcReplicaSetTdeEnabled = labels.get(TdeConsts.TDE_ENABLED);
                    if (StringUtils.equalsIgnoreCase(srcReplicaSetTdeEnabled, "1")) {
                        destReplicaSetTdeEnabled = true;
                        destReplicaSetTdeMode    = labels.get(TdeConsts.TDE_MODE);
                        destEncryptionKeyId      = labels.get(TdeConsts.TDE_ENCRYPTION_KEY_ID);
                        log.info(String.format("tde enabled in src replica set, tde_mode:%s, kms_key_id:%s", destReplicaSetTdeMode, destEncryptionKeyId));
                    }
                }
            }
            // 检查目标实例是否已有TDE标记
            Map<String, String> labels = dbaasMetaClient.getDefaultmetaApi().listReplicaSetLabels(requestId, destReplicaSetName);
            if (labels != null) {
                String destReplicaSetTdeEnabledStr = labels.get(TdeConsts.TDE_ENABLED);
                if (StringUtils.equalsIgnoreCase(destReplicaSetTdeEnabledStr, "1")) {
                    destReplicaSetTdeEnabled = true;
                    destReplicaSetTdeMode    = labels.get(TdeConsts.TDE_MODE);
                    destEncryptionKeyId      = labels.get(TdeConsts.TDE_ENCRYPTION_KEY_ID);
                    log.info(String.format("tde already enabled in dest replica set, tde_mode:%s, kms_key_id:%s", destReplicaSetTdeMode, destEncryptionKeyId));
                }
            }
            // 目标实例需要开启TDE
            if (destReplicaSetTdeEnabled) {
                log.info("should enable tde on dest replica set");
                // 如果目标实例是临时实例，则需要全新安装kms agent service
                if (destReplicaSet.getIsTmp() != null && destReplicaSet.getIsTmp()) {
                    log.info("tmp ins needs full tde install");
                    this.installKmsAgent(destReplicaSetName, replicaId);
                    // 查询源实例密钥信息, 如果没有源实例，直接报错
                    KmsAuditChainListResult kmsAuditChainListResult = dbaasMetaClient.getDefaultmetaApi().getKmsAuditChain(requestId, Math.toIntExact(srcReplicaSet.getId()),
                                                                                                                           destEncryptionKeyId, null);
                    // 理论上应该有
                    if (kmsAuditChainListResult.getItems() == null || kmsAuditChainListResult.getItems().isEmpty()) {
                        log.error("source replica set {} does not have kms audit chain info, this should be a bug", srcReplicaSet.getName());
                        throw new Exception("no kms audit chain found");
                    }
                    KmsAuditChain kmsAuditChain = null;
                    for (int i = kmsAuditChainListResult.getItems().size() - 1; i >= 0; i--) {
                        kmsAuditChain = kmsAuditChainListResult.getItems().get(i);
                        if (kmsAuditChain.getTaskId() == 0) {
                            break;
                        }
                    }
                    if (kmsAuditChain == null) {
                        log.error("source replica set {} does not have kms audit chain with task_id 0 (for TDE), this should be a bug", srcReplicaSet.getName());
                        throw new Exception("no kms audit chain with task_id 0 found");
                    }
                    String rdsMkId = kmsAuditChain.getRdsMkId();
                    String ciphertext = kmsAuditChain.getCiphertext();
                    String plaintextMd5 = kmsAuditChain.getPlainMd5();
                    destReplicaSetTdeMode = kmsAuditChain.getEncryptionMode();
                    this.installKmsConfig(destReplicaSetName, rdsMkId, plaintextMd5, ciphertext, TdeConsts.TDE_KEY_TYPE, replicaId);
                    returnData.put(TdeConsts.NEED_GENERATE_KEY, true);
                }
                // rds service配置 不管是不是临时实例都重新刷一遍
                this.refreshKmsServiceConfig(destReplicaSetName, TdeConsts.TDE_KEY_TYPE, replicaId);
                // 刷新参数配置
                if (destReplicaSetTdeMode == null) {
                    destReplicaSetTdeMode = StringUtils.startsWithIgnoreCase(destEncryptionKeyId, "key-") ? TdeConsts.TDE_MODE_BYOK : TdeConsts.TDE_MODE_SERVICE;
                }
                log.info(String.format("tde enabled with mode:%s, key:%s", destReplicaSetTdeMode, destEncryptionKeyId));
                dbaasMetaClient.getDefaultmetaApi().updateReplicaSetLabels(requestId, destReplicaSetName,
                                                                           ImmutableMap.of(TdeConsts.TDE_ENABLED, "1", TdeConsts.TDE_MODE, destReplicaSetTdeMode,
                                                                                           TdeConsts.TDE_ENCRYPTION_KEY_ID, destEncryptionKeyId));
                workFlowBaseService.updateWorkflowContext(ImmutableMap.of(TdeConsts.TDE_ENABLED, "1", TdeConsts.TDE_MODE, destReplicaSetTdeMode,
                                                                          TdeConsts.TDE_ENCRYPTION_KEY_ID, destEncryptionKeyId));
                this.refreshTdeConfigForReplica(destReplicaSetName, replicaId);
                // 新实例更新密钥审计表，有源实例直接使用源实例密钥审计信息
                KmsAuditChain kmsAuditChain = null;
                if (StringUtils.isNotBlank(srcReplicaSetName)) {
                    KmsAuditChainListResult kmsAuditChainListResult = dbaasMetaClient.getDefaultmetaApi().getKmsAuditChain(requestId, Math.toIntExact(srcReplicaSet.getId()),
                                                                                                                           destEncryptionKeyId, null);
                    for (int i = kmsAuditChainListResult.getItems().size() - 1; i >= 0; i--) {
                        kmsAuditChain = kmsAuditChainListResult.getItems().get(i);
                        if (kmsAuditChain.getTaskId() == 0) {
                            kmsAuditChain.setCustinsId(Math.toIntExact(destReplicaSet.getId()));
                            break;
                        }
                    }
                }
                // 没有源实例使用数据盘上的密钥信息
                if (kmsAuditChain == null) {
                    kmsAuditChain = tdeBaseService.getKmsAuditChainInfoFromDisk(destReplicaSetName, replicaId, TdeConsts.TDE_KEY_TYPE).custinsId(Math.toIntExact(destReplicaSet.getId()))
                                                  .kmsCmkId(destEncryptionKeyId).taskId(0);
                }
                // 更新新实例密钥审计表
                try {
                    dbaasMetaClient.getDefaultmetaApi().addKmsAuditChain(requestId, kmsAuditChain);
                } catch (ApiException e) {
                    log.warn("KmsAuditChain already exists");
                }
                returnData.put(TdeConsts.TDE_ENABLED, true);
                return Result.returnSuccessResult(returnData);
            }
            return Result.returnSuccessResult(returnData);
        } catch (BaseServiceException e) {
            String msg = String.format(
                "setupTdeConfigForReplica encounter BaseServiceException, requestId: %s, replicaId: %d, code: %s, message:%s",
                requestId, replicaId, e.getCode(), e.getMessage());
            log.error(msg, e);
            throw new ActivityException(e);
        } catch (Exception e) {
            String msg =
                String.format("setupTdeConfigForReplica encounter exception, requestId: %s", requestId);
            log.error(msg, e);
            throw new ActivityException(e);
        }
    }


    @ApiOperation(value = "恢复时检查设置开启CLS")
    @PostMapping("/setup_cls_config_for_replica")
    public Result<Map<String, Object>> setupClsConfigForReplica(
            @RequestParam(name = "replicaId") Long replicaId,
            @RequestParam(name = "srcReplicaSetName", required = false) String srcReplicaSetName,
            @RequestParam(name = "backupSetId", required = false) Long backupSetId,
            @RequestParam(name = "clsKeyMode", required = false) String clsKeyMode,
            @RequestParam(name = "clsEncryptionKeyId", required = false) String clsEncryptionKeyId) throws Exception {
        String requestId = RequestSession.getRequestId();
        try {
            ReplicaResource destReplica = dbaasMetaClient.getDefaultmetaApi().getReplica(RequestSession.getRequestId(), replicaId, false);
            String destReplicaSetName = destReplica.getReplicaSetName();
            ReplicaSet destReplicaSet = dbaasMetaClient.getDefaultmetaApi().getReplicaSet(requestId, destReplicaSetName, false);
            // 针对临时实例，获取源实例名
            if (StringUtils.isBlank(srcReplicaSetName) && StringUtils.isNotBlank(destReplicaSet.getPrimaryInsName())) {
                srcReplicaSetName = destReplicaSet.getPrimaryInsName();
            }
            Map<String, Object> returnData = new HashMap<>();
            returnData.put(CLSConsts.NEED_GENERATE_KEY, false);
            returnData.put(CLSConsts.CLS_KEY_MODE, null);
            // prepare data
            String destClsKeyMode = clsKeyMode;
            String destEncryptionKeyId = clsEncryptionKeyId;
            // 叠加备份集标记
            if (srcReplicaSetName != null && backupSetId != null) {
                log.info("checking backup set tde status");
                SlaveStatus slaveStatus = backupBaseService.getBackupSet(srcReplicaSetName, backupSetId).getSlaveStatusObj();
                if (StringUtils.equalsIgnoreCase(slaveStatus.getClsKeyMode(), CLSConsts.CLS_KEY_MODE_KMS_KEY)) {
                    destClsKeyMode = CLSConsts.CLS_KEY_MODE_KMS_KEY;
                    destEncryptionKeyId = slaveStatus.getClsEncryptionKeyId();
                    log.info(String.format("cls enabled in backup set, cls_key_mode:%s, cls_encryption_key_id:%s", destClsKeyMode, destEncryptionKeyId));
                }
            }
            // 叠加源实例参数
            ReplicaSet srcReplicaSet = null;
            if (StringUtils.isNotBlank(srcReplicaSetName)) {
                log.info("checking source replica set cls status");
                srcReplicaSet = dbaasMetaClient.getDefaultmetaApi().getReplicaSet(requestId, srcReplicaSetName, null);
                Map<String, String> labels = dbaasMetaClient.getDefaultmetaApi().listReplicaSetLabels(requestId, srcReplicaSetName);
                if (labels != null) {
                    String srcReplicaSetClsKeyMode = labels.get(CLSConsts.CLS_KEY_MODE);
                    if (StringUtils.equalsIgnoreCase(srcReplicaSetClsKeyMode, CLSConsts.CLS_KEY_MODE_KMS_KEY)) {
                        destClsKeyMode = CLSConsts.CLS_KEY_MODE_KMS_KEY;
                        destEncryptionKeyId = labels.get(CLSConsts.CLS_ENCRYPTION_KEY_ID);
                        log.info(String.format("cls enabled in src replica set, cls_key_mode:%s, cls_encryption_key_id:%s", destClsKeyMode, destEncryptionKeyId));
                    }
                }
            }
            // 检查目标实例是否已有CLS标记
            Map<String, String> labels = dbaasMetaClient.getDefaultmetaApi().listReplicaSetLabels(requestId, destReplicaSetName);
            if (labels != null) {
                String destReplicaSetClsKeyMode = labels.get(CLSConsts.CLS_KEY_MODE);
                if (StringUtils.equalsIgnoreCase(destReplicaSetClsKeyMode, CLSConsts.CLS_KEY_MODE_KMS_KEY)) {
                    destClsKeyMode = CLSConsts.CLS_KEY_MODE_KMS_KEY;
                    destEncryptionKeyId = labels.get(CLSConsts.CLS_ENCRYPTION_KEY_ID);
                    log.info(String.format("cls enabled in dest replica set, cls_key_mode:%s, cls_encryption_key_id:%s", destClsKeyMode, destEncryptionKeyId));
                }
            }
            // 目标实例启用了kms_key模式
            if (CLSConsts.CLS_KEY_MODE_KMS_KEY.equals(destClsKeyMode)) {
                log.info("should set cls to kms mode on dest replica set");
                // 如果目标实例是临时实例，则需要全新安装kms agent service
                if (destReplicaSet.getIsTmp() != null && destReplicaSet.getIsTmp()) {
                    log.info("tmp ins needs full kms agent install");
                    this.installKmsAgent(destReplicaSetName, replicaId);
                    // 查询源实例密钥信息, 如果没有源实例，直接报错
                    KmsAuditChainListResult kmsAuditChainListResult = dbaasMetaClient.getDefaultmetaApi().getKmsAuditChain(requestId, Math.toIntExact(srcReplicaSet.getId()),
                            destEncryptionKeyId, null);
                    // 理论上应该有
                    if (kmsAuditChainListResult.getItems() == null || kmsAuditChainListResult.getItems().isEmpty()) {
                        log.error("source replica set {} does not have kms audit chain info, this should be a bug", srcReplicaSet.getName());
                        throw new Exception("no kms audit chain found");
                    }
                    // CLS可能对实例多次记录kmsAuditChain，但是taskid最大的一定是最新的一个。
                    KmsAuditChain kmsAuditChain = null;
                    int maxTaskId = 1;
                    for (int i = kmsAuditChainListResult.getItems().size() - 1; i >= 0; i--) {
                        KmsAuditChain tmpKmsAuditChain = kmsAuditChainListResult.getItems().get(i);
                        if (tmpKmsAuditChain.getTaskId() >= maxTaskId) {
                            maxTaskId = tmpKmsAuditChain.getTaskId();
                            kmsAuditChain = tmpKmsAuditChain;
                        }
                    }
                    if (kmsAuditChain == null) {
                        log.error("source replica set {} does not have kms audit chain with task_id > 0 (for CLS), this should be a bug", srcReplicaSet.getName());
                        throw new Exception("no kms audit chain with task_id > 0 found");
                    }
                    String rdsMkId = kmsAuditChain.getRdsMkId();
                    String ciphertext = kmsAuditChain.getCiphertext();
                    String plaintextMd5 = kmsAuditChain.getPlainMd5();
                    this.installKmsConfig(destReplicaSetName, rdsMkId, plaintextMd5, ciphertext, CLSConsts.CLS_KEY_TYPE, replicaId);
                    returnData.put(CLSConsts.NEED_GENERATE_KEY, true);
                }
                // rds service配置 不管是不是临时实例都重新刷一遍
                this.refreshKmsServiceConfig(destReplicaSetName, CLSConsts.CLS_KEY_TYPE, replicaId);
                // 刷新参数配置
                log.info(String.format("cls enabled iwith cls_key_mode:%s, cls_encryption_key_id:%s", destClsKeyMode, destEncryptionKeyId));
                dbaasMetaClient.getDefaultmetaApi().updateReplicaSetLabels(requestId, destReplicaSetName,
                        ImmutableMap.of(CLSConsts.CLS_KEY_MODE, CLSConsts.CLS_KEY_MODE_KMS_KEY, CLSConsts.CLS_ENCRYPTION_KEY_ID, destEncryptionKeyId));
                workFlowBaseService.updateWorkflowContext(
                        ImmutableMap.of(CLSConsts.CLS_KEY_MODE, CLSConsts.CLS_KEY_MODE_KMS_KEY, CLSConsts.CLS_ENCRYPTION_KEY_ID, destEncryptionKeyId));
                this.setEncdbKMSAgentConfigForReplica(destReplicaSetName, replicaId, true);
                // 新实例更新密钥审计表，有源实例直接使用源实例密钥审计信息
                KmsAuditChain kmsAuditChain = null;
                if (StringUtils.isNotBlank(srcReplicaSetName)) {
                    KmsAuditChainListResult kmsAuditChainListResult = dbaasMetaClient.getDefaultmetaApi().getKmsAuditChain(requestId, Math.toIntExact(srcReplicaSet.getId()),
                            destEncryptionKeyId, null);
                    if (kmsAuditChainListResult != null && !kmsAuditChainListResult.getItems().isEmpty()) {
                        int maxTaskId = 1;
                        for (int i = kmsAuditChainListResult.getItems().size() - 1; i >= 0; i--) {
                            KmsAuditChain tmpKmsAuditChain = kmsAuditChainListResult.getItems().get(i);
                            if (tmpKmsAuditChain.getTaskId() >= maxTaskId) {
                                maxTaskId = tmpKmsAuditChain.getTaskId();
                                kmsAuditChain = tmpKmsAuditChain;
                                kmsAuditChain.setCustinsId(Math.toIntExact(destReplicaSet.getId()));
                            }
                        }
                    }
                }
                // 没有源实例使用数据盘上的密钥信息
                if (kmsAuditChain == null) {
                    kmsAuditChain = tdeBaseService.getKmsAuditChainInfoFromDisk(destReplicaSetName, replicaId, CLSConsts.CLS_KEY_TYPE).custinsId(Math.toIntExact(destReplicaSet.getId()))
                            .kmsCmkId(destEncryptionKeyId).taskId(1);
                }
                // 更新新实例密钥审计表
                try {
                    dbaasMetaClient.getDefaultmetaApi().addKmsAuditChain(requestId, kmsAuditChain);
                } catch (ApiException e) {
                    log.warn("KmsAuditChain already exists");
                }
                returnData.put(CLSConsts.CLS_KEY_MODE, CLSConsts.CLS_KEY_MODE_KMS_KEY);
                return Result.returnSuccessResult(returnData);
            }
            return Result.returnSuccessResult(returnData);
        } catch (BaseServiceException e) {
            String msg = String.format(
                    "setupClsConfigForReplica encounter BaseServiceException, requestId: %s, replicaId: %d, code: %s, message:%s",
                    requestId, replicaId, e.getCode(), e.getMessage());
            log.error(msg, e);
            throw new ActivityException(e);
        } catch (Exception e) {
            String msg =
                    String.format("setupClsConfigForReplica encounter exception, requestId: %s", requestId);
            log.error(msg, e);
            throw new ActivityException(e);
        }
    }

    @ApiOperation(value = "配置CLS的encdb_kms_agent_cmd参数，为启用KMS模式作准备")
    @PostMapping("/set_encdb_kms_agent_config_for_replica")
    public Result<Boolean> setEncdbKMSAgentConfigForReplica(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                            @RequestParam(name = "replicaId") Long replicaId,
                                                            @RequestParam(name = "reloadFileOnly", required = false) boolean reloadFileOnly) throws Exception {
        String requestId = RequestSession.getRequestId();
        try {
            // 动态设置不需要重启
            HashMap<String, Object> config = new HashMap<>();
            // ''包一下，符合mysql字符串格式
            String mysqlStrVal = "'" + MysqlConsts.KMS_AGENT_DIR + "/bin/kms_agent" + "'";
            config.put(CLSConsts.LOOSE_ENCDB_KMS_AGENT_CMD, mysqlStrVal);
            if (!reloadFileOnly)
                ParameterHelper.doReloadConfigFileAction(actionJobManager, replicaSetName, replicaId, config, true);
            ParameterHelper.doReloadConfigFileAction(actionJobManager, replicaSetName, replicaId, config, false);
            return Result.returnSuccessResult(true);
        } catch (Exception e) {
            String msg = String.format("setEncdbKMSAgentConfigForReplica encounter exception, requestId: %s, replicaSetName: %s",
                    requestId, replicaSetName);
            log.error(msg, e);
            throw new ActivityException(e);
        }
    }

    @ApiOperation(value = "刷新 TDE 开启参数")
    @PostMapping("/refresh_tde_config_for_replica")
    public Result<Boolean> refreshTdeConfigForReplica(@RequestParam(name = "replicaSetName") String replicaSetName,
        @RequestParam(name = "replicaId") Long replicaId) throws Exception {
        String requestId = RequestSession.getRequestId();
        try {
            HashMap<String, Object> config = new HashMap<>();
            config.put(TdeConsts.EARLY_PLUGIN_LOAD, TdeConsts.KEYRING_RDS_SO);
            config.put(TdeConsts.LOOSE_KEYRING_RDS_KMS_AGENT_CMD, MysqlConsts.KMS_AGENT_DIR + "/bin/kms_agent");
            ParameterHelper.doReloadConfigFileAction(actionJobManager, replicaSetName, replicaId, config, false);
            return Result.returnSuccessResult(true);
        } catch (Exception e) {
            String msg = String.format("refreshTdeConfig encounter exception, requestId: %s, replicaSetName: %s",
                requestId, replicaSetName);
            log.error(msg, e);
            throw new ActivityException(e);
        }
    }

    @ApiOperation(value = "call dbms_keyring.generate_key()")
    @PostMapping("/call_generate_key_for_replica")
    public Result<Boolean> callGenerateKeyForReplica(@RequestParam(name = "replicaId") Long replicaId) throws Exception {
        String requestId = RequestSession.getRequestId();
        try {
            SQLResult sqlResult = sqlBaseExecutor.execQueryInReplica(replicaId,
                new MySQLCommand(TdeConsts.DBMS_KEYRING_GENERATE_KEY_SQL));
            if (!sqlResult.isSuccess()) {
                String msg = String.format("failed to call dbms_keyring.generate_key(), requestId: %s", requestId);
                log.error(msg);
                throw new ActivityException(msg);
            }
            return Result.returnSuccessResult(true);
        } catch (Exception e) {
            String msg = String.format("callGenerateKey encounter exception, requestId: %s, replicaId: %s", requestId, replicaId);
            log.error(msg, e);
            throw new ActivityException(e);
        }

    }

    @ApiOperation(value = "生成KMS密钥")
    @PostMapping("/call_set_kms_key_for_replica")
    public Result<Boolean> callGenerateCLSKeyForReplica(@RequestParam(name = "replicaId") Long replicaId) throws Exception {
        String requestId = RequestSession.getRequestId();
        try {
            SQLResult sqlResult = sqlBaseExecutor.execQueryInReplica(replicaId,
                    new MySQLCommand(CLSConsts.ENCDB_GENERATE_KMS_KEY_SQL));
            if (!sqlResult.isSuccess()) {
                String msg = String.format("failed to call encdb_generate_kms_master_key(), requestId: %s", requestId);
                log.error(msg);
                throw new ActivityException(msg);
            }
            return Result.returnSuccessResult(true);
        } catch (Exception e) {
            String msg = String.format("callGenerateCLSKeyForReplica encounter exception, requestId: %s, replicaId: %s", requestId, replicaId);
            log.error(msg, e);
            throw new ActivityException(e);
        }

    }

    @ApiOperation(value = "设置全局变量encdb")
    @PostMapping("/prepare_set_loose_encdb_changelog")
    public Result<String> prepareClsStatusChangeLog(@RequestParam(name = "replicaSetName") String replicaSetName,
                                                    @RequestParam(name = "encryptionStatus") Integer encryptionStatus) throws Exception {
        String requestId = RequestSession.getRequestId();
        try {
            ConfigChangeLog c = new ConfigChangeLog();
            c.setIsApplied(false);
            c.setName("loose_encdb");
            if (encryptionStatus == 1) {
                c.setOldValue("OFF");
                c.setNewValue("ON");
            } else {
                c.setOldValue("ON");
                c.setNewValue("OFF");
            }
            ConfigChangeLog commitedChangeLog = dbaasMetaClient.getDefaultmetaApi().commitReplicaSetConfigsChange(requestId, replicaSetName, c);
            return Result.returnSuccessResult(commitedChangeLog.getId().toString());
        } catch (Exception e) {
            String msg = String.format("setCLSStatusForReplica encounter exception, requestId: %s, replicasetName: %s", requestId, replicaSetName);
            log.error(msg, e);
            throw new ActivityException(e);
        }
    }

    @ApiOperation(value = "给实例集刷新TDE启动参数")
    @PostMapping("/set_replica_set_tde_enable")
    public Result<Boolean> setReplicaSetTdeEnable(@RequestParam(name = "replicaSetName") String replicaSetName, @RequestParam(name = "status") String status) throws Exception {
        String requestId = RequestSession.getRequestId();
        try {
            Map<String, String> labels = new HashMap<>();
            labels.put(TdeConsts.TDE_ENABLED, status);
            labels.put(TdeConsts.TDE_ENCRYPTION_KEY_ID, workFlowBaseService.getValueFromContext("kmsKeyId", "").toString());
            labels.put(TdeConsts.TDE_MODE, workFlowBaseService.getValueFromContext("encryptionMode", "").toString());
            dbaasMetaClient.getDefaultmetaApi().updateReplicaSetLabels(requestId, replicaSetName, labels);
            return Result.returnSuccessResult(true);
        } catch (Exception e) {
            String msg = String.format("setReplicaSetTdeEnable encounter exception, requestId: %s, replicaSetName: %s",
                                       requestId, replicaSetName);
            log.error(msg, e);
            throw new ActivityException(e);
        }
    }

    @ApiOperation(value = "设置DB，在主备都配置完成后启用KMS密钥")
    @PostMapping("/enable_cls_kms_mode")
    public Result<Boolean> configCLSKmsMode(@RequestParam(name = "replicaId") Long replicaId,
                                            @RequestParam(name = "replicaSetName") String replicaSetName,
                                            @RequestParam(name = "extraParams") String extraParams) throws Exception {
        String requestId = RequestSession.getRequestId();
        try {
            // 只需要在master配置，slave通过binlog同步
            Map<String, Object> extraParamsMap = JSON.parseObject(extraParams, Map.class);
            String kmsKeyId = extraParamsMap.getOrDefault(CLSConsts.KMS_KEY_ID, "").toString();
            // 设置kms_mode
            SQLResult sqlResult = sqlBaseExecutor.execQueryInReplica(replicaId,
                    new MySQLCommand(CLSConsts.ENCDB_SET_PARAM_SQL(CLSConsts.ENCDB_KMS_MODE, !kmsKeyId.isEmpty() ? "true" : "false")));
            if (!sqlResult.isSuccess()) {
                String msg = String.format("failed to set encdb kms_mode, requestId: %s", requestId);
                log.error(msg);
                throw new ActivityException(msg);
            }
            return Result.returnSuccessResult(true);
        } catch (Exception e) {
            String msg = String.format("configCLSKmsMode encounter exception, requestId: %s, replicaId: %s", requestId, replicaId);
            log.error(msg, e);
            throw new ActivityException(e);
        }

    }

    @ApiOperation(value = "给实例集刷新CLS启动参数")
    @PostMapping("/set_replica_set_cls_key")
    public Result<Boolean> setReplicaSetCLSEnable(@RequestParam(name = "replicaSetName") String replicaSetName, @RequestParam(name = "status") String status) throws Exception {
        String requestId = RequestSession.getRequestId();
        try {
            Map<String, String> labels = new HashMap<>();
            // CLS的其他参数，比如loose_encdb已经在mycnf中记录，这里不用管。算法、白名单模式等在用户数据库里，也不需要管。
            labels.put(CLSConsts.CLS_ENCRYPTION_KEY_ID, workFlowBaseService.getValueFromContext("kmsKeyId", "").toString());
            dbaasMetaClient.getDefaultmetaApi().updateReplicaSetLabels(requestId, replicaSetName, labels);
            return Result.returnSuccessResult(true);
        } catch (Exception e) {
            String msg = String.format("setReplicaSetTCLSEnable encounter exception, requestId: %s, replicaSetName: %s",
                    requestId, replicaSetName);
            log.error(msg, e);
            throw new ActivityException(e);
        }
    }

    @ApiOperation(value = "检查TDE是否开启")
    @PostMapping("/check_tde_enabled")
    public Result<Boolean> checkTdeEnabled(@RequestParam(name = "replicaSetName") String replicaSetName) throws ActivityException {
        String requestId = RequestSession.getRequestId();
        try {
            String tdeEnabled = dbaasMetaClient.getDefaultmetaApi().getReplicaSetLabel(requestId, replicaSetName, TdeConsts.TDE_ENABLED);
            if (tdeEnabled != null && StringUtils.equalsIgnoreCase("1", tdeEnabled)) {
                throw new ActivityException("TDE is enabled, jump to do_release_replica_set_lock");
            }
            return Result.returnSuccessResult(false);
        } catch (Exception e) {
            log.error("tde is already enabled");
            throw new ActivityException(e);
        }
    }

    @ApiOperation(value = "清除CLS相关配置，包括本地文件，runtime变量")
    @PostMapping("/cleanup_cls_configs")
    public Result<Boolean> cleanUpCLSConfigsForReplica(@RequestParam(name = "replicaId") Long replicaId) throws Exception {
        String requestId = RequestSession.getRequestId();
        ReplicaResource replica = dbaasMetaClient.getDefaultmetaApi().getReplica(requestId, replicaId, false);
        String replicaSetName = replica.getReplicaSetName();
        // 检查，如果实例本身配置了列加密KMS相关配置，需要做清理。这个步骤不丢出错误。
        try {
            SQLResult sqlResult = sqlBaseExecutor.execQueryInReplica(replicaId,
                    new MySQLCommand(CLSConsts.ENCDB_EXPORT_PARAM_SQL));
            if (sqlResult.isSuccess()) {
                Map<String, Object> ret = sqlResult.getResultMap().get(0);
                String paramsStr = (String)ret.get("encdb_params");
                JSONObject params = JSONObject.parseObject(paramsStr);
                if ((Boolean)params.get("kms_mode")) {
                    // 清除实例本地文件，以及实例内状态
                    actionJobManager
                            .run(ActionJobParam.builder().resource(CLSAction.RESOURCE).action(CLSAction.CleanConfigs.ACTION)
                                    .replicaSetName(replicaSetName).replicaIdList(Collections.singletonList(replicaId)).build());
                    sqlResult = sqlBaseExecutor.execSQLInReplica(replicaId,
                            new MySQLCommand(CLSConsts.ENCDB_SET_PARAM_DIRCT_SQL(CLSConsts.ENCDB_KMS_MODE, "false")));
                    if (!sqlResult.isSuccess()) {
                        String msg = String.format("failed to set encdb kms_mode to false, requestId: %s", requestId);
                        log.error(msg);
                    }
                }
            }
        } catch (BaseServiceException e) {
            // 忽略错误。列加密本身配置清除这一步出错问题不大，不影响整体搭建节点任务流。打印日志即可。
            String msg = String.format(
                    "setupCLSConfigForReplica encounter BaseServiceException, requestId: %s, replicaId: %d, code: %s, message:%s",
                    requestId, replicaId, e.getCode(), e.getMessage());
            log.error(msg, e);
        } catch (Exception e) {
            String msg = String.format(
                    "setupCLSConfigForReplica encounter Exception, requestId: %s, replicaId: %d, message:%s",
                    requestId, replicaId, e.getMessage());
            log.error(msg, e);
        }
        return Result.returnSuccessResult(true);
    }
}
