package com.aliyun.app.activityprovider.common.consts;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> on 2020/11/25.
 */
public interface ActionJobConsts {

    /**
     * docker_images的字段
     */
    String IMAGE_OP_TYPE = "op_tools";
    String IMAGE_BIZ_TYPE_DEFAULT = "default";
    String IMAGE_BIZ_TYPE_DHG = "dhg";
    String IMAGE_BIZ_TYPE_ARM = "arm";

    String TAG_ALIGROUP_DEFAULT = "default";
    String TAG_ALIGROUP_CLOUD_PFS = "cloud_pfs";
    String TAG_ALIGROUP_CLOUD_PFS_FOR_ARM = "cloud_disk_for_arm";

    String TAG_XCLUSTER_DOCKER_MULTI_W = "xcluster_docker_multi_w";
    String TAG_XCLUSTER_DOCKER_SINGLE_W = "xcluster_docker_single_w";
    String TAG_XCLUSTER_DOCKER_ARM_W = "xcluster_docker_arm_w";
    String TAG_XCLUSTER_POLARX_HATP = "xcluster_docker_polarx";
    String TAG_XCLUSTER_DHG_DOCKER_IMAGE = "xcluster_dhg_docker_image";

    String TAG_ALISQL_CLOUD_SSD = "aliyun_cloud_ssd";
    String TAG_ALISQL_DOCKER_IMAGE = "alisql_docker_image";
    String TAG_ALISQL_AARCH_DOCKER_IMAGE = "alisql_aarch_docker_image";
    String TAG_ALISQL_XC_DOCKER_IMAGE = "alisql_xc_docker_image";
    String TAG_ALISQL_DUCKDB_DOCKER_IMAGE = "alisql_duckdb_docker_image";
    String TAG_ALISQL_DHG_DOCKER_IMAGE = "alisql_dhg_docker_image";
    String TAG_ALISQL_BETA_DOCKER_IMAGE = "alisql_beta_docker_image";
    String TAG_MARIADB_DOCKER_IMAGE = "mariadb_docker_image";

    String TAG_SUBTYPE_XC = "xc";
    String TAG_SUBTYPE_DUCKDB = "duckdb";
    String TAG_SUBTYPE_DEFAULT = "";


    /**
     * service_tag的枚举
     */
    enum ServiceTag {

        MINOR_VERSION_TAG_ALISQL_DOCKER_IMAGE(TAG_ALISQL_DOCKER_IMAGE, IMAGE_BIZ_TYPE_DEFAULT, TAG_SUBTYPE_DEFAULT),
        MINOR_VERSION_TAG_ALISQL_AARCH_DOCKER_IMAGE(TAG_ALISQL_AARCH_DOCKER_IMAGE, IMAGE_BIZ_TYPE_ARM, TAG_SUBTYPE_DEFAULT),
        MINOR_VERSION_TAG_ALISQL_XC_DOCKER_IMAGE(TAG_ALISQL_XC_DOCKER_IMAGE, IMAGE_BIZ_TYPE_DEFAULT, TAG_SUBTYPE_XC),
        MINOR_VERSION_TAG_XCLUSTER_DOCKER_MULTI_W(TAG_XCLUSTER_DOCKER_MULTI_W, IMAGE_BIZ_TYPE_DEFAULT, TAG_SUBTYPE_DEFAULT),
        MINOR_VERSION_TAG_XCLUSTER_DOCKER_SINGLE_W(TAG_XCLUSTER_DOCKER_SINGLE_W, IMAGE_BIZ_TYPE_DEFAULT, TAG_SUBTYPE_DEFAULT),
        MINOR_VERSION_TAG_XCLUSTER_DOCKER_ARM_W(TAG_XCLUSTER_DOCKER_ARM_W, IMAGE_BIZ_TYPE_ARM, TAG_SUBTYPE_DEFAULT),
        MINOR_VERSION_TAG_XCLUSTER_DOCKER_ARM_PFS_SINGLE_W(TAG_ALIGROUP_CLOUD_PFS_FOR_ARM, IMAGE_BIZ_TYPE_ARM, TAG_SUBTYPE_DEFAULT),
        MINOR_VERSION_TAG_ALISQL_BETA_DOCKER_IMAGE(TAG_ALISQL_BETA_DOCKER_IMAGE, IMAGE_BIZ_TYPE_DEFAULT, TAG_SUBTYPE_DEFAULT),
        MINOR_VERSION_TAG_ALISQL_DHG_DOCKER_IMAGE(TAG_ALISQL_DHG_DOCKER_IMAGE, IMAGE_BIZ_TYPE_DHG, TAG_SUBTYPE_DEFAULT),
        MINOR_VERSION_TAG_MARIADB_DOCKER_IMAGE(TAG_MARIADB_DOCKER_IMAGE, IMAGE_BIZ_TYPE_DEFAULT, TAG_SUBTYPE_DEFAULT),
        MINOR_VERSION_TAG_ALISQL_DUCKDB_DOCKER_IMAGE(TAG_ALISQL_DUCKDB_DOCKER_IMAGE, IMAGE_BIZ_TYPE_DEFAULT,TAG_SUBTYPE_DUCKDB),
        MINOR_VERSION_TAG_XCLUSTER_DHG_DOCKER_IMAGE(TAG_XCLUSTER_DHG_DOCKER_IMAGE, IMAGE_BIZ_TYPE_DHG, TAG_SUBTYPE_DEFAULT);


        private final String prefix;
        private final String bizType;
        private final String subType;

        ServiceTag(String prefix, String bizType, String subType) {
            this.prefix = prefix;
            this.bizType = bizType;
            this.subType = subType;
        }


        public String getPrefix() {
            return prefix;
        }

        public String getBizType() {
            return bizType;
        }

        public String getSubType() {
            return subType;
        }

        public static String getSubtypeByServiceSpecTag(String specTag) {
            if (StringUtils.isBlank(specTag)) {
                return null;
            }
            for (ServiceTag tag : ServiceTag.values()) {
                if (specTag.contains(tag.getPrefix())) {
                    return tag.getSubType();
                }
            }
            return null; // 如果未找到匹配的 prefix
        }
    }

}
