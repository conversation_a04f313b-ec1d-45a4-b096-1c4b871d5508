package com.aliyun.app.activityprovider.common.consts;

import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.google.common.collect.Sets;

import java.util.*;

/**
 * Created by f<PERSON><PERSON><PERSON><PERSON> on 19/10/11
 */
public class CommonConsts {
    public static final String APP_NAME = "MysqlActivityProvider";

    public static final String ACCESS_ID_YAOCHI = "YaoChi";

    public static final String CTX_KEY_REPLICA_SET_ENDPOINTS_FORMATTER = "%s-endpoints";

    public static String CALL_TYPE_SYNC = "sync";

    public static String CALL_TYPE_ASYNC = "async";

    public static String POST_METHOD = "POST";

    public static String GET_METHOD = "GET";

    public static String PUT_METHOD = "PUT";

    public static String DELETE_METHOD = "DELETE";

    public static String HTTP_SUCCESS = "success";

    public static String WORKFLOWID_KEY = "workflowId";
    public static String WORKFLOWIDS_KEY = "workflowIds";
    public static String TASK_IDS_KEY = "taskIds";


    // 工作流恢复mode
    public static String TASK_RECOVER_MODE_IMMEDIATE = "immediate";
    public static String TASK_RECOVER_MODE_TIMEPOINT = "timepoint";
    public static String TASK_RECOVER_MODE_MAINTAINTIME = "maintainTime";

    // 工作流状态
    public static String WORKFLOW_STATUS_SCHEDULABLE = "SCHEDULABLE";
    public static String WORKFLOW_STATUS_WAITING = "WAITING";
    public static String WORKFLOW_STATUS_RUNNING = "RUNNING";
    public static String WORKFLOW_STATUS_COMPLETED = "COMPLETED";
    public static String WORKFLOW_STATUS_HUMAN_PROCESSING = "HUMAN_PROCESSING";
    public static String WORKFLOW_STATUS_PAUSE = "PAUSE";
    public static String WORKFLOW_STATUS_TEMP_TERMINATED = "TEMP_TERMINATED";
    public static String WORKFLOW_STATUS_FAILED = "FAILED";

    public static Set<String> INPROCESSING_WORKFLOW_STATUS = Sets.newHashSet(WORKFLOW_STATUS_SCHEDULABLE, WORKFLOW_STATUS_WAITING, WORKFLOW_STATUS_RUNNING, WORKFLOW_STATUS_PAUSE);

    // 任务状态
    public static final String STATUS_INIT = "WAITING";
    public static final String STATUS_INIT_FAILED = "INIT_FAILED";

    public static final String STATUS_ISSUED = "RUNNING";

    public static final String STATUS_SUCCEED = "COMPLETED";

    public static final String STATUS_FAILED = "FAILED";

    public static final String STATUS_HUMAN_PROCESSING = "HUMAN_PROCESSING";

    public static final String STATUS_PAUSE = "PAUSE";

    public static final String STATUS_TEMP_TERMINATED = "TEMP_TERMINATED";

    public static String[] INPROCESSING_TASK_STATUS = {STATUS_INIT, STATUS_ISSUED, STATUS_PAUSE};

    // name service
    public static final Integer NAME_SERVICE_CONNECT_TIMEOUT = 30;
    public static final Integer NAME_SERVICE_READ_TIMEOUT = 30;

    // 名字服务相关
    public static String NAME_SERVICE_TYPE_WORKFLOW_ENGINE = "WORKFLOW_ENGINE";
    public static String NAME_SERVICE_TYPE_WORKFLOW_ENGINE_AGENT = "WORKFLOW_ENGINE_AGENT";

    public static String NAME_SERVICE_TYPE_BIFROST_SERVICE = "BIFROST_SERVICE";
    public static String NAME_SERVICE_TYPE_META_SERVICE = "DBAAS_META_API";
    public static String NAME_SERVICE_TYPE_EVENT_CENTER = "EVENT_CENTER_API";
    public static String NAME_SERVICE_TYPE_COMMON_PROVIDER_SERVICE = "COMMON_ACTIVITY_PROVIDER";
    public static String NAME_SERVICE_TYPE_MYSQL_PROVIDER_SERVICE = "MYSQL_ACTIVITY_PROVIDER";
    public static String NAME_SERVICE_TYPE_VIP_MANAGER_SRV = "VIP_MANAGER_SRV";
    public static String NAME_SERVICE_TYPE_VIP_MAPPING_SERVICE = "VIP_MAPPING_SERVICE";
    public static String NAME_SERVICE_TYPE_AURORA_API = "AURORA_API";
    public static String NAME_SERVICE_TYPE_BACKUP_SERVICE = "BACKUP_API";
    public static String NAME_SERVICE_TYPE_SECGROUP_SERVICE = "SECGROUP_SRV";
    public static String NAME_SERVICE_TYPE_ACK_K8S_SERVICE = "ACK_K8S";
    public static String NAME_SERVICE_TYPE_LAFITE = "LAFITE";
    public static String NAME_SERVICE_TYPE_RM_INVENTORY = "RM_INVENTORY";
    public static String NAME_SERVICE_TYPE_RDSAPI = "RDS_API";
    public static String NAME_SERVICE_TYPE_MAXSCALE_ACTIVITY_PROVIDER_SERVICE_TYPE = "MAXSCALE_ACTIVITY_PROVIDER";
    public static String NAME_SERVICE_TYPE_SMARTBEAT_SERVICE = "SMARTBEAT_EVENTHUB";
    public static String NAME_SERVICE_TYPE_GDN_META_API = "GDN_META_API";
    public static String NAME_SERVICE_TYPE_EIR = "EIR_SERVICE";
    public static String NAME_SERVICE_TYPE_DAS = "DAS_SERVICE";
    public static String NAME_SERVICE_TYPE_ECS = "ECS_SERVICE";
    public static String NAME_SERVICE_TYPE_INNER_API = "INNER_API_SERVICE";
    public static String NAME_SERVICE_TYPE_DBOSS = "DBOSS";
    public static String NAME_SERVICE_TYPE_DBS_GATEWAY = "DBS_GATEWAY";
    public static String NAME_SERVICE_TYPE_DBS_API = "DBS_API";
    /**
     * RunD const
     */
    public static final String MYSQL_MANAGER_CMD = "python /scripts/mysql_manager.py --action %s";
    public static String RUNTIME_TYPE_RUND = "rund";
    public static String RAM_ENI_MODE = "ram_eni_mode";

    public static String ENI_MODE = "common.eni_mode";

    public static String MAINTAIN_THREAD_CHANNEL = "alibaba_rds_maintain_channel";

    public static String ACTIVE_EXTERNAL_REPLICATION = "externalReplication";

    public static String KERNEL_CONTAINER_NAME = "engine";
    public static String FALSE = "false";
    public static String TRUE_STRING = "true";
    public static final String HYPERVISOR_KERNEL_KEY = "io.katacontainers.config.hypervisor.kernel";
    public static final String HYPERVISOR_KERNEL_419 = "images/vmlinux.alikernel.419";
    public static final String HYPERVISOR_KERNEL_510 = "images/vmlinux.alikernel.510";
    public static final String OVERRIDE_RUNTIME_HANDLER_KEY = "alibabacloud.com/override-runtime-handler";
    public static final String OVERRIDE_RUNTIME_HANDLER_RUND_419 = "rund-419";
    public static final String OVERRIDE_RUNTIME_HANDLER_RUND_510 = "rund-510";
    public static final String RUND_CUSTOM_CGROUP_KEY = "securecontainer.alibabacloud.com/custom_cgroups";
    public static final String RUND_CUSTOM_CGROUP_RESOURCE_KEY = "rund_custom_cgroups";

    public static final String RUND_KERNEL_VERSION = "rund_kernel_version";
    public static final String RUND_DEFAULT_KERNEL_VERSION_KEY = "rund_default_kernel_version_key";
    public static final String KERNEL_VERSION_419 = "4.19";
    public static final String KERNEL_VERSION_510 = "5.10";

    public static final String CUSTINS_LABEL_VBM_RESOURCE_TYPE = "custins_resource_type";
    public static final String CUSTINS_LABEL_VBM_RESOURCE_TYPE_VBM = "vbm";
    public static final String VMOC_LITE_POD_ANNO_XDP_KEY = "alibabacloud.com/kernel-params";
    public static final String VMOC_LITE_POD_ANNO_FORCE_XDP_VALUE = "virtio_net.force_xdp=1";

    public static final String INIT_CONTAINER_FOR_WHITELIST = "whiteip";
    public static final String INIT_CONTAINER_FOR_WHITELIST_IMAGE = "reg.docker.alibaba-inc.com/apsaradb_on_ecs/whiteip:ipset1.3_rund";
    public static final String POD_SPEC = "podSpec";
    public static final String METRIC_PORT_KEY = "metric_port";
    public static final String RUNTIME_CHANGE_SUFFIX = "-runtimechange";

    public static final String RUNTIME_CHANGE_SINGLE_TUNNEL_LABEL = "runtime_change_single_tunnel_endpoint";

    public static Integer RESOURCE_EXIST = 409;
    public static Integer RESOURCE_NOT_EXIST = 404;
    public static Integer RANDOM_PASSWD_LENGTH = 15;


    // serivce 类型
    public static String SERVICE_MYSQL = "mysql";
    public static String SERVICE_MARIA = "mariadb";


    /**
     * 网关超时
     * */
    public static int GATEWAY_TIMEOUT = 5 * 60;

    //工具镜像相关
    /**
     * 工具容器默认超时时间，不能超过GATEWAY_TIMEOUT
     * */
    public static int ACTION_JOB_TIMEOUT = GATEWAY_TIMEOUT;

    // 形态相关
    public static final String CHARACTER_TYPE_CS = "cs";
    public static final String CHARACTER_TYPE_DB = "db";
    public static final String CHARACTER_TYPE_NODES = "nodes";
    public static final String CHARACTER_TYPE_NORMAL = "normal";
    public static final String CHARACTER_TYPE_LOGIC = "logic";
    public static final String CHARACTER_TYPE_PHYSICAL = "physical";
    public static final String CHARACTER_TYPE_REDIS_PROXY = "proxy";
    public static final String CHARACTER_TYPE_REDIS_PORT = "port";
    public static final String CHARACTER_TYPE_MONGO_MONGOS = "mongos";
    public static final String CHARACTER_TYPE_MYSQL_MYSQLS = "mysqls";
    public static final String CHARACTER_TYPE_REDIS_CS = CHARACTER_TYPE_CS;
    public static final String CHARACTER_TYPE_REDIS_DB = CHARACTER_TYPE_DB;
    public static final String CHARACTER_TYPE_MYSQL_DB = CHARACTER_TYPE_DB;
    public static final String CHARACTER_TYPE_KEPLER_RC = "rc";
    public static final String CHARACTER_TYPE_KEPLER_WORKER = "worker";
    public static final String CHARACTER_TYPE_MYSQL_SPHINX = "sphinx";
    public static final String CHARACTER_TYPE_GPDB_GROUP = "gpdb_group";
    public static final String CHARACTER_TYPE_ANY = "any";
    /**
     * 数据库存储引擎
     */
    public static final String DB_STORAGE_ENGINE_INNODB = "innodb";
    public static final String DB_STORAGE_ENGINE_XENGINE = "xengine";
    public static final String DB_STORAGE_ENGINE_DUCKDB = "duckdb";

    /**
     * 宿主类型
     */
    public static final String CONTAINER_TYPE_DOCKER = "docker";
    public static final String CONTAINER_TYPE_HOST = "host";
    public static final String CONTAINER_TYPE_ECS = "ecs";
    public static final String CONTAINER_TYPE_DOCKER_ = "docker_on_ecs";

    /**
     * 规格业务类型
     */
    public static final String BASIC_LEVEL = "basic";
    public static final String STANDARD_LEVEL = "standard";
    public static final String SERVERLESS_BASIC_LEVEL = "serverless_basic";
    public static final String SERVERLESS_STANDARD_LEVEL = "serverless_standard";

    public static final String ENTERPRISE_LEVEL = "enterprise";
    public static final String CLUSTER_LEVEL = "cluster";//
    public static final String GENERAL_LEVEL = "general";//主从版

    /**
     * kind code 0:NC 1:VM
     * 00:cgroup on nc
     * 01:cgroup on ecs
     * 10:docker on nc
     * 11:docker on ecs
     */
    public static final Integer KIND_CODE_NC = 0;
    public static final Integer KIND_CODE_ECS_VM = 1;
    public static final Integer KIND_CODE_DOCKER = 2;
    public static final Integer KIND_CODE_DOCKER_ON_NC = 2;
    public static final Integer KIND_CODE_DOCKER_ON_ECS = 3;
    public static final Integer KIND_CODE_DOCKER_ON_POLARSTORE = 6;
    public static final Integer KIND_CODE_DOCKER_ON_ECS_LOCAL_SSD = 10;
    public static final Integer KIND_CODE_K8S = 18;

    public static final Integer CUSTINS_LOCK_NO = 0;
    public static final Integer CUSTINS_LOCK_YES = 1;
    public static final Integer CUSTINS_LOCK_YES_AUTO = 2;
    public static final Integer CUSTINS_LOCK_BEFORE_RECOVER = 3;
    public static final Integer CUSTINS_LOCK_DISK_FULL = 4;
    public static final Integer CUSTINS_LOCK_READINS_DISK_FULL = 5;
    public static final Integer CUSTINS_LOCK_MYSQL_TO_POLAR_DB = 6;
    public static final Integer CUSTINS_LOCK_BY_HOST_LOCK = 7;
    public static final Integer CUSTINS_LOCK_WRITE_BY_USER = 8;
    public static final Integer CUSTINS_LOCK_READ_WRITE_BY_USER = 9;
    //库类型
    public static final String DB_TYPE_MYSQL = "mysql";
    public static final String DB_TYPE_MARIADB = "mariadb";
    public static final String DB_TYPE_MAXSCALE = "maxscale";

    public static final String DB_TYPE_CLICKHOUSE = "clickhouse";

    // rhea-user
    public static final String RHEA_USER_ID = "MYSQL_ACTIVITY_PROVIDER";
    public static final String RHEA_UID = "ALICLOUD";

    public static Map<Integer, ReplicaSet.LockModeEnum> LOCK_MODE_META_ENUM_MAP = new HashMap<Integer, ReplicaSet.LockModeEnum>() {{
        this.put(CUSTINS_LOCK_NO, ReplicaSet.LockModeEnum.NOLOCK);
        this.put(CUSTINS_LOCK_YES, ReplicaSet.LockModeEnum.LOCKMANUAL);
        this.put(CUSTINS_LOCK_YES_AUTO, ReplicaSet.LockModeEnum.EXPIRE);
        this.put(CUSTINS_LOCK_DISK_FULL, ReplicaSet.LockModeEnum.DISKFULL);
        this.put(CUSTINS_LOCK_READINS_DISK_FULL, ReplicaSet.LockModeEnum.READINS_DISKFULL);
        this.put(CUSTINS_LOCK_BY_HOST_LOCK, ReplicaSet.LockModeEnum.HOST_LOCK);
        this.put(CUSTINS_LOCK_MYSQL_TO_POLAR_DB, ReplicaSet.LockModeEnum.MYSQL_TO_POLARDB);
        this.put(CUSTINS_LOCK_WRITE_BY_USER, ReplicaSet.LockModeEnum.USERLOCK_WRITE);
        this.put(CUSTINS_LOCK_READ_WRITE_BY_USER, ReplicaSet.LockModeEnum.USERLOCK_READ_WRITE);
    }};

    // 任务流锁
    public static final String WORKFLOW_LOCK_PARAM_NAME = "WF_LOCK";


    /**
     * 服务降级相关
     */
    public final static String DEGRADE_SERVICE_MARK = "__DEGRADE_SERVICE_MARK__";

    /**
     * 磁盘性能等级对应最小规格（GB）
     * */
    public final static Integer ECS_ClOUD_ESSD0_MIN_SIZE = 10; //PL0
    public final static Integer ECS_ClOUD_ESSD_MIN_SIZE = 20;  //PL1
    public final static Integer ECS_ClOUD_ESSD2_MIN_SIZE = 465; //PL2
    public final static Integer ECS_ClOUD_ESSD3_MIN_SIZE = 1265; //PL3

    /**
     * 磁盘性能等级
     */
    public static final String ECS_CLOUD_PERFORMANCE_ESSD0 = "PL0";
    public static final String ECS_CLOUD_PERFORMANCE_ESSD = "PL1";
    public static final String ECS_CLOUD_PERFORMANCE_ESSD2 = "PL2";
    public static final String ECS_CLOUD_PERFORMANCE_ESSD3 = "PL3";

    /**
     * 磁盘性能等级对应的最小容量(GB)
     */
    public static Map<String, Integer> PL_STORAGE_MAP = new HashMap<String, Integer>() {{
        this.put(ECS_CLOUD_PERFORMANCE_ESSD0, ECS_ClOUD_ESSD0_MIN_SIZE);
        this.put(ECS_CLOUD_PERFORMANCE_ESSD, ECS_ClOUD_ESSD_MIN_SIZE);
        this.put(ECS_CLOUD_PERFORMANCE_ESSD2, ECS_ClOUD_ESSD2_MIN_SIZE);
        this.put(ECS_CLOUD_PERFORMANCE_ESSD3, ECS_ClOUD_ESSD3_MIN_SIZE);
    }};


    /**
     * K8S Pod状态
     * */
    public final static String POD_RUNNING = "Running";
    public final static String POD_STOPPED = "Stopped";
    public final static String POD_CRASH = "CrashLoopBackOff";

    /**
     * blue green deployment
     */
    public final static String BLUEGREEN_DTS_LABEL_SERIES = "rds.white_list";

    public final static String BLUEGREEN_DTS_LABEL_TYPES = "mysql-blue-green-dts-free15d";

    public final static String MYSQL_ACTIVITY_PROVIDER = "mysql-activity-provider";

    // trans list 相关
    public final static Integer TRANSFER_TASK_STATUS_SUCCESS = 12;
    public final static Integer TRANSFER_TASK_STATUS_CANCEL = 99;
    public static Map<String, Integer> TRANSFER_TASK_STATUS_MAP = new HashMap<String, Integer>() {{
        this.put("success", TRANSFER_TASK_STATUS_SUCCESS);
        this.put("cancel", TRANSFER_TASK_STATUS_CANCEL);
    }};

    /**
     * custins_param
     * */
    public final static String LABEL_RCU = "rcu";

    /**
     * CPU架构
     */
    public final static String CPU_ARCH = "arch";


    public final static String NODE_SINGLE_TENANT = "single-tenant";
    public final static String NODE_MULTI_TENANT = "multi-tenant";

    /**
     * 资源模板
     */
    public final static String TEMPLATE_AFFINITY_LABEL_KEY = "appName";
    public final static String RS_TEMPLATE_SYS_PREFIX = "SYS_TEMPLATE_";
    public final static String RS_TEMPLATE_NAME_SYS_ALIYUN_CLOUD_HA = RS_TEMPLATE_SYS_PREFIX + "MYSQL_HIGH_PERF_POOL"; // 云上高可用云盘使用的默认资源模板
    public final static String RS_TEMPLATE_NAME_SYS_ALIYUN_CLOUD_SERVERLESS = RS_TEMPLATE_SYS_PREFIX + "MYSQL_SERVERLESS_POOL"; // 云上SERVERLESS使用的默认资源模板
    public final static String RS_TEMPLATE_ADMIN_USER = "admin";
    public static final String CUSTINS_PARAM_NAME_RESOURCE_SCHEDULE_TEMPLATE_NAME = "rs_schedule_template_name";

    public final static String GET_MYSQL_PROC_CMD = "ps -eo pid,lstart,cmd | grep -v mysqld_safe | grep -v grep |grep mysqld | grep %s";
    public final static String GET_MARIADB_PROC_CMD = "ps -eo pid,lstart,cmd | grep -v mysqld_safe | grep -v grep |grep mariadbd | grep %s";


    public final static String SG_USE_OTHER_CUSTINS = "sg_use_other_custins";

    public final static String LOCK_REASON_DISK_FULL_CAUSED_BY_BIG_TMP_TABLE = "disk_full_caused_by_big_tmp_table";

    public final static String ALIGROUP = "aligroup";

    /**
     * io 加速相关参数
     */
    public static final String IO_ACCELERATION_ENABLED_ON = "1";

    public static final String IO_ACCELERATION_ENABLED_OFF = "0";

    public static final String IO_ACCELERATION_ENABLED = "ioAccelerationEnabled";

    /**
     * 分析型只读相关参数
     */
    public static final String ANALYTIC_READ_ONLY_INS_FLAG = "isAnalyticReadOnlyIns";

    /**
     * optimized writes
     */
    public static final String OPTIMIZED_WRITES_INFO = "optimized_writes_info";
    public static final String OPTIMIZED_WRITES = "optimized_writes";
    public static final String INIT_OPTIMIZED_WRITES = "init_optimized_writes";
    public static String CLASS_CODE_ENABLE_SERVERLESS = "mysql.n2.serverless.xc";

    public static final String SERVERLESS_ENABLE = "serverless_enable";

    /**
     * 内部实例标识
     */
    public static final String IS_INTERNAL = "is_internal";
    /**
     * 文件系统类型
     */
    public static final String FILE_SYSTEM_EXT4 = "ext4";
    public static final String FILE_SYSTEM_PFS = "cloud_pfs";

    public static String SERVERLESS_CLUSTER_CLASS_CODE = "mysql.n2.serverless.xc";

    public static final String SERVERLESS_ENABLED = "serverless_enabled";

    public static final Set<String> instanceTypeFamilyListForEightGeneration = Sets.newHashSet("ecs.g8i", "ecs.r8i", "ecs.c8i", "ecs.g8a", "ecs.r8a", "ecs.c8a", "ecs.g8ae", "ecs.r8ae", "ecs.c8ae");
    public static String FREE_TIME_LABEL = "free_time";
    public static String ENABLE_DAS_PRO = "enable_das_pro";
    public static String ENABLE_SQL_LOG = "enable_sql_log";
    /**
     * 开启时间
     */
    public static String SQL_VISIBLE_TIME = "sql_visible_time";
    /**
     * SQL存放介质？
     */
    public static String SQL_RECORD_SOURCE = "sql_record_source";
    public static String SQL_LOG_VERSION = "sql_log_version";
    public static String SQL_RETENTION = "sql_retention";
    public static String SQL_RETENTION_WRITE = "sql_retention_write";
    public static String SQL_RETENTION_READ = "sql_retention_read";


    public enum LogTaskStatus {
        INIT, RUNNING, SUCCESS, FAILED
    }

    /**
     * read only status
     */
    public enum readOnlyStatus {
        // 实例只读态打开
        ON,
        // 实例只读态关闭 实例正常读写
        OFF
    }

    public static final String READ_ONLY_STATUS = "read_only_status";
}
