package com.aliyun.app.activityprovider.common.consts;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

import static com.aliyun.app.activityprovider.common.consts.CommonConsts.*;

/**
 * MySQL相关常量
 *
 * <AUTHOR> on 2020/4/28.
 */
public interface MysqlConsts {
    /**
     * 版本相关
     */
    String MYSQL_VERSION_55 = "5.5";
    String MYSQL_VERSION_56 = "5.6";
    String MYSQL_VERSION_57 = "5.7";
    String MYSQL_VERSION_80 = "8.0";
    String MARIADB_VERSION_103 = "10.3";
    String MARIADB_VERSION_106 = "10.6";

    /**
     * XDB相关
     */
    int XDB_XPAXOS_PORT_OFFSET = 8000;  //XDB的端口偏移量
    int XDB_WEIGHT_LEADER_FOLLOWER = 9;
    int XDB_WEIGHT_DEFAULT = 5;
    int XDB_WEIGHT_LOGGER = 1;
    int XDB_WEIGHT_MIN = 0;
    int XKV_PORT_STEP = 10000;
    int POLARX_PORT_STEP = 28000;


    /**
     * Slave相关
     */
    int TOLERATE_DELAY_SECONDS = 5; //允许容忍的最大事务数
    int XDB_TOLERATE_DELAY_TRANSACTION = 10000; //允许容忍的最大事务数
    int XDB_NORMAL_DELAY_TRANSACTION = 5000; //正常容忍的事务数


    /**
     * 目录相关
     */
    public static final String BASE_DIR = "/home/<USER>";
    public static final String DATA_BASE_DIR = BASE_DIR + "/data";
    public static final String LOG_BASE_DIR = BASE_DIR + "/log";
    public static final String DATA_DIR = DATA_BASE_DIR + "/mysql";
    public static final String MASTER_ERROR_LOG_PATH = LOG_BASE_DIR + "/mysql/master-error.log";
    public static final String INS_DATA_PATH = DATA_BASE_DIR + "/dbs";
    public static final String INS_DEVICE_PATH = "/dev/vda";
    public static final String DATA_DOWNLOAD_DIR = DATA_BASE_DIR + "/tmpbackup";
    public static final String BINLOG_BASE_DIR = LOG_BASE_DIR + "/mysql";
    public static final String BACKUP_NC_LOG = DATA_BASE_DIR + "/tmplog";
    public static final String XDB_LOG_DIR = LOG_BASE_DIR + "/mysql";
    public static final String XDB_LOG_TMP_DIR = LOG_BASE_DIR + "/tmp";
    public static final String SHUTDOWN_FILE_PATH = DATA_BASE_DIR + "/shutdown.sh";
    public static final String DOWNLOAD_BASE_DIR = "/home/<USER>/download";
    public static final String APPLY_RELAY_FILE_PATH = LOG_BASE_DIR + "/apply_relay.log";
    String SOCK_FILE_PATH = DATA_BASE_DIR + "/tmp/mysql.sock";
    String SOCK_FILE_LOCK_PATH = DATA_BASE_DIR + "/tmp/mysql.sock.lock";
    public static final String BACUPSET_LOG = DATA_BASE_DIR + "/mysql";
    public static final String CLONE_BINLOG_INFO_FILE = LOG_BASE_DIR + "/clone_binlog_info.json";
    public static final String DOWNLOAD_BINLOG_LOG_FILE = LOG_BASE_DIR + "/download_binlog.log";
    public static final String APPLY_RELAY_LOG_FILE = LOG_BASE_DIR + "/apply_relay.log";
    public static final String RETRY_DOWNLOAD_LOG_FILE = LOG_BASE_DIR + "/retry_download_binlg.log";
    public static final String PID_FILE = DATA_BASE_DIR + "/my%s.pid";
    public static final String BACKUP_DIR = DATA_BASE_DIR + "/backup";
    public static final String TDE_DIR = DATA_BASE_DIR + "/tde";
    public static final String KMS_AGENT_DIR = DATA_BASE_DIR + "/kms_agent";



    /**
     * 内核相关
     */
    String ALISQL_PACKAGE_BASE_DIR = "/u01/mysql";
    String ALISQL_BIN_PATH = "/u01/mysql/bin/mysqld";
    String ALISQL_UPGRADE_BIN_PATH = "/u01/mysql/bin/mysql_upgrade";
    String ALISQL_ADMIN_BIN_PATH = "/u01/mysql/bin/mysqladmin";
    String ALISQL_DUMP_BIN_PATH = "/u01/mysql/bin/mysqldump";
    String ALISQL_MYSQL_BIN_PATH = "/u01/mysql/bin/mysql";
    String ALISQL_CLIENT_PATH = "/u01/mysql/bin/mysql";
    String ALISQL_TZINFO_TO_SQL_PATH = "/u01/mysql/bin/mysql_tzinfo_to_sql";

    String MARIADB_PACKAGE_BASE_DIR = "/u01/mariadb";
    String MARIADB_BIN_PATH = "/u01/mariadb/bin/mysqld";
    String MARIADB_INSTALL_DB_PATH = "/u01/mariadb/scripts/mysql_install_db";
    String MARIADB_MYSQL_BIN_PATH = "/u01/mariadb/bin/mysql";
    String MARIADB_CLIENT_PATH = "/u01/mariadb/bin/mysql";
    String MARIADB_TZINFO_TO_SQL_PATH = "/u01/mariadb/bin/mysql_tzinfo_to_sql";
    String MARIADB_UPGRADE_BIN_PATH = "/u01/mariadb/bin/mysql_upgrade";

    String XCLUSTER_BIN_PATH = "/u01/xcluster/bin/mysqld";
    String XCLUSTER_TZINFO_TO_SQL_PATH = "/u01/xcluster/bin/mysql_tzinfo_to_sql";
    String XCLUSTER_CLIENT_PATH = "/u01/xcluster/bin/mysql";
    String XCLUSTER_UPGRADE_BIN_PATH = "/u01/xcluster/bin/mysql_upgrade";

    String SYSTEM_TIMEZONE_PATH = "/usr/share/zoneinfo";
    String MYSQL_CONFIG_PATH = "/home/<USER>/data/my.cnf";
    String MARIADB_CONFIG_PATH = "/home/<USER>/data/maria.cnf";

    /**
     * oss备份集相关
     */
    public static final String RDS_MYSQL_DUMP_SQL= DATA_BASE_DIR + "/rds-mysql-data.sql";
    public static final String SRC_MYSQL_DUMP_SQL = DATA_BASE_DIR + "/src-mysql-data.sql";
    public static final String RDS_MYSQL_DROP_SQL = DATA_BASE_DIR + "/rds-mysql-drop.sql";
    public static final String RDS_ANALYZE_TBL_SQL = DATA_BASE_DIR + "/rds-analyze-table.sql";


    /**
     * 数据增量
     */
    public static final String RELAY_LOG_PATTERN = BINLOG_BASE_DIR + "/slave-relay.[0-9]*";
    public static final String RELAY_LOG_INDEX = BINLOG_BASE_DIR + "/slave-relay-log.index";


    Pattern patternStatModifyTime = Pattern.compile("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}");

    /**
     * 参数相关
     */
    public static final String LOWER_CASE_TABLE_NAMES = "lower_case_table_names";
    public static final String LOOSE_VALIDATE_PASSWORD_LENGTH = "loose_validate_password_length";
    public static final String DEFAULT_TIME_ZONE = "default_time_zone";
    public static final String INNODB_LARGE_PREFIX = "innodb_large_prefix";
    public static final String RPL_SEMI_SYNC_SLAVE_ENABLED = "rpl_semi_sync_slave_enabled";
    public static final String CLOUD_BOX = "cloudbox";
    public static final String INIT_TIMEZONE_SQL_PATH = "/home/<USER>/init_timezone.sql";

    /**
     * Pengine迁移新架构数据目录正则
     */
    public static final String REGEX_MIGRATE_BACKUPSET_DIR_NAME = "^[0-9]*[0-9]$";

    /**
     * 参数值
     * */
    public static final String PARAM_ON = "ON";
    public static final String PARAM_OFF = "OFF";

    /*
     * 云盘加密相关
     * */
    public final static String ENCRYPTION_CLOUD_DISK_TYPE = "CloudDisk";
    public static String ENCRYPTION_TYPE_LABEL = "encryption_type";
    public static String ENCRYPTION_KEY_LABEL = "encryption_key";


    /**
     * aliyun_root加密标识
     */
    String ROOT_ENCRYPT_PWD = "root_encrypt_pwd";

    /**
     * 任务流相关
     */
    public static String STOP_INS_STAGE_1 = "stop_ins_stage_1";
    public static String STOP_INS_STAGE_2 = "stop_ins_stage_2";
    public static String START_INS_FROM_STOP_STAGE_1 = "start_ins_from_stop_stage_1";
    public static String START_INS_FROM_STOP_STAGE_2 = "start_ins_from_stop_stage_2";

    /**
     * IO加速 相关
     **/
    public static final String MYSQL_BPE_INNODB_BUFFER_POOL_EXTENSION = "loose_innodb_buffer_pool_extension";
    public static final String MYSQL_BPE_INNODB_BUFFER_POOL_EXTENSION_DIR = "loose_innodb_buffer_pool_extension_dir";
    public static final String MYSQL_BPE_INNODB_BUFFER_POOL_EXTENSION_SIZE = "loose_innodb_buffer_pool_extension_size";
    public static final String MYSQL_BPE_INNODB_BUFFER_POOL_EXTENSION_FLUSH_DIRTY = "loose_innodb_buffer_pool_extension_flush_dirty";
    public static final String MYSQL_BPE_INNODB_BUFFER_POOL_EXTENSION_IO_READ_CAPACITY = "loose_innodb_buffer_pool_extension_io_read_capacity";
    public static final String MYSQL_BPE_INNODB_BUFFER_POOL_EXTENSION_IO_WRITE_CAPACITY = "loose_innodb_buffer_pool_extension_io_write_capacity";
    public static final String FLUSH_DIRTY_PAGE_TO_BUFFER_POOL_EXTENSION = "ON";
    public static final String  TURN_ON_BUFFER_POOL_EXTENSION = "ON";
    public static final String  TURN_OFF_BUFFER_POOL_EXTENSION = "OFF";
    public static final String  BUFFER_POOL_EXTENSION_DIR = "/home/<USER>";

    /**
     * 分析型只读 相关
     **/
    public static final String MYSQL_DUCKDB_MODE = "loose_duckdb_mode";
    public static final String MYSQL_DUCKDB_MEMORY_LIMIT = "loose_duckdb_memory_limit";
    public static final String MYSQL_DUCKDB_TEMP_DIRECTORY = "loose_duckdb_temp_directory";
    public static final String MYSQL_DUCKDB_MAX_TEMP_DIRECTORY_SIZE = "loose_duckdb_max_temp_directory_size";
    public static final String MYSQL_DUCKDB_THREADS = "loose_duckdb_threads";
    public static final String MYSQL_DUCKDB_USE_DIRECT_IO = "loose_duckdb_use_direct_io";
    public static final String MYSQL_DUCKDB_FORCE_INNODB_TO_DUCKDB = "loose_force_innodb_to_duckdb";
    public static final String MYSQL_DUCKDB_CONVERT_ALL_AT_STARTUP = "loose_duckdb_convert_all_at_startup";
    public static final String MYSQL_DUCKDB_CONVERT_ALL_AT_STARTUP_IGNORE_ERROR = "loose_duckdb_convert_all_at_startup_ignore_error";
    public static final String MYSQL_DUCKDB_CONVERT_ALL_AT_STARTUP_THREADS = "loose_duckdb_convert_all_at_startup_threads";
    public static final String MYSQL_DUCKDB_COPY_DDL_IN_BATCH = "loose_duckdb_copy_ddl_in_batch";
    public static final String MYSQL_DUCKDB_DML_IN_BATCH = "loose_duckdb_dml_in_batch";
    public static final String MYSQL_DUCKDB_REQUIRE_PRIMARY_KEY = "loose_duckdb_require_primary_key";
    public static final String MYSQL_DUCKDB_UPDATE_MODIFIED_COLUMN_ONLY = "loose_duckdb_update_modified_column_only";

    public static final String DUCKDB_MODE_DEFAULT_VALUE = "ON";
    public static final String DUCKDB_DUCKDB_TEMP_DIRECTORY = "/home/<USER>/log/tmp";
    public static final String DUCKDB_MAX_TEMP_DIRECTORY_SIZE_DEFAULT_VALUE = "0";
    public static final String DUCKDB_THREADS_DEFAULT_VALUE = "0";
    public static final String DUCKDB_USE_DIRECT_IO_DEFAULT_VALUE = "OFF";
    public static final String FORCE_INNODB_TO_DUCKDB_DEFAULT_VALUE = "ON";
    public static final String DUCKDB_CONVERT_ALL_AT_STARTUP_DEFAULT_VALUE = "ON";
    public static final String DUCKDB_CONVERT_ALL_AT_STARTUP_IGNORE_ERROR_DEFAULT_VALUE = "OFF";
    public static final String DUCKDB_COPY_DDL_IN_BATCH_DEFAULT_VALUE = "ON";
    public static final String DUCKDB_DML_IN_BATCH_DEFAULT_VALUE = "OFF";
    public static final String DUCKDB_REQUIRE_PRIMARY_KEY_DEFAULT_VALUE = "ON";
    public static final String DUCKDB_UPDATE_MODIFIED_COLUMN_ONLY_DEFAULT_VALUE = "ON";




    /**
     * 写优化相关
     **/
    public static final String LABEL_PARAM_OPTIMIZED_WRITES_INFO = "optimized_writes_info";
    public static final String LABEL_OF_INIT_OPTIMIZED_WRITES = "init_optimized_writes";
    public static final String LABEL_OF_OPTIMIZED_WRITES = "optimized_writes";

    /**
     * 弹性临时盘特性
     * 每GB 最大 读吞吐 1.6MB/s
     * 每GB 最大 写吞吐 1MB/s
     * 一次IO的单位是16kb
     **/
    public static final double IO_READ_PER_GB = 1.6 * 1024L / 16;
    public static final long IO_WRITE_PER_GB = 1024L / 16;


    public static final String MYSQL_OSS_ENABLED = "loose_oss_enabled";
    public static final String MYSQL_OSS_ENDPOINT = "loose_oss_endpoint";
    public static final String MYSQL_OSS_BUCKET_NAME = "loose_oss_bucket_name";
    public static final String MYSQL_OSS_DIRECTORY = "loose_oss_directory";
    public static final String MYSQL_OSS_ACCESS_KEY_ID = "loose_oss_access_key_id";
    public static final String MYSQL_OSS_ACCESS_KEY_SECRET = "loose_oss_access_key_secret";
    public static final String MYSQL_OSS_TOKEN = "loose_oss_token";

    public static final String MYSQL_OSS_COLD_DATA_SIZE_KEY = "coldDataSize";
    public static final Long MYSQL_OSS_MIN_COLD_DATA_SIZE = 0L;

    public static final Map<String, Long> MAX_MBPS_FOR_DISK = new HashMap<String, Long>() {{
        this.put(ECS_CLOUD_PERFORMANCE_ESSD0, 180L);
        this.put(ECS_CLOUD_PERFORMANCE_ESSD, 350L);
        this.put(ECS_CLOUD_PERFORMANCE_ESSD2, 750L);
        this.put(ECS_CLOUD_PERFORMANCE_ESSD3, 4000L);
    }};
}
