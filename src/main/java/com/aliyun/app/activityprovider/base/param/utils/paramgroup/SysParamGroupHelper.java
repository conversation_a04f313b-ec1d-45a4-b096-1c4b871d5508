package com.aliyun.app.activityprovider.base.param.utils.paramgroup;

import com.aliyun.app.activityprovider.common.exception.ActivityException;
import com.google.common.collect.HashBiMap;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static com.aliyun.app.activityprovider.common.consts.CommonConsts.*;
import static com.aliyun.app.activityprovider.common.consts.ErrorCode.*;
import static com.aliyun.app.activityprovider.common.consts.MysqlConsts.*;

/**
 * 系统参数模板辅助类，用于编码、解码系统参数模板
 *
 * @blame 宇一
 */
@Component
public class SysParamGroupHelper {
    public static final String SYS_PARAM_GROUP_ID_PREFIX = "rpg-sys-";
    public static final Integer SYS_PARAM_GROUP_ID_CODE_UNIT_LENGTH = 2;
    public static final String CODE_KEY_ANY = "any";
    public static final Integer CODE_VALUE_ANY = 0;

    /**
     * 性能模板
     */
    public static final String PARAM_GROUP_PERFORMANCE_NORMAL = "normal";
    public static final String PARAM_GROUP_PERFORMANCE_HIGH = "high";
    public static final String PARAM_GROUP_PERFORMANCE_SAFE = "safe";
    private static final String PARAM_GROUP_PERFORMANCE_MGR = "mgr"; // mgr模板

    /**
     * 系统参数模板 param_group_id 支持含义定义
     */
    public static final String KEY_DB_TYPE = "db_type";
    public static final String KEY_DB_VERSION = "db_version";
    public static final String KEY_CHARACTER_TYPE = "character_type";
    public static final String KEY_CATEGORY = "category";
    public static final String KEY_DB_STORAGE_ENGINE = "db_storage_engine";
    public static final String KEY_PERFORMANCE_MODE = "performance_mode";
    public static final String KEY_KIND_CODE = "kind_code";

    /**
     * 系统参数模板 param_group_id 编码/解码 顺序定义
     */
    public static final String[] PARAM_GROUP_CODE_DEFINE = {
            KEY_DB_TYPE,
            KEY_DB_VERSION,
            KEY_CHARACTER_TYPE,
            KEY_CATEGORY,
            KEY_DB_STORAGE_ENGINE,
            KEY_PERFORMANCE_MODE,
            KEY_KIND_CODE,
    };

    /**
     * 系统参数模板 param_group_id 编码/解码映射
     */
    private static final HashBiMap<String, Integer> MAP_DB_TYPE = HashBiMap.create();
    private static final HashBiMap<String, Integer> MAP_DB_VERSION = HashBiMap.create();
    private static final HashBiMap<String, Integer> MAP_CHARACTER_TYPE = HashBiMap.create();
    private static final HashBiMap<String, Integer> MAP_CATEGORY = HashBiMap.create();
    private static final HashBiMap<String, Integer> MAP_DB_STORAGE_ENGINE = HashBiMap.create();
    private static final HashBiMap<String, Integer> MAP_PERFORMANCE_MODE = HashBiMap.create();
    private static final HashBiMap<String, Integer> MAP_KIND_CODE = HashBiMap.create();

    /**
     * 系统参数模板 param_group_id 编码/解码 顺序定义
     */
    private static final Map<String, HashBiMap> PARAM_GROUP_CODE_MAPPING = new HashMap<String, HashBiMap>() {{
        // 数据库类型
        put(KEY_DB_TYPE, MAP_DB_TYPE);
        // 数据库版本
        put(KEY_DB_VERSION, MAP_DB_VERSION);
        // 角色类型
        put(KEY_CHARACTER_TYPE, MAP_CHARACTER_TYPE);
        // RDS规格
        put(KEY_CATEGORY, MAP_CATEGORY);
        // 存储引擎
        put(KEY_DB_STORAGE_ENGINE, MAP_DB_STORAGE_ENGINE);
        // 参数模板性能模式
        put(KEY_PERFORMANCE_MODE, MAP_PERFORMANCE_MODE);
        // RDS形态
        put(KEY_KIND_CODE, MAP_KIND_CODE);
    }};


    static {
        // Database type register
        MAP_DB_TYPE.put(DB_TYPE_MYSQL, 1);
        MAP_DB_TYPE.put(DB_TYPE_MARIADB, 2);

        // Database version register
        MAP_DB_VERSION.put(MYSQL_VERSION_55, 1);
        MAP_DB_VERSION.put(MYSQL_VERSION_56, 2);
        MAP_DB_VERSION.put(MYSQL_VERSION_57, 3);
        MAP_DB_VERSION.put(MYSQL_VERSION_80, 4);

        // Character type register
        MAP_CHARACTER_TYPE.put(CODE_KEY_ANY, CODE_VALUE_ANY);
        MAP_CHARACTER_TYPE.put(CHARACTER_TYPE_CS, 1);
        MAP_CHARACTER_TYPE.put(CHARACTER_TYPE_DB, 2);
        MAP_CHARACTER_TYPE.put(CHARACTER_TYPE_NODES, 3);
        MAP_CHARACTER_TYPE.put(CHARACTER_TYPE_NORMAL, 4);
        MAP_CHARACTER_TYPE.put(CHARACTER_TYPE_LOGIC, 5);
        MAP_CHARACTER_TYPE.put(CHARACTER_TYPE_PHYSICAL, 6);
        MAP_CHARACTER_TYPE.put(CHARACTER_TYPE_REDIS_PROXY, 7);
        MAP_CHARACTER_TYPE.put(CHARACTER_TYPE_REDIS_PORT, 8);
        MAP_CHARACTER_TYPE.put(CHARACTER_TYPE_MONGO_MONGOS, 9);
        MAP_CHARACTER_TYPE.put(CHARACTER_TYPE_MYSQL_MYSQLS, 10);
        MAP_CHARACTER_TYPE.put(CHARACTER_TYPE_KEPLER_RC, 11);
        MAP_CHARACTER_TYPE.put(CHARACTER_TYPE_KEPLER_WORKER, 12);
        MAP_CHARACTER_TYPE.put(CHARACTER_TYPE_MYSQL_SPHINX, 13);
        MAP_CHARACTER_TYPE.put(CHARACTER_TYPE_GPDB_GROUP, 14);

        // Character register
        MAP_CATEGORY.put(BASIC_LEVEL, 1);
        MAP_CATEGORY.put(STANDARD_LEVEL, 2);
        MAP_CATEGORY.put(ENTERPRISE_LEVEL, 3);
        MAP_CATEGORY.put(GENERAL_LEVEL, 6);
        MAP_CATEGORY.put(CLUSTER_LEVEL, 7);

        // Database storage engine register
        MAP_DB_STORAGE_ENGINE.put(CODE_KEY_ANY, CODE_VALUE_ANY);
        MAP_DB_STORAGE_ENGINE.put(DB_STORAGE_ENGINE_INNODB, 1);
        MAP_DB_STORAGE_ENGINE.put(DB_STORAGE_ENGINE_XENGINE, 2);
        MAP_DB_STORAGE_ENGINE.put(DB_STORAGE_ENGINE_DUCKDB, 3);

        // Parameter group template performance mode register
        MAP_PERFORMANCE_MODE.put(PARAM_GROUP_PERFORMANCE_NORMAL, CODE_VALUE_ANY);
        MAP_PERFORMANCE_MODE.put(PARAM_GROUP_PERFORMANCE_SAFE, 1);
        MAP_PERFORMANCE_MODE.put(PARAM_GROUP_PERFORMANCE_HIGH, 2);
        MAP_PERFORMANCE_MODE.put(PARAM_GROUP_PERFORMANCE_MGR, 4);

        // Kind code register
        MAP_KIND_CODE.put(CODE_KEY_ANY, CODE_VALUE_ANY);
        MAP_KIND_CODE.put(KIND_CODE_DOCKER.toString(), 1);
        MAP_KIND_CODE.put(KIND_CODE_NC.toString(), 2);
        MAP_KIND_CODE.put(KIND_CODE_ECS_VM.toString(), 3);
        MAP_KIND_CODE.put(KIND_CODE_DOCKER.toString(), 4);
        MAP_KIND_CODE.put(KIND_CODE_DOCKER_ON_NC.toString(), 5);
        MAP_KIND_CODE.put(KIND_CODE_DOCKER_ON_ECS.toString(), 6);
        MAP_KIND_CODE.put(KIND_CODE_DOCKER_ON_POLARSTORE.toString(), 7);
        MAP_KIND_CODE.put(KIND_CODE_DOCKER_ON_ECS_LOCAL_SSD.toString(), 8);
    }

    /**
     * 通过options描述获取对应的system parameter group id
     *
     * @param options: 包含db_type, db_version, category, character_type 等信息的map
     * @return system param group id
     */
    public static String getSysParamGroupId(Map<String, String> options) throws ActivityException {
        StringBuilder paramGroupId = new StringBuilder();
        paramGroupId.append(SYS_PARAM_GROUP_ID_PREFIX);

        for (String option : PARAM_GROUP_CODE_DEFINE) {
            Integer code = 0;
            String value = options.get(option);
            HashBiMap mapping = PARAM_GROUP_CODE_MAPPING.get(option);

            // 防止新增类型未注册
            if (mapping == null) {
                throw new ActivityException(SYS_PARAMETER_GROUP_OPTION_KEY_NOT_DEFINED);
            }

            // 如果映射中不包含0值，则说明该值不可为空。
            if (StringUtils.isEmpty(value) && StringUtils.isEmpty((String) mapping.inverse().get(CODE_VALUE_ANY))) {
                throw new ActivityException(SYS_PARAMETER_GROUP_OPTION_VALUE_NOT_DEFINED);
            }

            // 防止新增值未注册
            if (StringUtils.isNotEmpty(value)) {
                code = (Integer) mapping.get(value);
                if (code == null) {
                    throw new ActivityException(SYS_PARAMETER_GROUP_OPTION_NOT_SUPPORT);
                }
            }

            paramGroupId.append(ConversionUtil.encode(code, SYS_PARAM_GROUP_ID_CODE_UNIT_LENGTH));
        }
        return paramGroupId.toString();
    }


    /**
     * 解析系统参数模板ID，返回该ID对应的信息，ID长度向后兼容
     *
     * @param paramGroupId 系统参数模板ID
     * @return 通过系统参数模板解析出的详细信息。
     */
    public static Map<String, String> describeSysParamGroupId(String paramGroupId) throws ActivityException {
        Map<String, String> details = new HashMap<>(4);
        if (!StringUtils.startsWith(paramGroupId, SYS_PARAM_GROUP_ID_PREFIX)) {
            return details;
        }

        paramGroupId = StringUtils.remove(paramGroupId, SYS_PARAM_GROUP_ID_PREFIX);
        if (StringUtils.isBlank(paramGroupId)) {
            return details;
        }

        int code;
        int length = paramGroupId.length();
        int start = 0;
        int end = start + SYS_PARAM_GROUP_ID_CODE_UNIT_LENGTH;
        String codeStr;

        for (String option : PARAM_GROUP_CODE_DEFINE) {
            if (end > length || start >= length) {
                break;
            }

            HashBiMap mapping = PARAM_GROUP_CODE_MAPPING.get(option);
            // 防止新增类型未注册
            if (mapping == null) {
                throw new ActivityException(SYS_PARAMETER_GROUP_OPTION_KEY_NOT_DEFINED);
            }

            codeStr = StringUtils.substring(paramGroupId, start, end);
            if (StringUtils.isEmpty(codeStr)) {
                break;
            }

            // code < 0 说明ID中含有非法数值
            code = (int) ConversionUtil.decode(codeStr);
            if (code < 0) {
                throw new ActivityException(INVALID_PARAMETER_GROUP_ID);
            }

            // 防止新增值未注册
            String info = (String) mapping.inverse().get(code);
            if (StringUtils.isEmpty(info)) {
                throw new ActivityException(SYS_PARAMETER_GROUP_OPTION_VALUE_NOT_DEFINED);
            }

            start = end;
            end += SYS_PARAM_GROUP_ID_CODE_UNIT_LENGTH;
            details.put(option, info);
        }
        return details;
    }

    public static String getDBType(String paramGroupId) throws ActivityException {
        Map<String, String> detail = describeSysParamGroupId(paramGroupId);
        return getDetailValue(detail, KEY_DB_TYPE);
    }

    public static String getDBVersion(String paramGroupId) throws ActivityException {
        Map<String, String> detail = describeSysParamGroupId(paramGroupId);
        return getDetailValue(detail, KEY_DB_VERSION);
    }

    public static String getCharacterType(String paramGroupId) throws ActivityException {
        Map<String, String> detail = describeSysParamGroupId(paramGroupId);
        return getDetailValue(detail, KEY_CHARACTER_TYPE);
    }

    public static String getCategory(String paramGroupId) throws ActivityException {
        Map<String, String> detail = describeSysParamGroupId(paramGroupId);
        return getDetailValue(detail, KEY_CATEGORY);
    }

    public static String getDBStorageEngine(String paramGroupId) throws ActivityException {
        Map<String, String> detail = describeSysParamGroupId(paramGroupId);
        return getDetailValue(detail, KEY_DB_STORAGE_ENGINE);
    }

    public static String getPerformanceMode(String paramGroupId) throws ActivityException {
        Map<String, String> detail = describeSysParamGroupId(paramGroupId);
        return getDetailValue(detail, KEY_PERFORMANCE_MODE);
    }

    /**
     * sync_mode 用于控制复制是异步或半同步，Aurora会监控这个配置项来调整实例的复制配置；
     * sync_mode = 0：异步复制；
     * sync_mode = 1：半同步复制；
     * sync_mode = 2（XDB）：强同步复制；
     * sync_mode = 4 MGR；
     * 默认模板:   rpg-sys-01040402010100, performance_mode=safe, 主备双1 & 半同步复制
     * 异步模板:   rpg-sys-01040402010000, performance_mode=normal, 主库双1 & 备库innodb_flush_log_at_trx_commit=2 & sync_binlog=1000 & 异步复制
     * 高性能模板: rpg-sys-01040402010200, performance_mode=high, 主备innodb_flush_log_at_trx_commit=2 & sync_binlog=1000 & 异步复制
     * MGR模板：  rpg-sys-01040407010100, performance_mode=mgr, 主备双1 & MGR
     * */
    public static Integer getSyncMode(String paramGroupId) throws ActivityException {
        // https://yuque.antfin-inc.com/docs/share/619fe517-5021-41ec-b560-c111e64f2acb#vgl2gi
        String performanceMode = getPerformanceMode(paramGroupId);
        if (PARAM_GROUP_PERFORMANCE_SAFE.equals(performanceMode)) {
            return 1;
        } else if (PARAM_GROUP_PERFORMANCE_MGR.equals(performanceMode)) {
            return 4;
        } else {
            return 0;
        }
    }

    /**
     * sync_binlog_mode 用于控制事务提交时的刷盘策略，Aurora会监控这个配置项来调整实例的innodb_flush_log_at_trx_commit & sync_binlog；
     * sync_binlog_mode = 0 ： 主备双1
     * sync_binlog_mode = 1 ： 主库双1，备库innodb_flush_log_at_trx_commit=2 & sync_binlog=1000
     * sync_binlog_mode = 2 ： 主备innodb_flush_log_at_trx_commit=2 & sync_binlog=1000
     * */
    public static Integer getSyncBinlogMode(String paramGroupId) throws ActivityException {
        // https://yuque.antfin-inc.com/docs/share/619fe517-5021-41ec-b560-c111e64f2acb?#vgl2gi
        String performanceMode = getPerformanceMode(paramGroupId);
        if (PARAM_GROUP_PERFORMANCE_SAFE.equals(performanceMode)) {
            return 0;
        } else {
            return PARAM_GROUP_PERFORMANCE_HIGH.equals(performanceMode) ? 2 : 1;
        }
    }

    public String getKindCode(String paramGroupId) throws ActivityException {
        Map<String, String> detail = describeSysParamGroupId(paramGroupId);
        return getDetailValue(detail, KEY_KIND_CODE);
    }

    public static void sysParamGroupIdValidation(String paramGroupId,
                                                 String dbType,
                                                 String dbVersion,
                                                 String category,
                                                 String dbStorageEngine) throws ActivityException {

        if (StringUtils.isBlank(paramGroupId)) {
            return;
        }

        if (!StringUtils.startsWith(paramGroupId, SYS_PARAM_GROUP_ID_PREFIX)) {
            return;
        }

        Map<String, String> detail = describeSysParamGroupId(paramGroupId);
        if (!getStringValueFromDetailMap(detail, KEY_DB_TYPE).equalsIgnoreCase(dbType)) {
            throw new ActivityException(INVALID_PARAMETER_GROUP_SERVICE_TYPE);
        } else if (!getStringValueFromDetailMap(detail, KEY_DB_VERSION).equalsIgnoreCase(dbVersion)) {
            throw new ActivityException(INVALID_PARAMETER_GROUP_SERVICE_VERSION);
        } else if (StringUtils.isNotBlank(category) && !getStringValueFromDetailMap(detail,
                KEY_CATEGORY).equalsIgnoreCase(category)) {
            throw new ActivityException(INVALID_PARAMETER_GROUP_CATEGORY);
        } else if (StringUtils.isNotBlank(dbStorageEngine) && !getStringValueFromDetailMap(detail,
                KEY_DB_STORAGE_ENGINE).equalsIgnoreCase(dbStorageEngine)) {
            throw new ActivityException(INVALID_PARAMETER_GROUP_STORAGE_ENGINE);
        }
    }

    private static String getDetailValue(Map<String, String> details, String key) {
        if (details == null) {
            return "";
        }

        String value = details.get(key);
        if (value == null) {
            return "";
        }

        return value;
    }

    private static String getStringValueFromDetailMap(Map<String, String> detail, String key) {
        Object value = detail.get(key);
        if (value != null) {
            return value.toString();
        }
        return "";
    }

    @Data
    public static class SysParameterGroupInfo {
        private String dbType;
        private String dbVersion;
        private String characterType;
        private String category;
        private String dbStorageEngine;
        private String performanceMode;
        private String kindCode;
    }

    public static void main(String[] args) throws ActivityException {
        Map a  =SysParamGroupHelper.describeSysParamGroupId("rpg-sys-01040407010400");
        System.out.println(a);
    }
}