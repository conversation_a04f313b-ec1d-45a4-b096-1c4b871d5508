package com.aliyun.app.activityprovider.base.k8s;

import com.aliyun.app.activityprovider.base.nameservice.NameServiceHelper;
import com.aliyun.app.activityprovider.common.consts.CommonConsts;
import com.aliyun.app.activityprovider.common.exception.ApiClientException;
import com.aliyun.app.activityprovider.common.exception.CheckException;
import com.aliyun.app.activityprovider.common.utils.EnvUtils;
import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.openapi.ApiException;
import io.kubernetes.client.openapi.Configuration;
import io.kubernetes.client.openapi.apis.CoreV1Api;
import io.kubernetes.client.openapi.models.*;
import io.kubernetes.client.util.Config;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * K8S的工具
 *
 * <AUTHOR> on 2020/9/14.
 */
@Slf4j
@Component
public class KubeHelper extends NameServiceHelper<CoreV1Api> {

    private final static String userAgent = "MySQLProvider";
    private final static Integer MAX_REQUESTS = 2000;
    private final static Integer MAX_REQUESTS_PERT_HOST = 100;
//    private final static String K8S_NAMESPACE_USE = "default";

    @Resource
    private EnvUtils envUtils;

    @Value("${k8s.namespace:default}")
    private String k8sNameSpace;

    private final static String DEFAULT_NAME_SPACE = "default";

    private CoreV1Api getK8sApiClient(String k8sCluster) {
        return getRegionApiClient(k8sCluster, nsc -> {
            if (envUtils.isDBStackEnv()) {
                try {
                    return new CoreV1Api(Config.defaultClient());
                } catch (IOException e) {
                    log.error("init default kube client failed: " + e.getMessage());
                    throw new ApiClientException("init default kube client");
                }
            }
            String gwSubDomain = nsc.finAckK8sService(k8sCluster);
            if (StringUtils.isEmpty(gwSubDomain)) {
                throw new CheckException("Can not get ack k8s sub domain from name service.");
            }
            String k8sUrl = "http://" + gwSubDomain;
            ApiClient client = Config.fromUrl(k8sUrl);
            OkHttpClient okHttpClient = new OkHttpClient.Builder()
                    .writeTimeout(120, TimeUnit.SECONDS)
                    .readTimeout(120, TimeUnit.SECONDS)
                    .retryOnConnectionFailure(Boolean.TRUE)
                    .build();
            okHttpClient.dispatcher().setMaxRequests(MAX_REQUESTS);
            okHttpClient.dispatcher().setMaxRequestsPerHost(MAX_REQUESTS_PERT_HOST);
            client.setHttpClient(okHttpClient);
            client.setUserAgent(userAgent);
            client.addDefaultHeader("Host", k8sUrl.substring(7));
            //     client.setDebugging(true);
            Configuration.setDefaultApiClient(client);
            return new CoreV1Api(client);
        });
    }

    /**
     * 获取Pod的状态
     *
     * @param k8sCluster
     * @param podName
     * @return
     */
    public V1PodStatus getPodStatus(String k8sCluster, String nameSpace, String podName, boolean throwWhenFail) throws ApiException {
        try {
            if (StringUtils.isBlank(nameSpace)) {
                nameSpace = StringUtils.isBlank(k8sNameSpace) ? DEFAULT_NAME_SPACE : k8sNameSpace;
            }
            V1Pod podNewStatus = getK8sApiClient(k8sCluster).readNamespacedPodStatus(podName, nameSpace, null);
            return podNewStatus.getStatus();
        } catch (ApiException e) {
            if (throwWhenFail) {
                throw e;
            }
            log.error(e.getMessage(), e);
        }
        return null;
    }


    /**
     * 获取Node的Status
     *
     * @param k8sCluster
     * @param hostName
     * @return
     */
    public V1Node getNodeStatus(String k8sCluster, String hostName) {
        try {
            return getK8sApiClient(k8sCluster).readNodeStatus(hostName, null);
        } catch (ApiException e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }


    /**
     * 获取Pod的Event的信息
     *
     * @param k8sCluster
     * @param podName
     * @return
     */
    public String getPodEventInfo(String k8sCluster, String nameSpace, String podName) {
        try {
            if (StringUtils.isBlank(nameSpace)) {
                nameSpace = StringUtils.isBlank(k8sNameSpace) ? DEFAULT_NAME_SPACE : k8sNameSpace;
            }
            V1EventList eventList = getK8sApiClient(k8sCluster).listNamespacedEvent(nameSpace, null, null, null, String.format("involvedObject.kind=Pod,involvedObject.name=%s,type!=Normal", podName), null, 50, null, 20, null);
            if (eventList == null || CollectionUtils.isEmpty(eventList.getItems())) {
                return null;
            }
            StringBuilder sb = new StringBuilder();
            eventList.getItems().forEach(v ->
                    {
                        sb.append("class V1Event {\n");
                        sb.append("    count: ").append(v.getCount()).append("\n");
                        sb.append("    firstTimestamp: ").append(v.getFirstTimestamp()).append("\n");
                        sb.append("    lastTimestamp: ").append(v.getLastTimestamp()).append("\n");
                        sb.append("    message: ").append(v.getMessage()).append("\n");
                        sb.append("    reason: ").append(v.getReason()).append("\n");
                        sb.append("    type: ").append(v.getType()).append("\n");
                        sb.append("}");
                    }
            );
            return sb.toString();
        } catch (ApiException e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }


    /**
     * 根据PVC的名字获取PVC的Spec
     *
     * @param k8sCluster
     * @param nameSpace
     * @param volumeId   PVC的Name
     * @return
     * @throws ApiException
     */
    public V1PersistentVolumeClaimSpec getPersistentVolumeClaims(String k8sCluster, String nameSpace, String volumeId) throws ApiException {
        CoreV1Api coreV1Api = getK8sApiClient(k8sCluster);
        if (StringUtils.isBlank(nameSpace)) {
            nameSpace = StringUtils.isBlank(k8sNameSpace) ? DEFAULT_NAME_SPACE : k8sNameSpace;
        }
        V1PersistentVolumeClaim v1PersistentVolumeClaim = coreV1Api.readNamespacedPersistentVolumeClaim(volumeId, nameSpace, null, false, false);
        return v1PersistentVolumeClaim.getSpec();
    }


    /**
     * 根据Pod的PVC的名字获取PV
     *
     * @param k8sCluster
     * @param nameSpace
     * @param volumeId  PVC的Name
     * @return
     * @throws ApiException
     */
    public V1PersistentVolume getPersistentVolume(String k8sCluster, String nameSpace, String volumeId) throws ApiException {
        V1PersistentVolumeClaimSpec v1PersistentVolumeClaimSpec = getPersistentVolumeClaims(k8sCluster, nameSpace, volumeId);
        CoreV1Api coreV1Api = getK8sApiClient(k8sCluster);
        if (StringUtils.isBlank(nameSpace)) {
            nameSpace = StringUtils.isBlank(k8sNameSpace) ? DEFAULT_NAME_SPACE : k8sNameSpace;
        }
        if (v1PersistentVolumeClaimSpec == null) {
            throw new ApiException(volumeId + " pvc not found");
        }
        V1PersistentVolume v1PersistentVolume = coreV1Api.readPersistentVolume(v1PersistentVolumeClaimSpec.getVolumeName(), null, false, false);
        return v1PersistentVolume;
    }

    public String getNamespacePodLog(String k8sCluster, String podName, String namespace, String containerName) throws ApiException {
        CoreV1Api coreV1Api = getK8sApiClient(k8sCluster);
        if (StringUtils.isBlank(namespace)) {
            namespace = StringUtils.isBlank(k8sNameSpace) ? DEFAULT_NAME_SPACE : k8sNameSpace;
        }
        return coreV1Api.readNamespacedPodLog(
                podName, namespace, containerName,
                false, null, null,
                null, null, null, null, null);
    }


    @Override
    protected String getServiceType() {
        return CommonConsts.NAME_SERVICE_TYPE_ACK_K8S_SERVICE;
    }


    public String parseLog(String logs) {
        /*
         * 解析工具执行的日志
         * */
        String pattern = "(?<=start_reply\\=)(.+)(?=\\=end_reply)";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(logs);
        if (m.find()) {
            return m.group(0);
        } else {
            return logs;
        }
    }
}
