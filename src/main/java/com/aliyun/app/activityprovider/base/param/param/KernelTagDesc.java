package com.aliyun.app.activityprovider.base.param.param;

import com.aliyun.app.activityprovider.base.common.spec.entity.Spec;
import com.aliyun.app.activityprovider.base.param.component.ParamDependency;
import com.aliyun.app.activityprovider.common.exception.CheckException;
import com.aliyun.app.activityprovider.meta.ReplicaSetMetaHelper;
import com.aliyun.app.activityprovider.web.RequestSession;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.apsaradb.dbaasmetaapi.model.ServiceSpec;
import com.google.gson.Gson;
import io.kubernetes.client.openapi.models.V1Container;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.util.Map;
import java.util.stream.Collectors;

import static com.aliyun.app.activityprovider.base.param.utils.ParamConst.*;
import static com.aliyun.app.activityprovider.common.consts.ActionJobConsts.*;

/**
 * 用于根据Tag配置基础模板参数生成策略
 */
@Slf4j
public class KernelTagDesc {
    @Getter
    private String replicaSetName;
    @Getter
    private String baseSysDiffParamGroupId;
    @Getter
    private String levelDiffParamGroupId;
    @Getter
    private boolean isReadIns;
    @Getter
    private boolean isAliGroup;
    @Getter
    private Integer minorVersion;
    @Getter
    private boolean isXCluster;
    @Getter
    private boolean isArm;
    @Getter
    private boolean isAliSQLInnovation;
    @Getter
    private boolean isPolarXHATP;

    public KernelTagDesc() {

    }

    public static KernelTagDesc getKernelTagDesc(@NotNull ParamDependency dependency, ReplicaSet replicaSet) throws ApiException {
        KernelTagDesc tagDesc = new KernelTagDesc();
        tagDesc.replicaSetName = replicaSet.getName();
        // 获取内核镜像的Tag
        ServiceSpec serviceSpec = dependency.getDbaasMetaClient().getDefaultmetaApi().getServiceSpecById(RequestSession.getRequestId(),
                dependency.getDbaasMetaClient().getDefaultmetaApi().getReplicaSetServiceSpecId(RequestSession.getRequestId(), tagDesc.getReplicaSetName()));
        String tag = serviceSpec.getTag();
        Spec replicaSetSpec = new Gson().fromJson(serviceSpec.getSpec(), Spec.class);
        log.info("Kernel Tag is : {}", tag);

        tagDesc.isReadIns = dependency.getReplicaMetaHelper().isReadIns(replicaSet);
        tagDesc.isAliGroup = ReplicaSetMetaHelper.isAliGroup(replicaSet.getBizType());
        tagDesc.minorVersion = getMinorVersion(replicaSetSpec, serviceSpec.getTag());

        // 根据内核镜像设置需要的变量
        if (StringUtils.startsWith(tag, TAG_ALIGROUP_DEFAULT)) {
            // 集团 XCluster 本地盘
            tagDesc.isXCluster = true;

        } else if (StringUtils.startsWith(tag, TAG_ALIGROUP_CLOUD_PFS)) {
            // 集团 XCluster 云盘
            tagDesc.isXCluster = true;

        } else if (StringUtils.startsWith(tag, TAG_ALIGROUP_CLOUD_PFS_FOR_ARM)) {
            // 集团 XCluster ARM 云盘
            tagDesc.isXCluster = true;
            tagDesc.isArm = true;

        } else if (StringUtils.startsWith(tag, TAG_XCLUSTER_DOCKER_ARM_W)) {
            // 集团 XCluster ARM 云盘
            tagDesc.isXCluster = true;
            tagDesc.isArm = true;

        } else if (StringUtils.startsWith(tag, TAG_XCLUSTER_DOCKER_MULTI_W)) {
            // 集团多点写
            tagDesc.isXCluster = true;

        } else if (StringUtils.startsWith(tag, TAG_XCLUSTER_DOCKER_SINGLE_W)) {
            // 集团单点写
            tagDesc.isXCluster = true;

        } else if (StringUtils.startsWith(tag, TAG_XCLUSTER_POLARX_HATP)) {
            // PolarX-HATP
            tagDesc.isXCluster = true;
            tagDesc.isPolarXHATP = true;

        } else if (StringUtils.startsWith(tag, TAG_XCLUSTER_DHG_DOCKER_IMAGE)) {
            // MyBase XCluster
            tagDesc.isXCluster = true;

        } else if (StringUtils.startsWith(tag, TAG_ALISQL_DOCKER_IMAGE)) {
            // AliSQL

        } else if (StringUtils.startsWith(tag, TAG_ALISQL_XC_DOCKER_IMAGE)) {
            // AliSQL XC

        } else if (StringUtils.startsWith(tag, TAG_ALISQL_CLOUD_SSD)) {
            // AliSQL 云盘镜像

        } else if (StringUtils.startsWith(tag, TAG_ALISQL_DHG_DOCKER_IMAGE)) {
            // MyBase AliSQL

        } else if (StringUtils.startsWith(tag, TAG_ALISQL_AARCH_DOCKER_IMAGE)) {
            // AliSQL arm

        } else if (StringUtils.startsWith(tag, TAG_MARIADB_DOCKER_IMAGE)) {
            // Mariadb

        }  else if (StringUtils.startsWith(tag, TAG_ALISQL_BETA_DOCKER_IMAGE)) {
            // AliSQL 创新版
            tagDesc.isAliSQLInnovation = true;

        } else if (StringUtils.startsWith(tag, TAG_ALISQL_DUCKDB_DOCKER_IMAGE)) {

        } else {
            throw new CheckException("unknown tag: " + tag);
        }

        // 根据各种flag设置参数模板
        if (tagDesc.isAliGroup) {
            tagDesc.baseSysDiffParamGroupId = ALIGROUP_CONFIG_GROUPID;
            tagDesc.levelDiffParamGroupId = ALIGROUP_LEVEL_CONFIG_GROUPID;
        }
        if (tagDesc.isArm) {
            tagDesc.baseSysDiffParamGroupId = ALIGROUP_ARM_DIFF_GROUPID;
            tagDesc.levelDiffParamGroupId = ALIGROUP_ARM_LEVEL_DIFF_GROUPID;
        }
        if (tagDesc.isAliSQLInnovation) {
            tagDesc.baseSysDiffParamGroupId = ALISQL_INNOVATION_CONFIG_GROUPID;
        }
        if (tagDesc.isPolarXHATP) {
            tagDesc.baseSysDiffParamGroupId = PLOARX_HATP_CONFIG_GROUPID;
        }
        return tagDesc;
    }

    /**
     * 尝试从ServiceSpec中获取版本信息
     * 此处不从元数据中获取，当实例发生版本变化的时候，初始化参数时元数据可能还未更新
     */
    private static Integer getMinorVersion(Spec spec, String tag) {
        // Tag为标准【xxx_小版本号】模式时，优先使用Tag
        String[] tagInfo = tag.split("_");
        String minorVersionTag = tagInfo[tagInfo.length - 1];
        if (StringUtils.isNumeric(minorVersionTag)) {
            log.info("get minor version {} from tag {}", minorVersionTag, tag);
            return Integer.valueOf(minorVersionTag);
        }

        V1Container container = spec.getContainers().stream()
                .filter(v1Container -> "engine".equalsIgnoreCase(v1Container.getName()))
                .collect(Collectors.toList())
                .get(0);
        String image = container.getImage();
        String[] tmpMinorVersionInfo = StringUtils.split(image, ":");
        if (tmpMinorVersionInfo.length != 2) {
            log.info("unsupported spec: image is {}", image);
            return DEFAULT_MINOR_VERSION;
        }

        String minorVersionInfoStr = tmpMinorVersionInfo[1];
        if (!minorVersionInfoStr.contains("-")) {
            log.info("unsupported spec: image is {}", image);
            return DEFAULT_MINOR_VERSION;
        }

        try {
            return Integer.valueOf(StringUtils.split(minorVersionInfoStr, "-")[0]);
        } catch (Exception e) {
            log.info("parse version failed: image is {}", image);
            return DEFAULT_MINOR_VERSION;
        }
    }

    public static KernelTagDesc getTagForPEngineIns(@NotNull ParamDependency dependency,
                                                    ReplicaSet replicaSet,
                                                    Map<String, String> labels) throws ApiException {
        KernelTagDesc tag = new KernelTagDesc();
        tag.isReadIns = dependency.getReplicaMetaHelper().isReadIns(replicaSet);
        tag.isAliGroup = false;
        // Pengine实例生成配置时，默认保留全部，后面按照临时实例小版本移除参数
        tag.minorVersion = DEFAULT_MINOR_VERSION;
        return tag;
    }

    private static Integer tryToGetMinorVersion(String minorVersionStr) {
        if (StringUtils.isEmpty(minorVersionStr)) {
            return DEFAULT_MINOR_VERSION;
        }
        String separatorChars = "-";
        if (minorVersionStr.contains(":")) {
            separatorChars = ":";
        } else if (minorVersionStr.contains("_")) {
            separatorChars = "_";
        }
        try {
            return Integer.valueOf(StringUtils.split(minorVersionStr, separatorChars)[1]);
        } catch (Exception e) {
            return DEFAULT_MINOR_VERSION;
        }
    }
}