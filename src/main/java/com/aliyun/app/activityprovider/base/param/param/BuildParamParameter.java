package com.aliyun.app.activityprovider.base.param.param;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.aliyun.app.activityprovider.base.param.utils.paramgroup.SysParamGroupHelper;
import com.aliyun.app.activityprovider.common.consts.CommonConsts;
import com.aliyun.app.activityprovider.common.exception.ActivityException;
import com.aliyun.app.activityprovider.common.exception.CheckException;
import com.aliyun.app.activityprovider.meta.ReplicaSetMetaHelper;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ParamGroup;
import com.aliyun.apsaradb.dbaasmetaapi.model.Replica;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.app.activityprovider.base.param.utils.ParamConst.PARAM_GROUP_TYPE_SYS;
import static com.aliyun.app.activityprovider.base.ssl.SSLConst.LABEL_PARAM_SSL_ENABLE;
import static com.aliyun.app.activityprovider.common.consts.CommonConsts.DB_STORAGE_ENGINE_XENGINE;
import static com.aliyun.app.activityprovider.common.consts.CommonConsts.ENTERPRISE_LEVEL;
import static com.aliyun.app.activityprovider.common.consts.MysqlConsts.*;

/**
 * ParameterMeta: 生成实例参数时所需的元数据
 *
 * @blame 宇一
 */
@Data
@NoArgsConstructor
@Slf4j
public class BuildParamParameter implements Serializable {
    private String replicaSetName;
    private String paramGroupId;
    private String dbType;
    private String dbVersion;
    private String bizType;
    private String category;
    private String classCode;
    private String characterType;
    private String custinsType;
    private String xdbClusterId;
    private KernelTagDesc tag;
    private ReplicaSet replicaSet;
    private InstanceLevel instanceLevel;
    private Map<String, String> labels;
    private ParamGroup paramGroup;
    private Boolean isXdbSingle;
    private Replica.RoleEnum role = Replica.RoleEnum.MASTER;
    private Boolean isAliYun;
    private Double rcu;
    private Boolean isIoAccelerationEnabled;
    private Boolean isServerless;
    private Boolean isColdDataEnabled;
    private Boolean tdeEnabled;
    private Boolean isAnalyticReadOnlyIns;

    private final Map<LabelKey, Boolean> labelMatcherContainer = new HashMap<>();


    private final List<LabelsMatcher> labelsMatchers = new ArrayList<LabelsMatcher>() {{
        this.add(new PfsMatcher());
        this.add(new XEngineMatcher());
        this.add(new OpenSqlAuditMatcher());
        this.add(new OpenSSLMatcher());
        this.add(new ArchChangedMatcher());
        this.add(new OptimizedWritesMatcher());
    }};


    /**
     * 根据Label的Key判断是否命中
     *
     * @param labelKey
     * @return
     */
    public boolean isMatch(LabelKey labelKey) {
        if (!labelMatcherContainer.containsKey(labelKey)) {
            throw new CheckException("label key is not exists");
        }
        return labelMatcherContainer.get(labelKey);
    }


    /**
     * 用于判断记录的参数模板是否是标准系统模板，集团模板除外，集团模板作为特异性模板游离在模板之外
     */
    public boolean isSysParamGroup() {
        if (StringUtils.isEmpty(paramGroupId)) {
            return false;
        }
        if (paramGroup != null) {
            return PARAM_GROUP_TYPE_SYS.equals(paramGroup.getType());
        }
        return false;
    }

    /**
     * 用于判断参数模板基础来源，是否来自Extra表，用于灰度控制
     */
    public boolean isBaseOnExtra() {
        log.info("isBaseOnExtra for param: {}, {}, {}", dbType, dbVersion, category);
        return CommonConsts.SERVICE_MYSQL.equalsIgnoreCase(dbType) &&
                ENTERPRISE_LEVEL.equalsIgnoreCase(category) &&
                !MYSQL_VERSION_56.equalsIgnoreCase(dbVersion);
    }

    public void reloadMatcher() {
        for (LabelsMatcher labelsMatcher : labelsMatchers) {
            labelMatcherContainer.put(labelsMatcher.getKey(), labelsMatcher.doMatch(this));
        }
    }


    public enum LabelKey {
        PFS, XENGINE, OPEN_SQL_AUDIT, SSL_ENABLE, ARCH_CHANGED, OPTIMIZED_WRITES;

    }

    private abstract static class LabelsMatcher {

        abstract LabelKey getKey();

        abstract boolean doMatch(BuildParamParameter requestMeta);

    }

    /**
     * 判断是否pfs
     */
    private static class PfsMatcher extends LabelsMatcher {

        @Override
        LabelKey getKey() {
            return LabelKey.PFS;
        }

        @Override
        public boolean doMatch(BuildParamParameter requestMeta) {
            return ReplicaSetMetaHelper.isPfs(requestMeta.getLabels());
        }
    }

    /**
     * 设置XEngine模板标志
     */
    private static class XEngineMatcher extends LabelsMatcher {

        @Override
        LabelKey getKey() {
            return LabelKey.XENGINE;
        }

        @Override
        public boolean doMatch(BuildParamParameter requestMeta) {
            try {
                return DB_STORAGE_ENGINE_XENGINE.equalsIgnoreCase(
                        SysParamGroupHelper.getDBStorageEngine(requestMeta.getParamGroupId()));
            } catch (ActivityException e) {
                throw new CheckException(e.getMessage());
            }
        }
    }


    /**
     * 设置SQL审计开关
     */
    private static class OpenSqlAuditMatcher extends LabelsMatcher {

        @Override
        LabelKey getKey() {
            return LabelKey.OPEN_SQL_AUDIT;
        }

        @Override
        public boolean doMatch(BuildParamParameter requestMeta) {
            return "1".equalsIgnoreCase(requestMeta.getLabels().getOrDefault("enable_sql_log", "0"));
        }
    }

    /**
     * 设置SSL开启开关
     */
    private static class OpenSSLMatcher extends LabelsMatcher {

        @Override
        LabelKey getKey() {
            return LabelKey.SSL_ENABLE;
        }

        @Override
        public boolean doMatch(BuildParamParameter requestMeta) {
            return "1".equalsIgnoreCase(requestMeta.getLabels().getOrDefault(LABEL_PARAM_SSL_ENABLE, "0"));
        }
    }
    /**
     * 设置double write开关
     */
    private static class ArchChangedMatcher extends LabelsMatcher {
        @Override
        LabelKey getKey() {
            return LabelKey.ARCH_CHANGED;
        }

        @Override
        public boolean doMatch(BuildParamParameter requestMeta) {
            return "1".equalsIgnoreCase(requestMeta.getLabels().getOrDefault("arch_changed", "0"));
        }
    }

    private static class OptimizedWritesMatcher extends LabelsMatcher {
        @Override
        LabelKey getKey() {
            return LabelKey.OPTIMIZED_WRITES;
        }
        @Override
        boolean doMatch(BuildParamParameter requestMeta) {
            String optimizedWritesInfoString = requestMeta.getLabels().get(LABEL_PARAM_OPTIMIZED_WRITES_INFO);
            HashMap<String, Boolean> optimizedWritesInfo = JSON.parseObject(optimizedWritesInfoString, new TypeReference<HashMap<String, Boolean>>(){});
            if (optimizedWritesInfo == null || optimizedWritesInfo.isEmpty()) {
                return false;
            }
            Boolean initOptimizedWrites = optimizedWritesInfo.getOrDefault(LABEL_OF_INIT_OPTIMIZED_WRITES, false);
            Boolean optimizedWrites = optimizedWritesInfo.getOrDefault(LABEL_OF_OPTIMIZED_WRITES, false);
            return initOptimizedWrites && optimizedWrites;
        }
    }

}
