package com.aliyun.app.activityprovider.base.param.builder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.app.activityprovider.base.param.component.ParamDependency;
import com.aliyun.app.activityprovider.base.param.param.BuildParamParameter;
import com.aliyun.app.activityprovider.base.param.param.ParamMetaBuilder;
import com.aliyun.app.activityprovider.base.param.utils.*;
import com.aliyun.app.activityprovider.base.param.utils.paramgroup.SysParamGroupHelper;
import com.aliyun.app.activityprovider.common.consts.MysqlConsts;
import com.aliyun.app.activityprovider.common.exception.CheckException;
import com.aliyun.app.activityprovider.common.exception.NotRetryException;
import com.aliyun.app.activityprovider.common.utils.LangUtil;
import com.aliyun.app.activityprovider.meta.ReplicaSetMetaHelper;
import com.aliyun.app.activityprovider.web.RequestSession;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.thymeleaf.util.ListUtils;
import org.thymeleaf.util.MapUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static com.aliyun.app.activityprovider.base.param.utils.ParamConst.*;
import static com.aliyun.app.activityprovider.base.param.utils.paramgroup.SysParamGroupHelper.PARAM_GROUP_PERFORMANCE_HIGH;
import static com.aliyun.app.activityprovider.base.param.utils.paramgroup.SysParamGroupHelper.SYS_PARAM_GROUP_ID_PREFIX;
import static com.aliyun.app.activityprovider.common.consts.CommonConsts.DB_TYPE_MARIADB;
import static com.aliyun.app.activityprovider.common.consts.CommonConsts.DB_TYPE_MYSQL;
import static com.aliyun.app.activityprovider.common.consts.MysqlConsts.*;
import static com.aliyun.app.activityprovider.common.consts.ResourceKeyConsts.BUFFER_POOL_EXTENSION_OVER_SOLD_PERSENT;
import static com.aliyun.app.activityprovider.common.consts.ResourceKeyConsts.RATIO_OF_BPE_TO_INSTANCE_MEMORY;

/**
 * 节点级别参数生成
 * */
@Slf4j
public class ReplicaParamBuilder extends ParamBuilder {

    private Replica replica;

    private Map<String, String> newChangeParams = null;

    private Map<String, ConfigTemplate> sysBaseParamTemplate;
    private Map<String, ConfigTemplate> levelParam;

    private Map<String, ConfigTemplate> currentInstanceParam;
    private Map<String, ConfigTemplate> userChangedConfigs;
    private Map<String, ConfigTemplate> magicParams;
    private Map<String, ConfigTemplate> deprecatedParams;

    public ReplicaParamBuilder() {
        super();
    }

    public ReplicaParamBuilder(ParamDependency dependency,
                               ReplicaSet replicaSet,
                               Replica replica,
                               String targetClassCode,
                               Double rcu,
                               String paramGroupId,
                               ParamCustomer customer) throws Exception {
        this(dependency, replicaSet, replica, targetClassCode, rcu, paramGroupId, customer, Replica.RoleEnum.MASTER, null);
    }

    /**
     * Replica级别参数生成器，在规格发生变化时，使用这个类
     *
     * @param dependency   生成参数时依赖的服务组件
     * @param replicaSet   对应参数生成的实例
     * @param targetClassCode 指定规格，null时默认从Replica.classCode属性中获获取
     * @param rcu          Serverless 规格系数，null时默认从custins_param中获获取
     * @param paramGroupId 指定参数模板，null是默认从实例属性中获取
     * @param customer     参数定义回调，根据需要对相关方法进行实现，修改参数生成逻辑
     * @param role     Replica角色，只有在Logger节点时会指定
     * @param newChangeParams   新修改参数时，指定这个值，用于增量生成参数
     * @throws Exception
     */
    public ReplicaParamBuilder(ParamDependency dependency,
                               ReplicaSet replicaSet,
                               Replica replica,
                               String targetClassCode,
                               Double rcu,
                               String paramGroupId,
                               ParamCustomer customer,
                               Replica.RoleEnum role,
                               Map<String, String> newChangeParams) throws Exception {
        super();
        this.dep = dependency;
        this.customer = customer;
        this.replica = replica;
        this.newChangeParams = newChangeParams;
        log.info("start to build parameter meta..");
        parameterMeta = ParamMetaBuilder.initParameterMeta(dep, replicaSet, targetClassCode, rcu, paramGroupId);
        parameterMeta.setRole(role);
        reload();
    }

    /**
     * 获取参数，参数表达式未解析
     */
    public Map<String, ConfigTemplate> getParamsBeforeExprTranslate() throws Exception {
        Map<String, ConfigTemplate> configTemplate = new HashMap<>();
        configTemplate.putAll(sysBaseParamTemplate);
        configTemplate.putAll(currentInstanceParam);
        configTemplate.putAll(levelParam);
        configTemplate.putAll(userChangedConfigs);
        configTemplate.putAll(magicParams);

        duplicateParameterCheck(configTemplate);

        // AliYun场景下，不需要移除非loose参数
        if (!parameterMeta.getIsAliYun()) {
            configTemplate = removeDuplicateParameter(configTemplate);
        }


        // ==> START ================================================================
        // 此处用于放置参数强校验逻辑

        // 检查并重置innodb_buffer_pool_size大小
        checkAndResetInnodbBufferPoolSizeToExpr(configTemplate);
        // sync_mode, sync_binlog_mode重置
        checkAndResetPerformanceMode(configTemplate);
        // 强制开启 opt_enable_rds_priv_strategy 参数，防止用户越权
        checkAndResetOptEnableRdsPrivStrategy(configTemplate);
        // 检查是否为不开启 BPE 的非临时实例，删除配置文件中，可能由克隆或者只读创建引入的 BPE 参数
        checkAndResetIoAccelerationParams(configTemplate);
        // 检查是否为分析型只读实例并设置相关参数
        checkAndResetDuckDBParams(configTemplate);
        // 检查是否为开启冷存归档的实例，根据冷存开关写入mycnf_instance参数表,实际写入内核参数由customParameterForReplica完成
        checkAndResetColdDataEnabled(configTemplate);
        // 强制检查character_set_server和collation_server是否有一致性问题
        doValidCollationServer(configTemplate);
        //checkAndResetCollationServer(sysBaseParamTemplate, configTemplate);

        // ==> END ==================================================================

        if (customer != null) {
            // 回调进行自定义修改
            Map<String, ConfigTemplate> origin = new HashMap<>();
            Map<String, ConfigTemplate> finalConfigTemplate = configTemplate;
            configTemplate.keySet().forEach((name) -> origin.put(name, copyConfigTemplate(finalConfigTemplate, name, null)));
            customer.customParameter(this, parameterMeta, finalConfigTemplate);
            String diffLogs = ParamDiffUtils.diffParamMap(toStringMap(origin), toStringMap(finalConfigTemplate));
            if (StringUtils.isNotEmpty(diffLogs)) {
                log.info("custom changed by customer: \n{}", diffLogs);
            }
            configTemplate = finalConfigTemplate;
        }

        // 检查并写入 double write 参数
        checkAndResetDoubleWrite(configTemplate);

        // 检查 innodb_log_file_size 参数，如果需要会重置为默认值
        checkAndResetLogFileSize(configTemplate);

        String otherParam;
        boolean startWithLoose;
        for (String k : deprecatedParams.keySet()) {
            startWithLoose = StringUtils.startsWith(k, PARAMETER_LOOSE_PREFIX);
            otherParam = startWithLoose ? StringUtils.remove(k, PARAMETER_LOOSE_PREFIX) : PARAMETER_LOOSE_PREFIX + k;
            configTemplate.remove(k);
            configTemplate.remove(otherParam);
        }
        log.info("minorVersion is {}, deprecatedParams: {}",
                parameterMeta.getTag().getMinorVersion(), deprecatedParams.keySet());

        return configTemplate;
    }

    public Map<String, ConfigTemplate> getCustomParamsForServerlessScale() throws Exception {
        // finalConfig is ins config template before calculate (eg. bp = {DBInstanceClassMemory*1/4})
        Map<String, ConfigTemplate> finalConfig = getParamsBeforeExprTranslate();

        Map<String, ConfigTemplate> defaultConfig = new HashMap<>();
        defaultConfig.putAll(sysBaseParamTemplate);
        defaultConfig.putAll(levelParam);

        Map<String, ConfigTemplate> customParams = new HashMap<>();
        for (String paraName: ALLOW_EDIT_PARAMS_FOR_SERVERLESS_UPDATE_WHEN_SCALING) {
            ConfigTemplate finalValue = finalConfig.get(paraName);
            ConfigTemplate defaultValue = defaultConfig.get(paraName);
            // if user edited the param, we need use it when serverless scale
            if (!StringUtils.equals(finalValue.getDefaultValue(), defaultValue.getDefaultValue())) {
                customParams.put(paraName, finalValue);
            }
        }
        log.info("custom params need update when serverless scale: {}", customParams);
        return customParams;
    }

    public Map<String, String> getParamsAfterExprTranslate(List<String> paraNames) throws Exception {
        Map<String, ConfigTemplate> template = getParamsBeforeExprTranslate();
        Map<String, String> params = new HashMap<>();
        paraNames.forEach(name -> params.put(name, template.get(name).getDefaultValue()));

        dep.getParamExprService().paramExprTranslate(
                parameterMeta.getReplicaSet(),
                parameterMeta.getInstanceLevel(),
                parameterMeta.getRcu(),
                params,
                template
        );
        return params;
    }

    /**
     * 获取参数，参数表达式已解析
     */
    public Map<String, String> getParamsAfterExprTranslate() throws Exception {
        Map<String, ConfigTemplate> template = getParamsBeforeExprTranslate();
        Map<String, String> params = newChangeParams == null ? toStringMap(template) : new HashMap<>(newChangeParams);
        dep.getParamExprService().paramExprTranslate(
                parameterMeta.getReplicaSet(),
                parameterMeta.getInstanceLevel(),
                parameterMeta.getRcu(),
                params,
                template
        );

        // 刷参情况下，不进行customer处理，避免非预期刷新用户有没有指定的参数
        if (newChangeParams != null) {
            return params;
        }

        // 实例相关参数统一修改
        dep.getBaseParamCustomer().customParameterForReplica(this, parameterMeta, params, replica);

        // 业务指定参数修改
        if (customer != null) {
            // 回调进行自定义修改
            Map<String, String> origin = new HashMap<>(params);
            customer.customParameterForReplica(this, parameterMeta, params, replica);
            String diffLogs = ParamDiffUtils.diffParamMap(origin, params);
            if (StringUtils.isNotEmpty(diffLogs)) {
                log.info("Replica {} custom changed by customer: \n{}", replica.getId(), diffLogs);
            }
        }
        return params;
    }

    /**
     * 重新生成加载各个层级的参数，当元数据变更后可以使用该方法重载
     */
    protected void reload() throws Exception {
        // 初始化 baseSysParam
        reloadSysBaseConfigTemplate()
                // 初始化 currentInstanceParam
                .reloadCurrentReplicaSetConfigs()
                // 初始化 levelParam
                .reloadLevelConfig()
                // 初始化 userChangedConfigs
                .reloadUserChangedParam()
                // 初始化 magicParams
                .reloadMagicParameters();
    }



    //处理是否需要清理collation_server参数
    public void doValidCollationServer(Map<String, ConfigTemplate> configParam) {
        if (configParam.containsKey(COLLATION_SERVER_PARAM_NAME)) {
            if (!configParam.containsKey(CHARACTER_SET_SERVER_PARAM_NAME)) {
                configParam.remove(COLLATION_SERVER_PARAM_NAME);
            } else {
                String collationServerValue = configParam.get(COLLATION_SERVER_PARAM_NAME).getDefaultValue();
                String characterSetServerValue = configParam.get(CHARACTER_SET_SERVER_PARAM_NAME).getDefaultValue();
                if (!CHARACTER_SET_SERVER_COLLACTION_SERVER_MAP.get(characterSetServerValue).contains(collationServerValue)){
                    configParam.remove(COLLATION_SERVER_PARAM_NAME);
                }
            }
        }
    }

    private void ignoreSysBaseTemplateParam(Map<String, ConfigTemplate> sysBaseParamTemplate) {
        // 该参数需要给用户展示修改，但是不能使用系统模板的默认值 需要移除
        for (String paramName : IGNORE_SYS_BASE_TEMPLATE_PARAM) {
            log.info("ignoreSysBaseTemplateParam {}", paramName);
            sysBaseParamTemplate.remove(paramName);
        }
    }

    /**
     * 根据 parameterMeta 中初始化的信息，生成基础参数模板
     * 基础参数模板从下至上分由如下几层模板合并而成，上层覆盖下层：
     * - 版本基础无差异模板
     * - 系统参数模板/用户指定参数模板
     */
    private ReplicaParamBuilder reloadSysBaseConfigTemplate() throws Exception {
        log.info("getBaseSysConfigTemplate parameterMeta: {}", JSONObject.toJSONString(parameterMeta));
        sysBaseParamTemplate = new HashMap<>();
        deprecatedParams = new HashMap<>();

        // 获取通用基础模板
        log.info("==> start to get base template");
        List<ConfigTemplate> configList = parameterMeta.isBaseOnExtra() ?
                getNormalBaseTemplateFromExtra(null) :
                getNormalBaseTemplate();
        if (configList == null || configList.isEmpty()) {
            throw new CheckException("Got empty base template list");
        }
        putSysParams(sysBaseParamTemplate, configList);

        // 该参数需要给用户展示修改，但是不能使用系统模板的默认值 需要移除
        // 不要忽略，参数合并的时候去判断collation_server和character_set_server是否符合预期就好
        // ignoreSysBaseTemplateParam(sysBaseParamTemplate);

        // 使用普通模板的参数，还需要再覆盖一次extra表中的信息
        if (!parameterMeta.isBaseOnExtra()) {
            log.info("==> start to get base extra template");
            List<ConfigTemplate> extraConfigList = getNormalBaseTemplateFromExtra(null);
            if (!ListUtils.isEmpty(extraConfigList)) {
                putSysParams(sysBaseParamTemplate, extraConfigList);
            }
        }

        // 根据不同的tag，生成不同的模板
        String sysBaseDiffParamGroupId = parameterMeta.getTag().getBaseSysDiffParamGroupId();
        if (StringUtils.isNotEmpty(sysBaseDiffParamGroupId)) {
            log.info("==> start to get sys tag diff parameter, with paramGroupId {}", sysBaseDiffParamGroupId);
            mergeSysParamGroupParamsIntoMap(sysBaseDiffParamGroupId, sysBaseParamTemplate, true);
        }

        // 根据实例绑定的参数模板id，生成模板
        if (parameterMeta.isSysParamGroup() && StringUtils.isNotEmpty(parameterMeta.getParamGroupId())) {
            // 覆盖系统参数模板
            // 系统参数模板存在默认模板，默认模板可能为空
            log.info("==> start to get sys param group: {}", parameterMeta.getParamGroupId());
            mergeSysParamGroupParamsIntoMap(parameterMeta.getParamGroupId(), sysBaseParamTemplate, false);
        } else if (parameterMeta.getParamGroup() != null) {
            // 覆盖用户指定模板
            log.info("==> start to get user param group params");
            mergeUserParamGroupParamsIntoMap(sysBaseParamTemplate);
        }

        // 覆盖用户创建时指定的自定义参数（如时区、大小写敏感等）
        if (parameterMeta.getLabels() != null) {
            String customParamsJSONStr = parameterMeta.getLabels().get(CUSTINS_MYSQL_CUSTOM_PARAMS);
            if (StringUtils.isNotEmpty(customParamsJSONStr)) {
                log.info("set {} into template: {}", CUSTINS_MYSQL_CUSTOM_PARAMS, customParamsJSONStr);
                HashMap<String, String> customParams = new Gson().fromJson(customParamsJSONStr, HashMap.class);
                for (String name : customParams.keySet()) {
                    if (StringUtils.equalsIgnoreCase(name, "time_zone")) {
                        // 配置文件中不能包含time_zone，只能设置为default_time_zone，这里对存量架构做一个兼容
                        // The initial global server time zone value can be specified explicitly at startup with the --default-time-zone option on the command line, or you can use the following line in an option file:
                        // https://dev.mysql.com/doc/refman/8.0/en/time-zone-support.html
                        log.warn("remove [time_zone] from mysql_custom_params");
                        continue;
                    }
                    String value = customParams.get(name);
                    if (StringUtils.isNotEmpty(value)) {
                        sysBaseParamTemplate.put(name, copyConfigTemplate(sysBaseParamTemplate, name, value));
                    }
                }
            }
        }

        return this;
    }

    /**
     * 从 mycnf_custinstance 表中获取实例参数
     */
    private ReplicaParamBuilder reloadCurrentReplicaSetConfigs() throws Exception {
        sysBaseParamCheck();
        currentInstanceParam = new HashMap<>();
        Map<String, String> replicaSetConfigs = dep.getDbaasMetaClient().getDefaultmetaApi()
                .listReplicaSetConfigs(
                        RequestSession.getRequestId(),
                        parameterMeta.getReplicaSetName(),
                        null, null, null, null
                ).getItems();

        if (parameterMeta.getIsServerless() && !MapUtils.isEmpty(replicaSetConfigs)) {
            Set<String> paramsServerlessNoSupportChange = getParamsServerlessNoSupportChange();
            replicaSetConfigs.keySet().removeIf(name -> {
                boolean ignore = paramsServerlessNoSupportChange.contains(name);
                if (ignore) {
                    log.info("current ins is serverless, ignore origin param: {} value: {}", name, replicaSetConfigs.get(name));
                }
                return ignore;
            });
        }


        if (!MapUtils.isEmpty(replicaSetConfigs)) {
            for (Map.Entry<String, String> entry : replicaSetConfigs.entrySet()) {
                String name = entry.getKey();
                String value = entry.getValue();
                ConfigTemplate tempCopy = copyConfigTemplate(sysBaseParamTemplate, name, value);
                currentInstanceParam.put(name, tempCopy);
            }
        }

        if (customer != null) {
            // 修改前对生成的原逻辑进行备份
            Map<String, ConfigTemplate> origin = new HashMap<>();
            currentInstanceParam.keySet().forEach((name) -> origin.put(name, copyConfigTemplate(currentInstanceParam, name, null)));
            // 提供回调支持业务自定义修改
            customer.customInstanceConfig(this, parameterMeta, currentInstanceParam);

            // 修改后获取变更差异，输出日志方便debug
            String diffLogs = ParamDiffUtils.diffParamMap(toStringMap(origin), toStringMap(currentInstanceParam));
            if (!MapUtils.isEmpty(origin) && StringUtils.isNotEmpty(diffLogs)) {
                log.info("currentInstanceParam changed by customer: \n{}", diffLogs);
            }
        }

        return this;
    }

    /**
     * 参数优化与参数正确性兜底
     */
    private ReplicaParamBuilder reloadMagicParameters() throws Exception {
        sysBaseParamCheck();
        magicParams = new HashMap<>();

        boolean isReadIns = dep.getReplicaMetaHelper().isReadIns(parameterMeta.getReplicaSet());
        boolean isXCluster = parameterMeta.getTag().isXCluster();
        boolean isAliGroup = parameterMeta.getTag().isAliGroup();
        boolean isLogger = Replica.RoleEnum.LOGGER.equals(parameterMeta.getRole());
        boolean isPfs = parameterMeta.isMatch(BuildParamParameter.LabelKey.PFS);
        boolean isForLogger = Replica.RoleEnum.LOGGER.equals(parameterMeta.getRole());

        // 云上实例统一设置innodb_use_native_aio为ON
        if (parameterMeta.getIsAliYun()) {
            copyFromTempAndPutToDst(INNODB_USE_NATIVE_AIO, magicParams, sysBaseParamTemplate);

            // 用户没有修改过open_files_limit的情况下，如果系统模板 > 实例快照，将系统模板值作为最终使用
            if (!userChangedConfigs.containsKey(OPEN_FILES_LIMIT) && sysBaseParamTemplate.containsKey(OPEN_FILES_LIMIT) && currentInstanceParam.containsKey(OPEN_FILES_LIMIT)) {
                Integer openFilesLimitSysBase = Integer.valueOf(Objects.requireNonNull(sysBaseParamTemplate.get(OPEN_FILES_LIMIT).getDefaultValue()));
                Integer openFilesLimitCurrIns = Integer.valueOf(Objects.requireNonNull(currentInstanceParam.get(OPEN_FILES_LIMIT).getDefaultValue()));
                if (openFilesLimitSysBase > openFilesLimitCurrIns) {
                    log.info("openFilesLimitSysBase {} > openFilesLimitCurrIns {}, put magic param {} = {}", openFilesLimitSysBase, openFilesLimitCurrIns, OPEN_FILES_LIMIT, openFilesLimitSysBase);
                    Map<String, String> openFilesLimit = Collections.singletonMap(OPEN_FILES_LIMIT, openFilesLimitSysBase.toString());
                    copyFromTempAndPutToDst(openFilesLimit, magicParams, sysBaseParamTemplate);
                }
            }
        }

        // 只读实例优化
        if (isReadIns) {
            copyFromTempAndPutToDst(READ_INS_PARAMETER, magicParams, sysBaseParamTemplate);
        }

        // PFS文件系统逻辑
        if (isPfs && !isForLogger) {
            copyFromTempAndPutToDst(CLOUD_PFS_PARAMS, magicParams, sysBaseParamTemplate);
        }
        // Logger不使用pfs文件系统
        if (isPfs && isForLogger) {
            copyFromTempAndPutToDst(CLOUD_PFS_PARAMS_FOR_LOGGER, magicParams, sysBaseParamTemplate);
        }

        // 集团特异性参数逻辑
        // FIXME：集团logger参数为自定义参数，暂时先硬编码到代码中，后续需要对比规格参数后转换规格
        if (isAliGroup) {
            copyFromTempAndPutToDst(ALI_GROUP_MEMCACHED, magicParams, sysBaseParamTemplate);
        }
        if (isAliGroup && isForLogger) {
            copyFromTempAndPutToDst(ALI_GROUP_LOGGER, magicParams, sysBaseParamTemplate);
        }

        // 三节点参数
        if (isXCluster) {
            magicParams.put("cluster-id", copyConfigTemplate(sysBaseParamTemplate, "cluster-id", parameterMeta.getXdbClusterId()));
            magicParams.put("loose_cluster-id", copyConfigTemplate(sysBaseParamTemplate, "loose_cluster-id", parameterMeta.getXdbClusterId()));
        }

        // 三节点非只读
        if (isXCluster && isLogger && !isReadIns) {
            magicParams.put("loose_cluster-log-type-node", copyConfigTemplate(sysBaseParamTemplate, "loose_cluster-log-type-node", "ON"));
        }

        // 参数正确性兜底逻辑
        // SQL审计参数
        if (isAliGroup) {
            if (parameterMeta.isMatch(BuildParamParameter.LabelKey.OPEN_SQL_AUDIT)) {
                String paramName = "8.0".equalsIgnoreCase(parameterMeta.getDbVersion()) ?
                        "rds_audit_log_enabled" : "loose_opt_rds_audit_log_enabled";
                magicParams.put(paramName, copyConfigTemplate(sysBaseParamTemplate, paramName, "ON"));
            }
        }
        else {
            String paramName = "8.0".equalsIgnoreCase(parameterMeta.getDbVersion()) ? "rds_audit_log_enabled" : "loose_opt_rds_audit_log_enabled";
            if (parameterMeta.isMatch(BuildParamParameter.LabelKey.OPEN_SQL_AUDIT)) {
                magicParams.put(paramName, copyConfigTemplate(sysBaseParamTemplate, paramName, "ON"));
            } else {  // 强依赖管控参数
                magicParams.put(paramName, copyConfigTemplate(sysBaseParamTemplate, paramName, "OFF"));
            }
        }

        log.info("magicParams: {}", JSONObject.toJSONString(toStringMap(magicParams)));
        return this;
    }

    /**
     * 获取用户修改过的参数
     */
    private ReplicaParamBuilder reloadUserChangedParam() throws ApiException {
        sysBaseParamCheck();

        userChangedConfigs = new HashMap<>();
        List<ConfigChangeLog> changeParams = ParameterHelper.getChangeLogApplied(dep.getDbaasMetaClient().getDefaultmetaApi(), parameterMeta.getReplicaSetName());
        if (ListUtils.isEmpty(changeParams)) {
            // 临时实例取不到changelog时，再次从原实例取changelog，避免changelog里面的修改被规格参数覆盖
            if (dep.getReplicaSetMetaHelper().isTmpIns(parameterMeta.getReplicaSet())) {
                log.info("changelog from replicaSet {} is empty and it is tmp ins, get changelog from {}",
                        parameterMeta.getReplicaSet().getName(), parameterMeta.getReplicaSet().getPrimaryInsName());
                changeParams = ParameterHelper.getChangeLogApplied(dep.getDbaasMetaClient().getDefaultmetaApi(),
                        parameterMeta.getReplicaSet().getPrimaryInsName());
            }

            if (ListUtils.isEmpty(changeParams)) {
                log.info("user changed param is empty, skip");
                return this;
            }
        }

        if (parameterMeta.getIsServerless()) {
            Set<String> paramsServerlessNoSupportChange = getParamsServerlessNoSupportChange();
            changeParams.removeIf(changeLog -> {
                boolean ignore = paramsServerlessNoSupportChange.contains(changeLog.getName());
                if (ignore) {
                    log.info("current ins is serverless, ignore user changed param: {} value: {}", changeLog.getName(), changeLog.getNewValue());
                }
                return ignore;
            });
        }

        String name;
        String value;
        String otherParam;
        boolean startWithLoose;
        for (ConfigChangeLog changedParam : changeParams) {
            name = changedParam.getName();
            value = changedParam.getNewValue();

            startWithLoose = StringUtils.startsWith(name, PARAMETER_LOOSE_PREFIX);
            otherParam = startWithLoose ? StringUtils.remove(name, PARAMETER_LOOSE_PREFIX) : PARAMETER_LOOSE_PREFIX + name;
            if (!startWithLoose && !sysBaseParamTemplate.containsKey(name)) {
                // 非loose开头的参数如果不在当前系统参数模板中，从用户修改中去掉，否则可能会导致实例拉不起来
                // 例：做过大版本升级的实例，可能在未升级大版本前做过修改参数，但是升级上去后这个参数不存在新的大版本中
                log.warn("[{}] in sysBaseParamTemplate not found, ignore it", name);
                continue;
            }
            // 记录用户修改过的参数
            // 有如下两种情况时，补充changelog，避免后续的参数校验出错：
            // 1. 同时修改过loose与非loose参数；
            // 2. 系统模板同时存在loose与非loose参数；
            userChangedConfigs.put(name, copyConfigTemplate(sysBaseParamTemplate, name, value));
            if (sysBaseParamTemplate.containsKey(otherParam) || userChangedConfigs.containsKey(otherParam)) {
                log.info("{} has changelog and put {} to changelog too.", name, otherParam);
                userChangedConfigs.put(otherParam, copyConfigTemplate(sysBaseParamTemplate, otherParam, value));
            }
            // change_log的连接数值需要加上预留值,否则会覆盖掉快照值
            if (StringUtils.equals(name, MAX_CONNECTIONS) && !StringUtils.isEmpty(value)) {
                handledMaxConnInChangeLog(value);
            }
        }

        log.info("userChangedConfigs: {}", JSONObject.toJSONString(toStringMap(userChangedConfigs)));
        return this;
    }

    /**
     * 生成系统规格参数
     */
    private ReplicaParamBuilder reloadLevelConfig() throws Exception {
        sysBaseParamCheck();

        levelParam = new HashMap<>();

        // 获取云上规格参数
        List<InstanceConfig> levelConfigList = dep.getDbaasMetaClient()
                .getDefaultmetaApi()
                .listInstanceLevelConfigs(
                        RequestSession.getRequestId(),
                        parameterMeta.getDbType(),
                        parameterMeta.getDbVersion(),
                        parameterMeta.getInstanceLevel().getClassCode()
                ).getItems();

        // copy param into levelConfigMap
        if (!ListUtils.isEmpty(levelConfigList)) {
            levelConfigList.forEach(config -> levelParam.put(
                    config.getName(), copyConfigTemplate(sysBaseParamTemplate, config.getName(), config.getValue())
            ));
        }

        // 获取最大连接数配置
        Map<String, ConfigTemplate> maxConnConfigs = getMaxConn();
        levelParam.putAll(maxConnConfigs);

        // 根据不同的tag，生成不同的规格差异模板
        String levelDiffParamGroupId = parameterMeta.getTag().getLevelDiffParamGroupId();
        if (StringUtils.isNotEmpty(levelDiffParamGroupId)) {
            // 系统参数模板，替换参数模板配置
            log.info("== GetDiffParamGroup: {} ==", levelDiffParamGroupId);
            mergeSysParamGroupParamsIntoMap(levelDiffParamGroupId, levelParam, true);
        }

        // xengine模板里存在参数会抑制innodb规格参数，所以需要在生成规格参数的时候再覆盖一次模板
        if (parameterMeta.isMatch(BuildParamParameter.LabelKey.XENGINE)) {
            mergeSysParamGroupParamsIntoMap(parameterMeta.getParamGroupId(), levelParam, true);
        }

        log.info("levelParam: {}", JSONObject.toJSONString(toStringMap(levelParam)));
        return this;
    }

    /**
     * 获取实例最大连接数
     */
    private Map<String, ConfigTemplate> getMaxConn() throws ApiException {
        sysBaseParamCheck();
        return getMaxConnForEngine();
    }

    /**
     * 获取MySQL，Mariadb 的最大连接数
     */
    private Map<String, ConfigTemplate> getMaxConnForEngine() throws ApiException {
        String requestId = RequestSession.getRequestId();
        DefaultApi metaApi = dep.getDbaasMetaClient().getDefaultmetaApi();

        // 初始化连接数相关参数
        Integer rdsReservedParamSum = getReservedConnectionsSum();
        int maxUserConnections = 0;
        int maxConnections = 0;

        // max_conn form level
        InstanceLevel levelInfo = parameterMeta.getInstanceLevel();
        AtomicInteger statConnection = new AtomicInteger(0);
        if (levelInfo.getMaxConn() != null) {
            statConnection.set(levelInfo.getMaxConn());
        }

        // 兼容stat表中临时设置的最大连接数
        List<Replica> replicaList = metaApi.listReplicasInReplicaSet(requestId, parameterMeta.getReplicaSetName(), null, null, null, null).getItems();
        if (!ListUtils.isEmpty(replicaList)) {
            replicaList.forEach(replica -> {
                if (replica.getCurrentMaxConnetions() != null && replica.getCurrentMaxConnetions() > statConnection.get()) {
                    statConnection.set(replica.getCurrentMaxConnetions());
                }
            });
        }

        if (statConnection.get() > 0) {
            maxUserConnections = statConnection.get();
            maxConnections = maxUserConnections + rdsReservedParamSum;
        } else {
            maxUserConnections = rdsReservedParamSum;
            maxConnections += rdsReservedParamSum * 2;

        }

        // 如果系统模板中指定了最大连接数，那么计算值与定义值选最大，因此系统模板中max_connections默认值要低于最小规格max_conn+预留值
        ConfigTemplate maxConnStrOrigin = sysBaseParamTemplate.get("max_connections");
        if (maxConnStrOrigin != null) {
            if (org.apache.commons.lang.StringUtils.isNotBlank(maxConnStrOrigin.getDefaultValue())) {
                int defMaxConnections = Integer.parseInt(maxConnStrOrigin.getDefaultValue());
                maxConnections = Math.max(defMaxConnections, maxConnections);
            }
        }

        // 如果系统模板中指定了用户最大连接数，那么计算值与定义值选最大
        ConfigTemplate maxUserConnStrOrigin = sysBaseParamTemplate.get("max_user_connections");
        if (maxUserConnStrOrigin != null) {
            if (org.apache.commons.lang.StringUtils.isNotBlank(maxUserConnStrOrigin.getDefaultValue())) {
                int defMaxUserConnections = Integer.parseInt(maxUserConnStrOrigin.getDefaultValue());
                maxUserConnections = Math.max(defMaxUserConnections, maxUserConnections);
            }
        }

        // 重新检查预留连接
        if (maxConnections - rdsReservedParamSum < maxUserConnections) {
            maxConnections = maxUserConnections + rdsReservedParamSum;
        }

        Map<String, ConfigTemplate> connConfigMap = new HashMap<>();
        connConfigMap.put("max_connections", copyConfigTemplate(sysBaseParamTemplate, "max_connections", Integer.toString(maxConnections)));
        connConfigMap.put("max_user_connections", copyConfigTemplate(sysBaseParamTemplate, "max_user_connections", Integer.toString(maxUserConnections)));
        log.info("max_connections:{}, max_user_connections:{}", maxConnections, maxUserConnections);
        return connConfigMap;
    }

    /**
     * 从 mycnf_template 表中获取参数模板
     */
    private List<ConfigTemplate> getNormalBaseTemplate() throws ApiException {
        return dep.getDbaasMetaClient()
                .getDefaultmetaApi()
                .listInstanceConfigTemplates(
                        RequestSession.getRequestId(),
                        parameterMeta.getDbType(),
                        parameterMeta.getDbVersion(),
                        parameterMeta.getCharacterType(),
                        null, null
                ).getItems();
    }

    /**
     * 从 mycnf_template_extra 表中获取参数模板
     */
    private List<ConfigTemplate> getNormalBaseTemplateFromExtra(String paramGroupId) throws ApiException {
        String category = parameterMeta.getCategory();

        // category makes no sense to system parameter groups
        if (paramGroupId != null && (paramGroupId.startsWith(SYS_PARAM_GROUP_ID_PREFIX) || paramGroupId.equals(ALISQL_INNOVATION_CONFIG_GROUPID))) {
            category = null;
        }
        return dep.getDbaasMetaClient()
                .getDefaultmetaApi()
                .listInstanceExtraConfigTemplates(
                        RequestSession.getRequestId(),
                        parameterMeta.getDbType(),
                        parameterMeta.getDbVersion(),
                        null,
                        category,
                        parameterMeta.getCharacterType(),
                        paramGroupId,
                        Collections.singletonList("paramGroupId")
                ).getItems();
    }

    /**
     * 从 mycnf_template_extra 表中获取系统参数模板，并将参数合并到目标参数模板 map 中
     */
    private void mergeSysParamGroupParamsIntoMap(String paramGroupId, Map<String, ConfigTemplate> target, boolean checkEmpty) throws Exception {
        if (StringUtils.isEmpty(paramGroupId)) {
            log.info("paramGroupId is empty, skip");
            return;
        }

        List<ConfigTemplate> sysParams = getNormalBaseTemplateFromExtra(paramGroupId);
        if (sysParams.isEmpty() && checkEmpty) {
            throw new CheckException("ParameterGroup: " + paramGroupId + " is empty");
        }

        putSysParams(target, sysParams);
    }

    /**
     * 将 List<ConfigTemplate> 放入到目标的 Map<String, ConfigTemplate> 中
     */
    private void putSysParams(Map<String, ConfigTemplate> target, List<ConfigTemplate> sysParams) throws Exception {
        for (ConfigTemplate config : sysParams) {
            if (config == null || config.getIsDeleted() == null || config.getIsDeleted()) {
                continue;
            }

            // 如果指定了版本区间，当前版本是否在参数区间内
            if (StringUtils.isNotEmpty(config.getMinorVersion()) &&
                    !IntervalUtil.isInTheInterval(parameterMeta.getTag().getMinorVersion().toString(), config.getMinorVersion())) {
                deprecatedParams.put(config.getName(), config);
                continue;
            }

            deprecatedParams.remove(config.getName());
            target.put(config.getName(), config);
        }
    }

    /**
     * 从 param_groups_detail 表中获取用户自定义模板默认值，替换默认模板中的默认值
     */
    private void mergeUserParamGroupParamsIntoMap(Map<String, ConfigTemplate> target) throws ApiException {
        if (parameterMeta.getParamGroup() == null) {
            log.info("no user param group, skip");
            return;
        }

        if (parameterMeta.isSysParamGroup()) {
            log.info("sys param group, skip");
            return;
        }

        // 获取用户参数模板
        List<ParamGroupsDetail> paramConfigs = dep.getDbaasMetaClient()
                .getDefaultmetaApi().listParamGroupsDetails(
                        RequestSession.getRequestId(),
                        parameterMeta.getParamGroup().getId(),
                        1, 500
                ).getItems();

        if (paramConfigs == null) {
            log.info("user param group is empty, skip");
            return;
        }

        // 修改参数模板默认值
        for (ParamGroupsDetail config : paramConfigs) {
            if (config == null) {
                continue;
            }

            ConfigTemplate temp = target.get(config.getName());
            if (temp == null) {
                log.warn("user param '{}' in parameter group '{}' not found in base template, skip", config.getName(), parameterMeta.getParamGroup().getId());
                continue;
            }

            temp.setDefaultValue(config.getValue());
            target.put(config.getName(), temp);
        }
    }

    private void sysBaseParamCheck() {
        if (sysBaseParamTemplate == null) {
            throw new CheckException("must load sysBaseParam before");
        }
    }

    /**
     * 根据系统参数模板重置性能参数
     * 通过sync_mode，sync_binlog_mode来控制
     *
     */
    private void checkAndResetPerformanceMode(Map<String, ConfigTemplate> finalParam) throws Exception {
        String currentSyncMode = "";
        String currentSyncBinlogMode = "";
        if (parameterMeta.getLabels() != null) {
            currentSyncMode = parameterMeta.getLabels().get(LABEL_SYNC_MODE);
            currentSyncBinlogMode = parameterMeta.getLabels().get(LABEL_SYNC_BINLOG_MODE);
        }
        Map<String, String> labels = new HashMap<>();
        try {
            if (!parameterMeta.isSysParamGroup()) {
                //没有设置系统参数模板，生成默认值
                log.warn("sysParamGroup is not set");
                if (StringUtils.isEmpty(currentSyncMode)) {
                    //XDB三节点为强同步
                    labels.put(LABEL_SYNC_MODE, parameterMeta.getInstanceLevel().getCategory() == InstanceLevel.CategoryEnum.ENTERPRISE ? SYNC_MODE_NO_REVERT_SEMI_SYNC : SYNC_MODE_SEMI_SYNC);
                }
                if (StringUtils.isEmpty(currentSyncBinlogMode)) {
                    labels.put(LABEL_SYNC_BINLOG_MODE, LangUtil.getString(MYSQL_SYNC_BINLOG_MODE_VALUE_NORMAL));
                }
            }
            else {
                //设置了系统参数模板
                String paramGroupId = parameterMeta.getParamGroupId();
                Integer syncMode = SysParamGroupHelper.getSyncMode(paramGroupId);
                Integer syncBinlogMode = SysParamGroupHelper.getSyncBinlogMode(paramGroupId);

                if (!syncMode.toString().equalsIgnoreCase(currentSyncMode)) {
                    // 云上实例支持用户直接变更复制模式，跟参数模板解耦
                    if (StringUtils.isNotEmpty(currentSyncMode) && ReplicaSetMetaHelper.isAliYun(parameterMeta.getReplicaSet().getBizType())) {
                        log.info("Support modify sync mode, do not reset it.");
                    } else {
                        log.info("reset sync_mode from parameter group.");
                        labels.put(LABEL_SYNC_MODE, syncMode.toString());
                        currentSyncMode = syncMode.toString();
                    }
                }

                if (!syncBinlogMode.toString().equalsIgnoreCase(currentSyncBinlogMode)) {
                    // 云上实例支持用户直接变更落盘模式，跟参数模板解耦
                    if (StringUtils.isNotEmpty(currentSyncBinlogMode) && ReplicaSetMetaHelper.isAliYun(parameterMeta.getReplicaSet().getBizType())) {
                        log.info("Support modify sync binlog mode, do not reset it.");
                    } else {
                        log.info("reset sync_binlog_mode from parameter group.");
                        labels.put(LABEL_SYNC_BINLOG_MODE, syncBinlogMode.toString());
                        currentSyncBinlogMode = syncBinlogMode.toString();
                    }
                }
                // 高性能参数重置
                if (parameterMeta.isSysParamGroup()) {
                    if (PARAM_GROUP_PERFORMANCE_HIGH.equalsIgnoreCase(SysParamGroupHelper.getPerformanceMode(paramGroupId))) {
                        copyFromTempAndPutToDst(HIGH_PERFORMANCE_MODE, finalParam, sysBaseParamTemplate);
                    } else {
                        copyFromTempAndPutToDst(DEFAULT_PERFORMANCE_MODE, finalParam, sysBaseParamTemplate);
                    }
                }
            }
            // XDB是强同步，其它需要设置同步参数，将参数持久化到配置文件中，防止实例重启后不正确
            if (parameterMeta.getIsAliYun() && parameterMeta.getInstanceLevel().getCategory() != InstanceLevel.CategoryEnum.ENTERPRISE) {
                Optional<String> keyOptional = finalParam.keySet().stream()
                        .filter(v -> StringUtils.equals(StringUtils.removeStart(v, "loose_"), MysqlConsts.RPL_SEMI_SYNC_SLAVE_ENABLED)).findFirst();
                //异步和MGR同步方式的rpl_semi_sync_slave_enabled统一设置为OFF
                String modeValue = (StringUtils.equals(currentSyncMode, SYNC_MODE_ASYNC) || StringUtils.equals(currentSyncMode, SYNC_MODE_MGR)) ? "OFF" : "ON";
                log.info("sync_mode is [{}] set rpl_semi_sync_slave_enabled -> [{}]", currentSyncMode, modeValue);
                copyFromTempAndPutToDst(Collections.singletonMap(keyOptional.orElse(MysqlConsts.RPL_SEMI_SYNC_SLAVE_ENABLED), modeValue),
                        finalParam,
                        sysBaseParamTemplate);
            }
        } finally {
            if (!MapUtils.isEmpty(labels)) {
                log.warn("update custins_params: {}", JSON.toJSONString(labels));
                dep.getDbaasMetaClient().getDefaultmetaApi().updateReplicaSetLabels(
                        RequestSession.getRequestId(),
                        parameterMeta.getReplicaSetName(),
                        labels);
            }
        }
    }

    /**
     * 检查并重置 innodb_buffer_pool_size 参数为表达式
     */
    private void checkAndResetInnodbBufferPoolSizeToExpr(Map<String, ConfigTemplate> finalParam) throws Exception {
        if (isXEngineSwitchOn(finalParam) || parameterMeta.getTag().isAliGroup()) {
            log.info("xengine switch is ON or replicaset's biz_type is aligroup, skip");
            return;
        }

        ConfigTemplate bpConfig = finalParam.get(INNODB_BUFFER_POOL_SIZE);
        if (bpConfig == null || bpConfig.getDefaultValue() == null) {
            throw new CheckException("InnodbBufferPoolSize not found!");
        }

        // 如果BP不是表达式，需要强制设置为表达式（XEngine模式下除外）
        String targetBPSize;
        if (!bpConfig.getDefaultValue().startsWith("{")) {
            targetBPSize = parameterMeta.getIsAnalyticReadOnlyIns() ? "{DBInstanceClassMemory*1/4}" : "{DBInstanceClassMemory*3/4}";
            log.info("reset innodb_buffer_pool_size {} ==> {}", bpConfig.getDefaultValue(), targetBPSize);
            bpConfig.setDefaultValue(targetBPSize);
        }

        // 如果开启 IO 加速，需要修改数值
        if (parameterMeta.getIsIoAccelerationEnabled()) {
            // 获取对应实例规格的内存数值 单位：GB
            InstanceLevel instanceLevel = parameterMeta.getInstanceLevel();
            double insMem = instanceLevel.getMemSizeMB();
            // 获取BPE与实例内存的比值，存在于 resource 表中
            ConfigListResult configsForBPE = dep.getDbaasMetaClient().getDefaultmetaApi().listConfigs(RequestSession.getRequestId(), RATIO_OF_BPE_TO_INSTANCE_MEMORY);
            if (configsForBPE.getItems() == null || configsForBPE.getItems().isEmpty()) {
                throw new NotRetryException("can not found delay config for key " + RATIO_OF_BPE_TO_INSTANCE_MEMORY);
            }
            double ratioForBPE = Double.parseDouble(configsForBPE.getItems().get(0).getValue());
            targetBPSize = String.valueOf((long)(insMem * 1024d * 1024d * (3d/4d - ratioForBPE * 50d / (16d * 1024d))));
            log.info("reset innodb_buffer_pool_size for bpe{} ==> {}", bpConfig.getDefaultValue(), targetBPSize);
            bpConfig.setDefaultValue(targetBPSize);
        }

        forceResetInnodbBufferPoolMetaToExpr(finalParam);
    }

    /**
     * 根据系统参数模板重置性能参数
     */
    protected void checkAndResetOptEnableRdsPrivStrategy(Map<String, ConfigTemplate> finalParam) {
        String paramName = "opt_enable_rds_priv_strategy";
        if (finalParam.containsKey(paramName) && "OFF".equalsIgnoreCase(finalParam.get(paramName).getDefaultValue())) {
            log.info("force reset {} to ON", paramName);
            finalParam.get(paramName).setDefaultValue("ON");
        }

        paramName = "loose_opt_enable_rds_priv_strategy";
        if (finalParam.containsKey(paramName) && "OFF".equalsIgnoreCase(finalParam.get(paramName).getDefaultValue())) {
            log.info("force reset {} to ON", paramName);
            finalParam.get(paramName).setDefaultValue("ON");
        }
    }

    private void checkAndResetIoAccelerationParams(Map<String, ConfigTemplate> finalParam) throws Exception {
        // innodb_buffer_pool_extension_dir 因为涉及replicaId，因此在 reloadReplicaSetAllParamConfigMap 中被刷入 ;
        // loose_innodb_buffer_pool_extension 需要根据 bpe 是否开启进行判断刷入的参数
        // 大版本不为8.0的直接跳过
        if (!"8.0".equalsIgnoreCase(parameterMeta.getDbVersion())) {
            log.info("dbVersion is: {}, do not need to check or reset BPE params", parameterMeta.getDbVersion());
            return;
        }

        if (parameterMeta.getIsIoAccelerationEnabled()) {
            // 获取对应实例规格的内存数值 单位：GB
            InstanceLevel instanceLevel = parameterMeta.getInstanceLevel();
            long insMem = instanceLevel.getMemSizeMB() / 1024;

            // 获取实例超卖比率，存在于 resource 表中
            ConfigListResult configs = dep.getDbaasMetaClient().getDefaultmetaApi().listConfigs(RequestSession.getRequestId(), BUFFER_POOL_EXTENSION_OVER_SOLD_PERSENT);
            if (configs.getItems() == null || configs.getItems().isEmpty()) {
                throw new NotRetryException("can not found delay config for key " + BUFFER_POOL_EXTENSION_OVER_SOLD_PERSENT);
            }
            long overSold = Long.parseLong(configs.getItems().get(0).getValue());

            // 获取BPE与实例内存的比值，存在于 resource 表中
            ConfigListResult configsForBPE = dep.getDbaasMetaClient().getDefaultmetaApi().listConfigs(RequestSession.getRequestId(), RATIO_OF_BPE_TO_INSTANCE_MEMORY);
            if (configsForBPE.getItems() == null || configsForBPE.getItems().isEmpty()) {
                throw new NotRetryException("can not found delay config for key " + RATIO_OF_BPE_TO_INSTANCE_MEMORY);
            }
            long ratioForBPE = Long.parseLong(configsForBPE.getItems().get(0).getValue());

            // 计算 BPE 相关参数值
            long loose_innodb_buffer_pool_extension_size = insMem * ratioForBPE * 1024L * 1024L * 1024L;

            long innodb_buffer_pool_extension_io_read_capacity =
                    (long) (IO_READ_PER_GB * loose_innodb_buffer_pool_extension_size * overSold / 1024L / 1024L / 1024L);

            long  innodb_buffer_pool_extension_io_write_capacity =
                    IO_WRITE_PER_GB * loose_innodb_buffer_pool_extension_size * overSold / 1024L / 1024L / 1024L;

            finalParam.get(MYSQL_BPE_INNODB_BUFFER_POOL_EXTENSION).setDefaultValue(TURN_ON_BUFFER_POOL_EXTENSION);
            finalParam.get(MYSQL_BPE_INNODB_BUFFER_POOL_EXTENSION_DIR).setDefaultValue(BUFFER_POOL_EXTENSION_DIR);
            finalParam.get(MYSQL_BPE_INNODB_BUFFER_POOL_EXTENSION_SIZE).setDefaultValue(String.valueOf(loose_innodb_buffer_pool_extension_size));
            finalParam.get(MYSQL_BPE_INNODB_BUFFER_POOL_EXTENSION_FLUSH_DIRTY).setDefaultValue(FLUSH_DIRTY_PAGE_TO_BUFFER_POOL_EXTENSION);
            finalParam.get(MYSQL_BPE_INNODB_BUFFER_POOL_EXTENSION_IO_READ_CAPACITY).setDefaultValue(String.valueOf(innodb_buffer_pool_extension_io_read_capacity));
            finalParam.get(MYSQL_BPE_INNODB_BUFFER_POOL_EXTENSION_IO_WRITE_CAPACITY).setDefaultValue(String.valueOf(innodb_buffer_pool_extension_io_write_capacity));
            log.info("set loose_innodb_buffer_pool_extension to ON.");
        } else {
            if (finalParam.containsKey(MYSQL_BPE_INNODB_BUFFER_POOL_EXTENSION)) {
                finalParam.get(MYSQL_BPE_INNODB_BUFFER_POOL_EXTENSION).setDefaultValue(TURN_OFF_BUFFER_POOL_EXTENSION);
            }
            log.info("set loose_innodb_buffer_pool_extension to OFF.");
        }
    }

    private void checkAndResetDuckDBParams(Map<String, ConfigTemplate> finalParam) throws Exception {
        if (parameterMeta.getIsAnalyticReadOnlyIns()) {
            // 基础DuckDB参数
            finalParam.put(MYSQL_DUCKDB_MODE, copyConfigTemplate(sysBaseParamTemplate, MYSQL_DUCKDB_MODE, DUCKDB_MODE_DEFAULT_VALUE));
            finalParam.put(MYSQL_DUCKDB_CONVERT_ALL_AT_STARTUP, copyConfigTemplate(sysBaseParamTemplate, MYSQL_DUCKDB_CONVERT_ALL_AT_STARTUP, DUCKDB_CONVERT_ALL_AT_STARTUP_DEFAULT_VALUE));
            finalParam.put(MYSQL_DUCKDB_FORCE_INNODB_TO_DUCKDB, copyConfigTemplate(sysBaseParamTemplate, MYSQL_DUCKDB_FORCE_INNODB_TO_DUCKDB, FORCE_INNODB_TO_DUCKDB_DEFAULT_VALUE));

            // 目录和存储相关参数
            finalParam.put(MYSQL_DUCKDB_TEMP_DIRECTORY, copyConfigTemplate(sysBaseParamTemplate, MYSQL_DUCKDB_TEMP_DIRECTORY, DUCKDB_DUCKDB_TEMP_DIRECTORY));
            finalParam.put(MYSQL_DUCKDB_MAX_TEMP_DIRECTORY_SIZE, copyConfigTemplate(sysBaseParamTemplate, MYSQL_DUCKDB_MAX_TEMP_DIRECTORY_SIZE, DUCKDB_MAX_TEMP_DIRECTORY_SIZE_DEFAULT_VALUE));
            finalParam.put(MYSQL_DUCKDB_USE_DIRECT_IO, copyConfigTemplate(sysBaseParamTemplate, MYSQL_DUCKDB_USE_DIRECT_IO, DUCKDB_USE_DIRECT_IO_DEFAULT_VALUE));

            // 线程和性能相关参数
            finalParam.put(MYSQL_DUCKDB_THREADS, copyConfigTemplate(sysBaseParamTemplate, MYSQL_DUCKDB_THREADS, DUCKDB_THREADS_DEFAULT_VALUE));
            finalParam.put(MYSQL_DUCKDB_CONVERT_ALL_AT_STARTUP_IGNORE_ERROR, copyConfigTemplate(sysBaseParamTemplate, MYSQL_DUCKDB_CONVERT_ALL_AT_STARTUP_IGNORE_ERROR, DUCKDB_CONVERT_ALL_AT_STARTUP_IGNORE_ERROR_DEFAULT_VALUE));

            // 批处理和优化相关参数
            finalParam.put(MYSQL_DUCKDB_COPY_DDL_IN_BATCH, copyConfigTemplate(sysBaseParamTemplate, MYSQL_DUCKDB_COPY_DDL_IN_BATCH, DUCKDB_COPY_DDL_IN_BATCH_DEFAULT_VALUE));
            finalParam.put(MYSQL_DUCKDB_DML_IN_BATCH, copyConfigTemplate(sysBaseParamTemplate, MYSQL_DUCKDB_DML_IN_BATCH, DUCKDB_DML_IN_BATCH_DEFAULT_VALUE));
            finalParam.put(MYSQL_DUCKDB_REQUIRE_PRIMARY_KEY, copyConfigTemplate(sysBaseParamTemplate, MYSQL_DUCKDB_REQUIRE_PRIMARY_KEY, DUCKDB_REQUIRE_PRIMARY_KEY_DEFAULT_VALUE));
            finalParam.put(MYSQL_DUCKDB_UPDATE_MODIFIED_COLUMN_ONLY, copyConfigTemplate(sysBaseParamTemplate, MYSQL_DUCKDB_UPDATE_MODIFIED_COLUMN_ONLY, DUCKDB_UPDATE_MODIFIED_COLUMN_ONLY_DEFAULT_VALUE));

            // 获取实例规格信息
            InstanceLevel instanceLevel = parameterMeta.getInstanceLevel();

            // 计算 duckdb_memory_limit = 内存规格 * 0.75 (单位：字节)
            Double insMemMB = instanceLevel.getMemSizeMB().doubleValue();
            long memoryLimitMB = Math.round(insMemMB * 0.75);
            long memoryLimitBytes = memoryLimitMB * 1024 * 1024; // 转换为字节
            String memoryLimitValue = String.valueOf(memoryLimitBytes);
            finalParam.put(MYSQL_DUCKDB_MEMORY_LIMIT, copyConfigTemplate(sysBaseParamTemplate, MYSQL_DUCKDB_MEMORY_LIMIT, memoryLimitValue));
            log.info("set duckdb_memory_limit to {} bytes ({}MB)", memoryLimitValue, memoryLimitMB);

            // 计算 duckdb_convert_all_at_startup_threads = CPU cores * 2
            Double cpuCores = instanceLevel.getCpuCores().doubleValue();
            int threadsValue = (int) Math.round(cpuCores * 2);
            finalParam.put(MYSQL_DUCKDB_CONVERT_ALL_AT_STARTUP_THREADS, copyConfigTemplate(sysBaseParamTemplate, MYSQL_DUCKDB_CONVERT_ALL_AT_STARTUP_THREADS, String.valueOf(threadsValue)));
            log.info("set duckdb_convert_all_at_startup_threads to {}", threadsValue);

            log.info("set all DuckDB parameters for analytical read-only instance.");
        }
    }

    public void checkAndResetCollationServer(Map<String, ConfigTemplate> sysParamTemplate, Map<String, ConfigTemplate> finalParam) {
        //如果本来就不包含，跳过，mariadb不包括
        if (!finalParam.containsKey(COLLATION_SERVER_PARAM_NAME)) {
            return;
        }

        //sys里面肯定有相应配置
        String sysCharacter = sysParamTemplate.get(CHARACTER_SET_SERVER_PARAM_NAME).getDefaultValue();
        String sysCollation = sysParamTemplate.get(COLLATION_SERVER_PARAM_NAME).getDefaultValue();

        //final里面覆盖了sys，所以最少是和sys相同的。
        String finalCharacter = finalParam.get(CHARACTER_SET_SERVER_PARAM_NAME).getDefaultValue();
        String finalCollation = finalParam.get(COLLATION_SERVER_PARAM_NAME).getDefaultValue();

        // 1.不修改character_set_server，只修改了collation_server，参数变更校验过，不需要关心
        // 2.修改character_set_server，collation_server会强制改掉。历史上之前允许不改collation_server,这种要处理掉
        if (!finalCharacter.equalsIgnoreCase(sysCharacter) && finalCollation.equalsIgnoreCase(sysCollation)) {
            finalParam.remove(COLLATION_SERVER_PARAM_NAME);
        }
    }

    private void checkAndResetColdDataEnabled(Map<String, ConfigTemplate> finalParam) {
        // 此处只loose_oss_enabled, 其余oss参数均和节点相关，在customParameterForReplica完成
        // 大版本不为8.0的直接跳过
        if (!"8.0".equalsIgnoreCase(parameterMeta.getDbVersion())) {
            log.info("dbVersion is: {}, do not need to check or reset loose_oss_enabled param", parameterMeta.getDbVersion());
            return;
        }
        if (finalParam.containsKey(MYSQL_OSS_ENABLED)) {
            if (parameterMeta.getIsColdDataEnabled()) {
                finalParam.get(MYSQL_OSS_ENABLED).setDefaultValue(PARAM_ON);
                log.info("set loose_oss_enabled to ON.");
            } else {
                finalParam.get(MYSQL_OSS_ENABLED).setDefaultValue(PARAM_OFF);
                log.info("set loose_oss_enabled to OFF.");
            }
        }
    }


    /**
     * 检查并重置innodb_log_file_size参数
     * 如果实例为云上实例，且innodb_log_file_size大于实例磁盘大小，则重置innodb_log_file_size为默认值1GB
     *
     * @param params
     */
    private void checkAndResetLogFileSize(Map<String, ConfigTemplate> params) {
        // 非云上实例直接返回
        if (!ReplicaSetMetaHelper.isAliYun(parameterMeta.getReplicaSet().getBizType())) {
            return;
        }

        if (Boolean.TRUE.equals(parameterMeta.getReplicaSet().getIsTmp()) || parameterMeta.getReplicaSet().getStatus() != ReplicaSet.StatusEnum.CREATING) {
            //当前只处理非临时实例，且状态为创建中的实例，其他状态不处理，防止错误处理
            return;
        }
        if (!params.containsKey(INNODB_LOG_FILE_SIZE)) {
            return;
        }
        try {
            Integer fileInGroups = 2;  //default group is 2
            if (params.containsKey(INNODB_LOG_FILES_IN_GROUP)) {
                ConfigTemplate config = params.get(INNODB_LOG_FILES_IN_GROUP);
                fileInGroups = LangUtil.getInteger(config.getDefaultValue(), 2);
            }
            long logFileSizeBytes = SizeUnitTransTool.trans(params.get(INNODB_LOG_FILE_SIZE).getDefaultValue(), "B").longValue();
            long diskSizeBytes = (long) parameterMeta.getReplicaSet().getDiskSizeMB() * 1024 * 1024;
            if (diskSizeBytes <= (fileInGroups * logFileSizeBytes)) {
                //如果实例的存储大小小于redo的总大小，会导致实例无法启动，这里做下处理，将redo设置为默认值1GB
                long defaultValue = 1024 * 1024 * 1024;
                params.put(INNODB_LOG_FILE_SIZE, copyConfigTemplate(sysBaseParamTemplate, INNODB_LOG_FILE_SIZE, LangUtil.getString(defaultValue)));
                log.info("current's replicaSet diskSize is {}MB, reset set innodb_log_file_size total size is {}MB, reset to default value 1GB.",
                        parameterMeta.getReplicaSet().getDiskSizeMB(), logFileSizeBytes / 1024 / 1024);
            }
        } catch (Exception e) {
            log.error("check innodb_log_file_size error, ignore it, {}", e.getMessage());
        }
    }


    private void checkAndResetDoubleWrite(Map<String, ConfigTemplate> finalParam) throws Exception {
        // 非云上实例直接返回
        if (!ReplicaSetMetaHelper.isAliYun(parameterMeta.getReplicaSet().getBizType())) {
            return;
        }

        String doubleWriteParamName = INNODB_DOUBLE_WRITE;
        //  获取是否打开写优化参数
        if (parameterMeta.isMatch(BuildParamParameter.LabelKey.OPTIMIZED_WRITES)) {
            finalParam.put(doubleWriteParamName, copyConfigTemplate(sysBaseParamTemplate, doubleWriteParamName, "0"));
            log.info("set innodb_doublewrite to 0.");
        } else {
            finalParam.put(doubleWriteParamName, copyConfigTemplate(sysBaseParamTemplate, doubleWriteParamName, "1"));
            log.info("set innodb_doublewrite to 1.");
        }


//        /**
//         * double write参数由备份集信息获取
//         * arm x86 互转场景下double write参数默认打开, 防止出现以下两个场景
//         * 1.arm 转 x86 后x86关闭了doublewrite导致数据存在丢失风险
//         * 2.x86 转arm 后由于x86云盘不符合double write关闭条件， 导致实例pod无法拉起
//         * 但如果是x86架构，该参数直接设置为1
//         */
        //todo：这里逻辑需要重点优化
        if (customer != null) {
            customer.customParameterFromBackupSet(finalParam);
        }
//        if(parameterMeta.isMatch(BuildParamParameter.LabelKey.ARCH_CHANGED) || !dep.getReplicaMetaHelper().isArm(parameterMeta.getInstanceLevel())){
//            finalParam.put(doubleWriteParamName, copyConfigTemplate(sysBaseParamTemplate, doubleWriteParamName, "1"));
//            log.info("set innodb_doublewrite to 1.");
//        }
    }

    /**
     * 检查 xengine 开关是否开启
     */
    private static boolean isXEngineSwitchOn(Map<String, ConfigTemplate> params) {
        ConfigTemplate xEngineSwitchConfig = params.get("loose_xengine");
        if (xEngineSwitchConfig == null) {
            return false;
        }

        String xEngineSwitch = xEngineSwitchConfig.getDefaultValue();
        return "1".equals(xEngineSwitch) || "ON".equalsIgnoreCase(xEngineSwitch);
    }

    /**
     * 检查实例参数，将BP强制设置为表达式
     * 将没有修改过BP的实例的BP大小修正设置为75%
     */
    private ParamBuilder forceResetInnodbBufferPoolMetaToExpr(Map<String, ConfigTemplate> finalParams) throws Exception {
        if (isXEngineSwitchOn(finalParams)) {
            return this;
        }

        DefaultApi metaApi = dep.getDbaasMetaClient().getDefaultmetaApi();
        String replicaSetName = parameterMeta.getReplicaSetName();
        String requestId = RequestSession.getRequestId();
        Map<String, String> replicaSetConfig = metaApi.listReplicaSetConfigs(
                        RequestSession.getRequestId(), replicaSetName, null, null, null, null)
                .getItems();
        if (replicaSetConfig == null || replicaSetConfig.size() == 0) {
            return this;
        }

        String innodbBufferPoolSize = replicaSetConfig.get(ParamConst.INNODB_BUFFER_POOL_SIZE);
        if (innodbBufferPoolSize != null && innodbBufferPoolSize.trim().startsWith("{")) {
            return this;
        }

        if (StringUtils.isEmpty(innodbBufferPoolSize) && Boolean.TRUE.equals(parameterMeta.getReplicaSet().getIsTmp())) {
            log.info("current's replicaSet is tmp and innodb_buffer_pool_size is empty, skip to reset it");
            return this;
        }

        String targetBPSize = parameterMeta.getIsAnalyticReadOnlyIns() ? "{DBInstanceClassMemory*1/4}" : "{DBInstanceClassMemory*3/4}";

        List<ConfigChangeLog> changeLogs = ParameterHelper.getChangeLogApplied(metaApi, replicaSetName);
        if (changeLogs != null && !changeLogs.isEmpty()) {
            for (ConfigChangeLog changeLog : changeLogs) {
                if (ParamConst.INNODB_BUFFER_POOL_SIZE.equals(changeLog.getName()) &&
                        Objects.requireNonNull(changeLog.getNewValue()).startsWith("{")) {
                    log.info("\ninnodb_buffer_pool_size is in change log ==> {}, skip reset", changeLog.getNewValue());
                    return this;
                }
            }
        }

        Map<String, String> bpConfig = new HashMap<>();
        log.info("\nreset innodb_buffer_pool_size meta {} to {}", innodbBufferPoolSize, targetBPSize);
        bpConfig.put(ParamConst.INNODB_BUFFER_POOL_SIZE, targetBPSize);
        metaApi.updateReplicaSetConfigs(requestId, replicaSetName, bpConfig, null);

        reload();
        return this;
    }

    public Map<String, ConfigTemplate> getSysBaseParamTemplate() {
        return sysBaseParamTemplate;
    }

    /**
     * 获取实例预留连接数值
     */
    public Integer getReservedConnectionsSum() throws CheckException {
        sysBaseParamCheck();
        String rdsReservedParamName;
        if (parameterMeta.getDbType().equals(DB_TYPE_MYSQL)) {
            rdsReservedParamName = MYSQL_VERSION_57.equals(parameterMeta.getDbVersion()) ? "rds_reserved_connections" :
                    MYSQL_VERSION_80.equals(parameterMeta.getDbVersion()) ? "maintain_max_connections" : "reserved_connections";
        } else if (parameterMeta.getDbType().equals(DB_TYPE_MARIADB)) {
            rdsReservedParamName = "rds_reserved_connections";
        } else {
            throw new UnsupportedOperationException("Not supported dbType: " + parameterMeta.getDbType());
        }
        String killConnectionName = "loose_rds_kill_connections";
        if (!sysBaseParamTemplate.containsKey(rdsReservedParamName)) {
            log.error("sysBaseParamTemplate not contains key: " + rdsReservedParamName);
            throw new CheckException("sysBaseParamTemplate not contains key: " + rdsReservedParamName);
        }
        if (!sysBaseParamTemplate.containsKey(killConnectionName)) {
            log.error("sysBaseParamTemplate not contains key: " + killConnectionName);
            throw new CheckException("sysBaseParamTemplate not contains key: " + killConnectionName);
        }
        String reservedConnections = sysBaseParamTemplate.get(rdsReservedParamName).getDefaultValue();
        String killConnections = sysBaseParamTemplate.get(killConnectionName).getDefaultValue();
        if (StringUtils.isEmpty(reservedConnections) || StringUtils.isEmpty(killConnections)) {
            throw new CheckException("reserved_connections or loose_rds_kill_connections param not found");
        }
        log.info("replicaSet [{}] rdsReservedParamName is [{}], value is [{}], killConnections param value is [{}]"
                , parameterMeta.getReplicaSetName(), rdsReservedParamName, reservedConnections, killConnections);
        return Integer.parseInt(reservedConnections) + Integer.parseInt(killConnections);
    }

    /**
     * 对chang_log中连接数进行处理，加上预留值
     */
    private void handledMaxConnInChangeLog(String oriMaxConnValue) throws CheckException {
        try {
            Integer newValue = Integer.parseInt(oriMaxConnValue) + getReservedConnectionsSum();
            userChangedConfigs.put(MAX_CONNECTIONS, copyConfigTemplate(sysBaseParamTemplate, MAX_CONNECTIONS, String.valueOf(newValue)));
            log.info("handle change log max connections value from [{}] to [{}]", oriMaxConnValue, newValue);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new CheckException("handle change log max connections param failed");
        }
    }

    /**
     * 从基础参数模板中克隆参数对象
     */



    /**
     * 检查生成的参数中是否同时包含带loose和不带loose的参数
     */
    protected static void duplicateParameterCheck(Map<String, ConfigTemplate> params) {
        List<String> duplicateKeys = new ArrayList<>();
        params.forEach((key, value) -> {
            String prefix = key.startsWith(PARAMETER_LOOSE_PREFIX) ? "" : PARAMETER_LOOSE_PREFIX;
            String otherKey = prefix + key.replaceFirst(PARAMETER_LOOSE_PREFIX, "");
            ConfigTemplate paramValue = params.get(otherKey);
            if (paramValue != null && paramValue.getDefaultValue() != null && !paramValue.getDefaultValue().equalsIgnoreCase(value.getDefaultValue())) {
                duplicateKeys.add(otherKey + "(" + paramValue.getDefaultValue() + ") <=> " + key + "(" + value.getDefaultValue() + ")");
            }
        });

        if (duplicateKeys.isEmpty()) {
            return;
        }

        throw new CheckException(String.format("Duplicate parameters have different value: %s", String.join(",", duplicateKeys)));
    }

    /**
     * 移除参数中的非loose参数
     */
    protected static Map<String, ConfigTemplate> removeDuplicateParameter(Map<String, ConfigTemplate> params) {
        Map<String, ConfigTemplate> newParams = new HashMap<>();
        Set<String> removedParam = new HashSet<>();
        for (String key : params.keySet()) {
            String nameWithOutLoose = StringUtils.remove(key, PARAMETER_LOOSE_PREFIX);
            if (!key.equalsIgnoreCase(nameWithOutLoose) && !DUP_PARAM_BLACK_LIST.contains(nameWithOutLoose)) {
                newParams.put(key, params.get(key));
                removedParam.add(nameWithOutLoose);
                continue;
            }
            newParams.put(key, params.get(key));
        }

        if (!removedParam.isEmpty()) {
            removedParam.forEach(k -> newParams.remove(k));
            log.info("removeDuplicateParameter: {}", JSONObject.toJSONString(removedParam));
        }

        return newParams;
    }


    /**
     * 将 src 中的值转换为对应的模板对象，更新到 dst 中
     */
    protected void copyFromTempAndPutToDst(Map<String, String> src,
                                           Map<String, ConfigTemplate> dst,
                                           Map<String, ConfigTemplate> tmp) {
        for (String name : src.keySet()) {
            String value = src.get(name);
            boolean startWithLoose = StringUtils.startsWith(name, PARAMETER_LOOSE_PREFIX);
            String otherParam = startWithLoose ? StringUtils.remove(name, PARAMETER_LOOSE_PREFIX) : PARAMETER_LOOSE_PREFIX + name;
            dst.put(name, copyConfigTemplate(tmp, name, value));
            if (tmp.containsKey(otherParam)) {
                log.info("param {} get otherParam {}.", name, otherParam);
                dst.put(otherParam, copyConfigTemplate(tmp, otherParam, value));
            }
        }
    }

    /**
     * 删除不在系统模版中的参数
     * @return
     */
    public void removeParamsNotInSysTemplate(Map<String, ConfigTemplate> params) {
        if (MapUtils.isEmpty(params)) {
            log.info("params is empty.");
            return;
        }
        Map<String, ConfigTemplate> originParams = new HashMap<>(params);
        for (String name: originParams.keySet()) {
            if (sysBaseParamTemplate.containsKey(name)) {
                continue;
            }
            ConfigTemplate configTemplate = originParams.get(name);

            // 不在sysBaseParamTemplate的情况: 判断loose是否存在，避免少参数
            boolean startWithLoose = StringUtils.startsWith(name, PARAMETER_LOOSE_PREFIX);
            String otherParam = startWithLoose ? StringUtils.remove(name, PARAMETER_LOOSE_PREFIX) : PARAMETER_LOOSE_PREFIX + name;
            if (!sysBaseParamTemplate.containsKey(otherParam)) {
                params.remove(name);
                deprecatedParams.put(name, configTemplate);
                log.info("param = [{}] is not in sys base param template.", name);
                continue;
            }
            params.remove(name);
            params.put(otherParam, configTemplate);
        }
    }

    public void handleParamsForMajorVersionDiff(String dbVersion) throws Exception {
        //57和80的参数名称有差异，做映射
        if (MYSQL_VERSION_80.equalsIgnoreCase(dbVersion)) {
            PARAM_57_TO_80_PARAM_DIFF_MAPPING.forEach((paramName57, paramName80) -> {
                if (currentInstanceParam.containsKey(paramName57)) {
                    log.info("[loose_rds_kill_user_list] old value:{},new value:{}", currentInstanceParam.get(paramName80), currentInstanceParam.get(paramName57));
                    currentInstanceParam.put(paramName80, currentInstanceParam.get(paramName57));
                }
            });
        }
        // 大版本升级：原实例的mycnf_custinstance和change_log里面可能存在不在新版本中的参数
        removeParamsNotInSysTemplate(currentInstanceParam);
        removeParamsNotInSysTemplate(userChangedConfigs);

        // maintain_user_list 处理：以新的大版本为准
        if (sysBaseParamTemplate.containsKey(PARAMS_MAINTAIN_USER_LIST) && currentInstanceParam.containsKey(PARAMS_MAINTAIN_USER_LIST)) {
            ConfigTemplate userListInCurr = currentInstanceParam.get(PARAMS_MAINTAIN_USER_LIST);
            userListInCurr.setDefaultValue(sysBaseParamTemplate.get(PARAMS_MAINTAIN_USER_LIST).getDefaultValue());
            currentInstanceParam.put(userListInCurr.getName(), userListInCurr);
        }

        //sql_mode处理
        Set<String> legalParams =PARAM_SQL_MODE_LEGAL_PARAMS.get(dbVersion);
        if (currentInstanceParam.containsKey(PARAMS_SQL_MODE) && sysBaseParamTemplate.containsKey(PARAMS_SQL_MODE)) {
            if(legalParams==null){
                currentInstanceParam.remove(PARAMS_SQL_MODE);
            }else {
                ConfigTemplate sqlModeInCurr = currentInstanceParam.get(PARAMS_SQL_MODE);
                if (!StringUtils.isBlank(sqlModeInCurr.getDefaultValue())) {
                    Set<String> currentParams = new HashSet<>(Arrays.asList(sqlModeInCurr.getDefaultValue().split(",")));
                    //取当前参数和合法参数的交集
                    currentParams.retainAll(legalParams);
                    sqlModeInCurr.setDefaultValue(StringUtils.join(currentParams.toArray(),","));
                }
                currentInstanceParam.put(sqlModeInCurr.getName(), sqlModeInCurr);
            }
        }
        if (userChangedConfigs.containsKey(PARAMS_SQL_MODE)) {
            if(legalParams==null){
                userChangedConfigs.remove(PARAMS_SQL_MODE);
            }else {
                ConfigTemplate sqlModeInChangeLog = userChangedConfigs.get(PARAMS_SQL_MODE);
                Set<String> currentParams = new HashSet<>(Arrays.asList(sqlModeInChangeLog.getDefaultValue().split(",")));
                //取当前参数和合法参数的交集
                currentParams.retainAll(legalParams);
                sqlModeInChangeLog.setDefaultValue(StringUtils.join(currentParams.toArray(), ","));
                userChangedConfigs.put(sqlModeInChangeLog.getName(), sqlModeInChangeLog);
            }
        }


        //loose_keyring_rds_kms_agent_cmd 处理：以新的大版本为准
        if (currentInstanceParam.containsKey(LOOSE_KEYRING_RDS_KMS_AGENT_CMD)) {
            ConfigTemplate kmsAgentCmdInCurr = currentInstanceParam.get(LOOSE_KEYRING_RDS_KMS_AGENT_CMD);
            kmsAgentCmdInCurr.setDefaultValue(sysBaseParamTemplate.get(LOOSE_KEYRING_RDS_KMS_AGENT_CMD).getDefaultValue());
            currentInstanceParam.put(kmsAgentCmdInCurr.getName(), kmsAgentCmdInCurr);
        }

    }

    public Map<String, ConfigTemplate> getCurrentInstanceParam() {
        return this.currentInstanceParam;
    }

    public Map<String, ConfigTemplate> getDeprecatedParams() {
        return this.deprecatedParams;
    }

    public Set<String> getParamsServerlessNoSupportChange() throws ApiException {
        Set<String> paramsNoSupport = new HashSet<>(Arrays.asList(
                "innodb_buffer_pool_chunk_size",
                "innodb_buffer_pool_instances",
                "default_authentication_plugin"
        ));

        // 注意此处配置为空也会覆盖默认值
        ConfigListResult configListResult = dep.getDbaasMetaClient().getDefaultmetaApi().listConfigs(
                RequestSession.getRequestId(), SERVERLESS_KERNEL_PARAMS_NO_SUPPORT_EDIT);
        if (!CollectionUtils.isEmpty(configListResult.getItems())) {
            com.aliyun.apsaradb.dbaasmetaapi.model.Config config = configListResult.getItems().get(0);
            paramsNoSupport = new HashSet<>(Arrays.asList(config.getValue().split(",")));
        }
        log.info("params serverless not support change: {}", paramsNoSupport);

        return paramsNoSupport;
    }

}
