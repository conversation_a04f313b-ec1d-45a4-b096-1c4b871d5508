package com.aliyun.app.activityprovider.base.param.param;

import com.aliyun.app.activityprovider.base.param.component.ParamDependency;
import com.aliyun.app.activityprovider.common.consts.CommonConsts;
import com.aliyun.app.activityprovider.common.exception.CheckException;
import com.aliyun.app.activityprovider.common.utils.LangUtil;
import com.aliyun.app.activityprovider.meta.ReplicaSetMetaHelper;
import com.aliyun.app.activityprovider.web.RequestSession;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.aliyun.app.activityprovider.base.param.utils.ParamConst.*;
import static com.aliyun.app.activityprovider.base.param.utils.paramgroup.SysParamGroupHelper.SYS_PARAM_GROUP_ID_PREFIX;
import static com.aliyun.app.activityprovider.common.consts.CommonConsts.*;

/**
 * <AUTHOR> on 2020/12/31.
 */
@Slf4j
public class ParamMetaBuilder {


    /**
     * 初始化ParamMeta元信息
     */
    public static BuildParamParameter initParameterMeta(ParamDependency dep,
                                                        ReplicaSet replicaSet,
                                                        String targetClassCode,
                                                        Double rcu,
                                                        String paramGroupId) throws Exception {
        if (replicaSet == null) {
            return null;
        }

        String requestId = RequestSession.getRequestId();
        ReplicaSet primaryReplicaSet = replicaSet;
        if (replicaSet.getInsType() == ReplicaSet.InsTypeEnum.TMP) {
            primaryReplicaSet = dep.getDbaasMetaClient().getDefaultmetaApi()
                    .getReplicaSet(requestId, replicaSet.getPrimaryInsName(), null);
        }

        Map<String, String> labels = new HashMap<>(dep.getDbaasMetaClient().getDefaultmetaApi().listReplicaSetLabels(requestId, primaryReplicaSet.getName()));
        if (!Objects.requireNonNull(primaryReplicaSet.getName()).equalsIgnoreCase(replicaSet.getName())) {
            labels.putAll(dep.getDbaasMetaClient().getDefaultmetaApi()
                    .listReplicaSetLabels(requestId, replicaSet.getName()));
        }

        BuildParamParameter requestMeta = new BuildParamParameter();
        requestMeta.setReplicaSetName(replicaSet.getName());
        requestMeta.setDbType(replicaSet.getService());
        requestMeta.setDbVersion(replicaSet.getServiceVersion());
        requestMeta.setCategory(requestMeta.getCategory());
        requestMeta.setReplicaSet(replicaSet);
        requestMeta.setLabels(labels);
        if (replicaSet.getKindCode() != null && CommonConsts.KIND_CODE_K8S.equals(replicaSet.getKindCode())) {
            log.info("get kernel info from tag: kind_code:{}", replicaSet.getKindCode());
            requestMeta.setTag(KernelTagDesc.getKernelTagDesc(dep, replicaSet));
        } else {
            log.info("get kernel info for pengine: kind_code:{}", replicaSet.getKindCode());
            requestMeta.setTag(KernelTagDesc.getTagForPEngineIns(dep, replicaSet, labels));
        }

        // 设置参数模板
        if (StringUtils.isEmpty(paramGroupId) && MapUtils.isNotEmpty(labels) && StringUtils.isEmpty(labels.get(CUSTINS_PARAM_UPGRADE_MAJOR_VERSION_LABEL))) {
            paramGroupId = labels.get(CUSTINS_PARAM_PARAM_GROUP_ID);
        }
        requestMeta.setParamGroupId(paramGroupId);
        if (!StringUtils.isEmpty(paramGroupId)) {
            boolean isSys = StringUtils.startsWith(paramGroupId, SYS_PARAM_GROUP_ID_PREFIX);
            ParamGroup pg = isSys ?
                    getSystemParamGroup(dep, replicaSet, paramGroupId) :
                    getUserParamGroup(dep, replicaSet, paramGroupId);
            requestMeta.setParamGroup(pg);
            log.info("param group is {}", pg);
        }

        // 设置规格
        ReplicaSet readPrimaryReplicaSet = dep.getReplicaMetaHelper().getPrimaryReplicaSet(replicaSet.getName());
        targetClassCode = StringUtils.isNotBlank(targetClassCode) ? targetClassCode : replicaSet.getClassCode();
        requestMeta.setClassCode(targetClassCode);
        requestMeta.setCharacterType("normal");

        // 判断是否为未开启 IO 加速的实例
        requestMeta.setIsIoAccelerationEnabled(false);
        String replicaSetName = replicaSet.getName();
        String ioAccelerationEnabled = dep.getDbaasMetaClient().getDefaultmetaApi().getReplicaSetLabel(RequestSession.getRequestId(), replicaSetName, IO_ACCELERATION_ENABLED);
        log.info("ioAccelerationEnabled is {}", ioAccelerationEnabled);
        if (StringUtils.equals(IO_ACCELERATION_ENABLED_ON, ioAccelerationEnabled)) {
            requestMeta.setIsIoAccelerationEnabled(true);
            log.info("The instance is ioAccelerationEnabled, set FLAG IsIoAccelerationEnabled to TRUE");
        }

        // 判断是否为分析型只读实例
        requestMeta.setIsAnalyticReadOnlyIns(false);
        String isAnalyticReadOnlyIns = dep.getDbaasMetaClient().getDefaultmetaApi().getReplicaSetLabel(RequestSession.getRequestId(), replicaSetName, ANALYTIC_READ_ONLY_INS_FLAG);
        log.info("isAnalyticReadOnlyIns is {}", isAnalyticReadOnlyIns);
        if(StringUtils.isNotBlank(isAnalyticReadOnlyIns) && Boolean.parseBoolean(isAnalyticReadOnlyIns)){
            // 不仅需要通过label判断，还需要判断这个实例是不是只读实例
            boolean isReadOnlyInstance = dep.getReplicaMetaHelper().isReadIns(replicaSet);
            log.info("isReadOnlyInstance is {}", isReadOnlyInstance);
            if (isReadOnlyInstance) {
                requestMeta.setIsAnalyticReadOnlyIns(true);
                log.info("The instance is DuckDB and is read-only instance, set FLAG isAnalyticReadOnlyIns to TRUE");
            } else {
                log.warn("The instance has isDuckDB label but is not a read-only instance, skip setting isAnalyticReadOnlyIns flag");
            }
        }

        // set coldDataEnabled by custins_parm
        requestMeta.setIsColdDataEnabled(false);
        if(MapUtils.isNotEmpty(labels)&&labels.containsKey(CUSTINS_PARAM_NAME_COLD_DATA_ENABLED)){
            String coldDataEnabledStr = labels.get(CUSTINS_PARAM_NAME_COLD_DATA_ENABLED);
            if(StringUtils.isNotBlank(coldDataEnabledStr)&&Boolean.parseBoolean(coldDataEnabledStr)){
                requestMeta.setIsColdDataEnabled(true);
            }
        }

        // set tde_enabled by custins_parm
        requestMeta.setTdeEnabled(false);
        if (MapUtils.isNotEmpty(labels) && labels.containsKey(TDE_ENABLED) && StringUtils.equalsIgnoreCase(labels.get(TDE_ENABLED), "1")) {
            requestMeta.setTdeEnabled(true);
        }

        // 避坑：三节点只读实例可能使用高可用的规格
        InstanceLevel instanceLevel = dep.getDbaasMetaClient().getDefaultmetaApi().getInstanceLevel(requestId,
                replicaSet.getService(), replicaSet.getServiceVersion(), targetClassCode, false);
        requestMeta.setCategory(readPrimaryReplicaSet.getCategory());
        requestMeta.setInstanceLevel(instanceLevel);

        // 设置单节点 xdb 标识
        requestMeta.setIsXdbSingle(ReplicaSetMetaHelper.isSingleNode(instanceLevel));

        // 避坑：临时实例的clusterID应该等于主实例的
        requestMeta.setXdbClusterId(LangUtil.getString(readPrimaryReplicaSet.getId()));

        // 避坑：集团实例的cluster-id记录在实例标签中
        if (StringUtils.isNotEmpty(labels.get("mycnfClusterId"))) {
            requestMeta.setXdbClusterId(labels.get("mycnfClusterId"));
        }
        requestMeta.reloadMatcher();
        requestMeta.setIsAliYun(ReplicaSetMetaHelper.isAliYun(replicaSet.getBizType()));

        // serverless 相关属性
        requestMeta.setIsServerless(false);
        if (ReplicaSetMetaHelper.isServerless(replicaSet)) {
            requestMeta.setIsServerless(true);

            if (rcu == null) {
                rcu = dep.getReplicaMetaHelper().getReplicaRcu(replicaSet.getName());
                log.info("set serverless rcu from {} meta: {}", replicaSet.getName(), rcu);
            }
        }
        requestMeta.setRcu(rcu);
        return requestMeta;
    }

    /**
     * 获取用户参数模板，用户参数模板一定与实例同region
     */
    private static ParamGroup getUserParamGroup(ParamDependency dep, ReplicaSet replicaSet, String paramGroupId) throws ApiException {
        ResourceGroup resourceGroup = dep.getDbaasMetaClient()
                .getDefaultmetaApi()
                .getResourceGroup(RequestSession.getRequestId(),
                        replicaSet.getResourceGroupName(), null);

        log.info("try to get user param group: region: {}, paramGroupId: {}", resourceGroup.getRegion(), paramGroupId);
        ParamGroup pg = dep.getDbaasMetaClient()
                .getDefaultmetaApi()
                .getParamGroups(RequestSession.getRequestId(),
                        resourceGroup.getRegion(),
                        paramGroupId, replicaSet.getService(), replicaSet.getServiceVersion(), false);
        if (pg == null) {
            log.warn("user param group not found: region: {}, paramGroupId: {}", resourceGroup.getRegion(), paramGroupId);
            return null;
        }

        // TODO: 参数模板用户ID与实例用户ID对比检查，防止越权
        return pg;
    }

    /**
     * 获取系统参数模板
     */
    private static ParamGroup getSystemParamGroup(ParamDependency dep, ReplicaSet replicaSet, String paramGroupId) throws ApiException {
        List<ParamGroup> pgs = Objects.requireNonNull(dep.getDbaasMetaClient()
                .getDefaultmetaApi().listParamGroups(
                        RequestSession.getRequestId(), null,
                        replicaSet.getService(),
                        replicaSet.getServiceVersion(),
                        PARAM_GROUP_TYPE_SYS, null, null, null)
                .getItems()).stream()
                .filter(p -> paramGroupId.equalsIgnoreCase(p.getParamGroupId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pgs)) {
            throw new CheckException("Cannot find paramGroup, id is " + paramGroupId);
        }
        return pgs.get(0);
    }

}
